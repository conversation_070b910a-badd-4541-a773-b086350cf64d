# 日常文档检查报告

**检查日期：** 2025/7/16
**检查时间：** 2025-07-16T05:37:38.906Z

## 检查结果摘要

| 检查项目 | 状态 | 说明 |
|---------|------|------|
| 链接检查 | ❌ 失败 | 存在无效链接 |
| 代码引用检查 | ❌ 失败 | 存在无效引用 |
| 格式检查 | ✅ 通过 | 格式规范 |

## 详细结果

### 链接检查
发现问题：
```
Command failed: npm run docs:check-links
node:fs:1507
  const result = binding.readdir(
                         ^

Error: ENOENT: no such file or directory, scandir '/Users/<USER>/nexus-panel/nexus-panel/docs'
    at Object.readdirSync (node:fs:1507:26)
    at getAllMarkdownFiles (/Users/<USER>/nexus-panel/scripts/validate-docs.js:9:20)
    at main (/Users/<USER>/nexus-panel/scripts/validate-docs.js:100:25)
    at Object.<anonymous> (/Users/<USER>/nexus-panel/scripts/validate-docs.js:146:3)
    at Module._compile (node:internal/modules/cjs/loader:1529:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)
    at Module.load (node:internal/modules/cjs/loader:1275:32)
    at Module._load (node:internal/modules/cjs/loader:1096:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)
    at node:internal/main/run_main_module:28:49 {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/Users/<USER>/nexus-panel/nexus-panel/docs'
}

Node.js v20.19.2

```

### 代码引用检查
发现问题：
```
Command failed: npm run docs:verify-code-refs
node:fs:1507
  const result = binding.readdir(
                         ^

Error: ENOENT: no such file or directory, scandir '/Users/<USER>/nexus-panel/nexus-panel/docs'
    at Object.readdirSync (node:fs:1507:26)
    at getAllMarkdownFiles (/Users/<USER>/nexus-panel/scripts/validate-docs.js:9:20)
    at main (/Users/<USER>/nexus-panel/scripts/validate-docs.js:100:25)
    at Object.<anonymous> (/Users/<USER>/nexus-panel/scripts/validate-docs.js:146:3)
    at Module._compile (node:internal/modules/cjs/loader:1529:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)
    at Module.load (node:internal/modules/cjs/loader:1275:32)
    at Module._load (node:internal/modules/cjs/loader:1096:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)
    at node:internal/main/run_main_module:28:49 {
  errno: -2,
  code: 'ENOENT',
  syscall: 'scandir',
  path: '/Users/<USER>/nexus-panel/nexus-panel/docs'
}

Node.js v20.19.2

```

### 格式检查
文档格式检查通过。

## 建议行动

- 🔗 修复无效链接
- 📝 更新代码引用

---
*此报告由文档维护调度器自动生成*
