{"timestamp": "2025-07-16T05:37:38.906Z", "type": "daily", "checks": {"links": {"status": "error", "error": "Command failed: npm run docs:check-links\nnode:fs:1507\n  const result = binding.readdir(\n                         ^\n\nError: ENOENT: no such file or directory, scandir '/Users/<USER>/nexus-panel/nexus-panel/docs'\n    at Object.readdirSync (node:fs:1507:26)\n    at getAllMarkdownFiles (/Users/<USER>/nexus-panel/scripts/validate-docs.js:9:20)\n    at main (/Users/<USER>/nexus-panel/scripts/validate-docs.js:100:25)\n    at Object.<anonymous> (/Users/<USER>/nexus-panel/scripts/validate-docs.js:146:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)\n    at node:internal/main/run_main_module:28:49 {\n  errno: -2,\n  code: 'ENOENT',\n  syscall: 'scandir',\n  path: '/Users/<USER>/nexus-panel/nexus-panel/docs'\n}\n\nNode.js v20.19.2\n"}, "codeRefs": {"status": "error", "error": "Command failed: npm run docs:verify-code-refs\nnode:fs:1507\n  const result = binding.readdir(\n                         ^\n\nError: ENOENT: no such file or directory, scandir '/Users/<USER>/nexus-panel/nexus-panel/docs'\n    at Object.readdirSync (node:fs:1507:26)\n    at getAllMarkdownFiles (/Users/<USER>/nexus-panel/scripts/validate-docs.js:9:20)\n    at main (/Users/<USER>/nexus-panel/scripts/validate-docs.js:100:25)\n    at Object.<anonymous> (/Users/<USER>/nexus-panel/scripts/validate-docs.js:146:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)\n    at node:internal/main/run_main_module:28:49 {\n  errno: -2,\n  code: 'ENOENT',\n  syscall: 'scandir',\n  path: '/Users/<USER>/nexus-panel/nexus-panel/docs'\n}\n\nNode.js v20.19.2\n"}, "format": {"status": "success", "output": "\n> nexus-panel@0.0.0 docs:lint\n> echo 'Markdown linting not configured yet'\n\nMarkdown linting not configured yet\n"}}}