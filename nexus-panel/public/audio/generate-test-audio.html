<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成测试音频文件</title>
</head>
<body>
    <h1>生成测试音频文件</h1>
    <p>点击下面的按钮生成一个测试音频文件：</p>
    <button onclick="generateTestAudio()">生成测试音频</button>
    <div id="status"></div>

    <script>
        function generateTestAudio() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '正在生成音频文件...';

            // 创建音频上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const sampleRate = audioContext.sampleRate;
            const duration = 10; // 10秒
            const frameCount = sampleRate * duration;

            // 创建音频缓冲区
            const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
            const channelData = audioBuffer.getChannelData(0);

            // 生成简单的音调序列（C大调音阶）
            const frequencies = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88, 523.25]; // C4-C5
            const noteLength = frameCount / frequencies.length;

            for (let i = 0; i < frameCount; i++) {
                const noteIndex = Math.floor(i / noteLength);
                const frequency = frequencies[noteIndex] || frequencies[0];
                const time = i / sampleRate;
                
                // 生成正弦波
                channelData[i] = Math.sin(2 * Math.PI * frequency * time) * 0.3;
                
                // 添加淡入淡出效果
                const noteTime = (i % noteLength) / noteLength;
                if (noteTime < 0.1) {
                    channelData[i] *= noteTime / 0.1; // 淡入
                } else if (noteTime > 0.9) {
                    channelData[i] *= (1 - noteTime) / 0.1; // 淡出
                }
            }

            // 将音频缓冲区转换为WAV格式
            const wavData = audioBufferToWav(audioBuffer);
            
            // 创建下载链接
            const blob = new Blob([wavData], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'sample-audio.wav';
            a.click();
            
            statusDiv.innerHTML = '音频文件已生成并下载！请将文件重命名为 sample-audio.mp3 并放置在 public/audio/ 目录下。';
        }

        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            const channels = buffer.numberOfChannels;
            const sampleRate = buffer.sampleRate;

            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, channels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);

            // 写入音频数据
            const channelData = buffer.getChannelData(0);
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }

            return arrayBuffer;
        }
    </script>
</body>
</html>
