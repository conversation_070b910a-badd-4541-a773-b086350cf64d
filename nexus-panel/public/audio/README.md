# 音频文件目录

## 概述

此目录用于存放AudioPlayer组件使用的音频文件。

## 支持的音频格式

- **MP3** - 推荐格式，兼容性最佳
- **WAV** - 高质量，文件较大  
- **OGG** - 开源格式，部分浏览器支持
- **AAC** - 高压缩比，移动端友好

## 测试音频文件

### 生成测试音频

1. 打开 `generate-test-audio.html` 文件
2. 点击"生成测试音频"按钮
3. 下载生成的WAV文件
4. 将文件重命名为 `sample-audio.mp3`
5. 放置在此目录下

### 在线测试音频

如果需要快速测试，可以使用以下在线音频文件：

```javascript
// 在AudioPlayer组件中使用在线音频进行测试
<AudioPlayer
  audioSrc="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"
  onPlay={() => console.log('测试音频开始播放')}
/>
```

## 文件命名规范

建议使用以下命名规范：

- `question-{id}.mp3` - 题目配音文件
- `sample-audio.mp3` - 示例音频文件
- `test-audio.mp3` - 测试音频文件

## 文件大小建议

- 单个音频文件建议不超过 5MB
- 音频时长建议不超过 5分钟
- 采样率建议使用 44.1kHz 或 48kHz
- 比特率建议使用 128kbps 或 192kbps

## 使用示例

```typescript
// 基础使用
<AudioPlayer audioSrc="/audio/question-1.mp3" />

// 完整配置
<AudioPlayer
  audioSrc="/audio/question-1.mp3"
  onPlay={() => console.log('开始播放')}
  onPause={() => console.log('暂停播放')}
  onEnded={() => console.log('播放结束')}
  onTimeUpdate={(currentTime, duration) => {
    console.log(`进度: ${currentTime}/${duration}`);
  }}
  isDisabled={false}
  isLoading={false}
/>
```

## 故障排除

### 音频无法播放

1. 检查文件路径是否正确
2. 确认音频文件格式受支持
3. 检查浏览器控制台是否有错误信息
4. 确认网络连接正常

### 音频加载缓慢

1. 优化音频文件大小
2. 使用适当的压缩格式
3. 考虑使用CDN加速

### 浏览器兼容性问题

1. 使用MP3格式以获得最佳兼容性
2. 提供多种格式的备选方案
3. 检查浏览器版本支持情况
