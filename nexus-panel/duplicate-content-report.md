# 重复内容检测报告

**生成时间**: 2025-07-16T04:16:26.398Z

## 📊 检测摘要

- **完全重复**: 0 对
- **相似内容**: 1 对
- **重复标题**: 135 个
- **重复代码块**: 24 个

## 🔍 完全重复文件

未发现完全重复的文件。

## 📝 相似内容文件


### SIMPLE_UPDATE_LOG.md ↔ UPDATE_LOG_v0.2.md
- **平均相似度**: 87%
- **Jaccard 相似度**: 89%
- **余弦相似度**: 97%
- **编辑距离相似度**: 74%
- **原因**: High word overlap, Similar word patterns
- **建议**: 考虑合并相似内容或明确区分用途


## 📑 重复标题


### "问题描述"
- **出现次数**: 4
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H2), MEMORY_LEAK_FIX.md (H2), RACE_API_SINGLETON_ARCHITECTURE.md (H3), REALTIME_LOG_COUNT_FIX.md (H2)


### "解决方案"
- **出现次数**: 4
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H2), MEMORY_STATS_EXPLANATION.md (H3), REALTIME_LOG_COUNT_FIX.md (H2), TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md (H2)


### "监控建议"
- **出现次数**: 2
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H3), MEMORY_STATS_EXPLANATION.md (H2)


### "修复前的问题架构"
- **出现次数**: 2
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H4), SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md (H3)


### "修复后的优化架构"
- **出现次数**: 2
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H4), SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md (H3)


### "🐛 调试信息"
- **出现次数**: 2
- **文件**: API_DUPLICATE_REQUESTS_ANALYSIS.md (H3), HOOK_FIXES_SUMMARY.md (H2)


### "目录"
- **出现次数**: 9
- **文件**: API_ENDPOINTS_ANALYSIS.md (H2), API_GUIDE.md (H2), ARCHITECTURE.md (H2), COMPONENTS_GUIDE.md (H2), DEVELOPMENT_GUIDE.md (H2), DOCUMENTATION_MAINTENANCE_GUIDE.md (H2), MQTT_GUIDE.md (H2), PERFORMANCE_OPTIMIZATION.md (H2), TROUBLESHOOTING.md (H2)


### "核心设计原则"
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (H3), API_GUIDE.md (H3)


### "基础配置"
- **出现次数**: 4
- **文件**: API_ENDPOINTS_ANALYSIS.md (H3), API_ENDPOINTS_ANALYSIS.md (H3), API_GUIDE.md (H3), API_GUIDE.md (H3)


### "1. 赛事管理 api"
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (H3), API_GUIDE.md (H4)



*显示前 10 个，总共 135 个重复标题*

## 💻 重复代码块


### 代码块 -2c262b2d
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (unknown), API_GUIDE.md (unknown)
- **预览**: `┌─────────────────────────────────────────────────────────────┐
│                    前端应用层 (React)  ...`


### 代码块 1571b4a1
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (http), API_ENDPOINTS_ANALYSIS.md (http)
- **预览**: `GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records
...`


### 代码块 174626b3
- **出现次数**: 4
- **文件**: API_ENDPOINTS_ANALYSIS.md (unknown), API_GUIDE.md (unknown), MQTT_DEVELOPER_GUIDE.md (unknown), MQTT_GUIDE.md (unknown)
- **预览**: `{domain}/{context}/{target}/{action}
...`


### 代码块 -4215f19c
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (typescript), API_GUIDE.md (typescript)
- **预览**: `class MQTTService {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  publish(topic: stri...`


### 代码块 -4e3bb0ec
- **出现次数**: 2
- **文件**: API_ENDPOINTS_ANALYSIS.md (mermaid), API_GUIDE.md (mermaid)
- **预览**: `graph TD
    A[前端应用] --> B[Hook 层]
    B --> C[服务层]
    C --> D[HTTP Client]
    C --> E[MQTT Servic...`



*显示前 5 个，总共 24 个重复代码块*

## 🎯 建议行动

### 立即处理
1. ✅ 无完全重复文件需要处理

### 考虑合并
2. **合并高度相似内容**: 1 对文件相似度超过 85%

### 标准化处理
3. **统一重复标题**: 135 个标题在多个文件中重复

### 代码复用优化
4. **优化重复代码**: 24 个代码块重复使用

---
*此报告由重复内容检测脚本自动生成*
