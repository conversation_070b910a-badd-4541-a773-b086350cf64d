/**
 * UltimatePK 相关类型定义
 * 
 * 此文件包含 UltimatePK 功能的所有类型定义，避免重复声明
 */

/**
 * 阶段枚举
 */
export const PKStage = {
	VIEWPOINT: '观点陈述',
	INQUIRY: '双方质询',
	FREE_DEBATE: '自由辩论',
	SUMMARY: '总结陈词'
} as const;

export type PKStage = typeof PKStage[keyof typeof PKStage];

/**
 * 选手方向
 */
export const PKSide = {
	POSITIVE: 'positive',
	NEGATIVE: 'negative'
} as const;

export type PKSide = typeof PKSide[keyof typeof PKSide];

/**
 * UltimatePK 控制接口类型定义
 */
export interface UltimatePKControls {
	// 阶段切换
	setStage: (stage: PKStage) => void;
	// 正方控制
	startPositiveTimer: () => void;
	pausePositiveTimer: () => void;
	resetPositiveTimer: () => void;
	// 反方控制
	startNegativeTimer: () => void;
	pauseNegativeTimer: () => void;
	resetNegativeTimer: () => void;
	// 全局控制
	pauseAllTimers: () => void;
	switchTimer: () => void;
	playShortBell: () => void;
	playLongBell: () => void;
	// 状态查询
	getCurrentStage: () => PKStage;
	isFreeDabateStage: () => boolean;
	// 按钮禁用状态查询
	shouldDisablePositiveControls: () => boolean;
	shouldDisableNegativeControls: () => boolean;
	// 触发按钮组重新渲染的方法
	triggerButtonGroupUpdate: () => void;
}

/**
 * 扩展 Window 接口以包含 ultimatePKControls
 */
declare global {
	interface Window {
		ultimatePKControls?: UltimatePKControls;
	}
}
