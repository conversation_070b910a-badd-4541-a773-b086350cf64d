/**
 * Motion for React 相关类型定义
 * 
 * 提供Motion动画相关的TypeScript类型定义和接口扩展
 */

import type { HTMLMotionProps, Variants, Transition } from "motion/react";

// ==================== 基础动画类型 ====================

/**
 * 动画状态枚举
 */
export type AnimationState = 'hidden' | 'visible' | 'exit';

/**
 * 动画方向枚举
 */
export type AnimationDirection = 'up' | 'down' | 'left' | 'right';

/**
 * 动画类型枚举
 */
export type AnimationType = 'fade' | 'slide' | 'scale' | 'rotate';

// ==================== 组件动画Props ====================

/**
 * 基础动画Props接口
 */
export interface BaseAnimationProps {
  /** 是否启用动画，默认为true */
  animated?: boolean;
  /** 动画持续时间，覆盖默认配置 */
  duration?: number;
  /** 动画延迟时间 */
  delay?: number;
  /** 自定义动画变体 */
  variants?: Variants;
  /** 自定义过渡配置 */
  transition?: Transition;
  /** 是否减少动画（无障碍访问） */
  reduceMotion?: boolean;
}

/**
 * 进入/退出动画Props接口
 */
export interface EnterExitAnimationProps extends BaseAnimationProps {
  /** 初始状态 */
  initial?: AnimationState | object;
  /** 动画状态 */
  animate?: AnimationState | object;
  /** 退出状态 */
  exit?: AnimationState | object;
}

/**
 * 布局动画Props接口
 */
export interface LayoutAnimationProps extends BaseAnimationProps {
  /** 是否启用布局动画 */
  layout?: boolean | "position" | "size";
  /** 布局动画ID */
  layoutId?: string;
  /** 布局依赖项 - 当这些值改变时会触发布局动画重新计算 */
  layoutDependency?: unknown | unknown[];
}

// ==================== 组件特定动画接口 ====================

/**
 * 控制台面板动画Props
 */
export interface ConsolePanelAnimationProps extends BaseAnimationProps {
  /** 是否展开 */
  isExpanded?: boolean;
  /** 面板高度 */
  height?: number;
  /** 最小高度 */
  minHeight?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 日志条目动画配置 */
  logEntryAnimation?: {
    enabled: boolean;
    staggerDelay: number;
  };
}

/**
 * 侧边栏按钮组动画Props
 */
export interface SidebarAnimationProps extends BaseAnimationProps {
  /** 按钮组切换动画 */
  groupSwitchAnimation?: {
    enabled: boolean;
    direction: AnimationDirection;
  };
  /** 按钮错开动画 */
  buttonStaggerAnimation?: {
    enabled: boolean;
    staggerDelay: number;
  };
}

/**
 * 导航树动画Props
 */
export interface NavigationTreeAnimationProps extends BaseAnimationProps {
  /** 展开/折叠动画 */
  expandAnimation?: {
    enabled: boolean;
    direction: 'vertical' | 'horizontal';
  };
  /** 节点高亮动画 */
  highlightAnimation?: {
    enabled: boolean;
    color: string;
  };
}

// ==================== Motion组件扩展类型 ====================

/**
 * 扩展的Motion Div Props
 */
export interface MotionDivProps extends HTMLMotionProps<"div">, BaseAnimationProps {
  /** 子组件 */
  children?: React.ReactNode;
  /** CSS类名 */
  className?: string;
  /** 内联样式 */
  style?: React.CSSProperties;
}

/**
 * 扩展的Motion容器Props
 */
export interface MotionContainerProps extends MotionDivProps {
  /** 容器类型 */
  containerType?: 'flex' | 'grid' | 'block';
  /** 是否为响应式容器 */
  responsive?: boolean;
}

// ==================== 动画配置类型 ====================

/**
 * 动画配置接口
 */
export interface AnimationConfig {
  /** 动画名称 */
  name: string;
  /** 动画变体 */
  variants: Variants;
  /** 默认过渡 */
  defaultTransition: Transition;
  /** 是否支持减少动画 */
  supportsReducedMotion: boolean;
}

/**
 * 错开动画配置
 */
export interface StaggerAnimationConfig {
  /** 错开延迟 */
  staggerDelay: number;
  /** 子元素延迟 */
  delayChildren?: number;
  /** 错开方向 */
  staggerDirection?: 1 | -1;
}

/**
 * 高度动画配置
 */
export interface HeightAnimationConfig {
  /** 起始高度 */
  from: number | string;
  /** 目标高度 */
  to: number | string;
  /** 动画持续时间 */
  duration: number;
  /** 缓动函数 */
  ease: string;
}

// ==================== 动画事件类型 ====================

/**
 * 动画事件回调类型
 */
export interface AnimationCallbacks {
  /** 动画开始回调 */
  onAnimationStart?: () => void;
  /** 动画完成回调 */
  onAnimationComplete?: () => void;
  /** 动画更新回调 */
  onAnimationUpdate?: (latest: Record<string, number | string>) => void;
}

/**
 * 动画生命周期事件
 */
export interface AnimationLifecycleEvents extends AnimationCallbacks {
  /** 进入动画开始 */
  onEnterStart?: () => void;
  /** 进入动画完成 */
  onEnterComplete?: () => void;
  /** 退出动画开始 */
  onExitStart?: () => void;
  /** 退出动画完成 */
  onExitComplete?: () => void;
}

// ==================== 工具类型 ====================

/**
 * 动画状态管理类型
 */
export interface AnimationStateManager {
  /** 当前动画状态 */
  currentState: AnimationState;
  /** 设置动画状态 */
  setState: (state: AnimationState) => void;
  /** 是否正在动画中 */
  isAnimating: boolean;
  /** 重置动画状态 */
  reset: () => void;
}

/**
 * 动画性能监控类型
 */
export interface AnimationPerformanceMetrics {
  /** 动画帧率 */
  fps: number;
  /** 动画持续时间 */
  duration: number;
  /** 是否掉帧 */
  hasDroppedFrames: boolean;
  /** 内存使用情况 */
  memoryUsage: number;
}

// ==================== 导出所有类型 ====================

export type {
  HTMLMotionProps,
  Variants,
  Transition
} from "motion/react";
