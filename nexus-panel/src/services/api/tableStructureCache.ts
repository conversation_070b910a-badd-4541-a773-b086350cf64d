/**
 * 全局表结构缓存管理器
 * 
 * 用于在整个应用中统一管理表结构数据的缓存，避免重复API请求
 */

import type { TableStructureResponse } from './types';

// 全局缓存存储
const globalTableStructureCache = new Map<string, TableStructureResponse>();

// 正在进行的请求缓存，防止并发重复请求
const pendingRequests = new Map<string, Promise<TableStructureResponse>>();

// 缓存过期时间（5分钟）
const CACHE_TTL = 5 * 60 * 1000;

// 缓存时间戳记录
const cacheTimestamps = new Map<string, number>();

// 请求计数器，用于调试
const requestCounters = new Map<string, number>();

/**
 * 全局表结构缓存管理器
 */
export class GlobalTableStructureCache {
    /**
     * 获取表结构数据（带缓存）
     * @param baseId 项目基础ID
     * @param fetchFunction 获取数据的函数
     * @returns 表结构数据
     */
    static async getWithCache(
        baseId: string,
        fetchFunction: (baseId: string) => Promise<TableStructureResponse>
    ): Promise<TableStructureResponse> {
        // 更新请求计数
        const currentCount = (requestCounters.get(baseId) || 0) + 1;
        requestCounters.set(baseId, currentCount);

        console.log(`[GlobalTableStructureCache] 🔍 请求表结构数据 (请求#${currentCount})`, {
            baseId,
            hasPendingRequest: pendingRequests.has(baseId),
            hasCachedData: globalTableStructureCache.has(baseId),
            cacheSize: globalTableStructureCache.size,
            pendingRequestsCount: pendingRequests.size,
            allCacheKeys: Array.from(globalTableStructureCache.keys()),
            allPendingKeys: Array.from(pendingRequests.keys()),
            requestCount: currentCount,
            timestamp: Date.now(),
            action: 'global_cache_request_start'
        });

        // 检查是否有正在进行的请求
        if (pendingRequests.has(baseId)) {
            console.log(`[GlobalTableStructureCache] ⏳ 检测到正在进行的表结构请求，等待结果 (请求#${currentCount})`, {
                baseId,
                requestCount: currentCount,
                timestamp: Date.now(),
                action: 'global_cache_request_deduplication'
            });
            return pendingRequests.get(baseId)!;
        }

        // 检查缓存是否有效
        const cachedData = globalTableStructureCache.get(baseId);
        const cacheTime = cacheTimestamps.get(baseId);

        if (cachedData && cacheTime && (Date.now() - cacheTime) < CACHE_TTL) {
            console.log(`[GlobalTableStructureCache] ✅ 全局缓存命中 (请求#${currentCount})`, {
                baseId,
                cacheAge: Date.now() - cacheTime,
                tablesCount: cachedData.list.length,
                requestCount: currentCount,
                timestamp: Date.now(),
                action: 'global_cache_hit'
            });
            return cachedData;
        }

        // 创建请求Promise并缓存
        const requestPromise = (async (): Promise<TableStructureResponse> => {
            try {
                console.log(`[GlobalTableStructureCache] 🚀 全局缓存未命中，发起API请求 (请求#${currentCount})`, {
                    baseId,
                    endpoint: `https://noco.ohvfx.com/api/v2/meta/bases/${baseId}/tables`,
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'global_cache_miss'
                });

                const result = await fetchFunction(baseId);

                // 存储到全局缓存
                globalTableStructureCache.set(baseId, result);
                cacheTimestamps.set(baseId, Date.now());

                console.log(`[GlobalTableStructureCache] ✅ 数据已存储到全局缓存 (请求#${currentCount})`, {
                    baseId,
                    tablesCount: result.list.length,
                    availableTables: result.list.map(t => t.title),
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'global_cache_stored'
                });

                return result;
            } catch (error) {
                console.error(`[GlobalTableStructureCache] ❌ 全局缓存请求失败 (请求#${currentCount})`, {
                    baseId,
                    error: error instanceof Error ? error.message : String(error),
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'global_cache_error'
                });
                throw error;
            } finally {
                // 请求完成后清理pending状态
                pendingRequests.delete(baseId);
                console.log(`[GlobalTableStructureCache] 🧹 清理pending请求 (请求#${currentCount})`, {
                    baseId,
                    remainingPendingRequests: pendingRequests.size,
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'global_cache_pending_cleanup'
                });
            }
        })();

        // 缓存正在进行的请求
        pendingRequests.set(baseId, requestPromise);

        return requestPromise;
    }

    /**
     * 预加载表结构数据
     * @param baseId 项目基础ID
     * @param fetchFunction 获取数据的函数
     */
    static preloadTableStructure(
        baseId: string,
        fetchFunction: (baseId: string) => Promise<TableStructureResponse>
    ): void {
        // 如果已经有缓存或正在请求，则跳过
        if (globalTableStructureCache.has(baseId) || pendingRequests.has(baseId)) {
            console.log(`[GlobalTableStructureCache] ⏭️ 跳过预加载，数据已存在或正在请求中`, {
                baseId,
                hasCachedData: globalTableStructureCache.has(baseId),
                hasPendingRequest: pendingRequests.has(baseId),
                timestamp: Date.now(),
                action: 'global_cache_preload_skip'
            });
            return;
        }

        console.log(`[GlobalTableStructureCache] 🔄 开始预加载表结构数据`, {
            baseId,
            timestamp: Date.now(),
            action: 'global_cache_preload_start'
        });

        // 异步获取数据，不等待结果
        this.getWithCache(baseId, fetchFunction).catch(error => {
            console.error(`[GlobalTableStructureCache] ❌ 预加载失败`, {
                baseId,
                error: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                action: 'global_cache_preload_error'
            });
        });
    }

    /**
     * 清理缓存
     * @param baseId 可选的特定项目ID，不提供则清理所有缓存
     */
    static clearCache(baseId?: string): void {
        if (baseId) {
            globalTableStructureCache.delete(baseId);
            cacheTimestamps.delete(baseId);
            pendingRequests.delete(baseId);
            console.log(`[GlobalTableStructureCache] 🧹 已清理特定项目缓存`, {
                baseId,
                timestamp: Date.now(),
                action: 'global_cache_clear_specific'
            });
        } else {
            globalTableStructureCache.clear();
            cacheTimestamps.clear();
            pendingRequests.clear();
            requestCounters.clear();
            console.log(`[GlobalTableStructureCache] 🧹 已清理所有全局缓存`, {
                timestamp: Date.now(),
                action: 'global_cache_clear_all'
            });
        }
    }

    /**
     * 获取缓存统计信息
     */
    static getCacheStats() {
        return {
            cacheSize: globalTableStructureCache.size,
            pendingRequests: pendingRequests.size,
            cacheKeys: Array.from(globalTableStructureCache.keys()),
            pendingKeys: Array.from(pendingRequests.keys()),
            requestCounts: Object.fromEntries(requestCounters.entries()),
            timestamp: Date.now()
        };
    }
}

// 定期清理过期缓存
setInterval(() => {
    const now = Date.now();
    for (const [baseId, timestamp] of cacheTimestamps.entries()) {
        if (now - timestamp > CACHE_TTL) {
            globalTableStructureCache.delete(baseId);
            cacheTimestamps.delete(baseId);
        }
    }
}, CACHE_TTL); // 每5分钟清理一次