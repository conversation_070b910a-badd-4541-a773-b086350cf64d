/**
 * HTTP 客户端配置和基础请求方法
 * 
 * 提供统一的 HTTP 请求接口，包括错误处理、超时控制和响应格式化
 */

import type { ApiConfig, ApiResponse, RequestOptions } from './types';
import { HttpMethod } from './types';
import { ApiErrorHandler } from './errors';

// ==================== 默认配置 ====================

/**
 * 默认 API 配置
 */
const DEFAULT_CONFIG: ApiConfig = {
	baseUrl: '',
	defaultHeaders: {
		'Content-Type': 'application/json',
	},
	timeout: 10000, // 10秒超时
};

// ==================== HTTP 客户端类 ====================

/**
 * HTTP 客户端类
 * 提供统一的 HTTP 请求接口
 */
export class HttpClient {
	private config: ApiConfig;

	constructor(config: Partial<ApiConfig> = {}) {
		this.config = { ...DEFAULT_CONFIG, ...config };
	}

	/**
	 * 更新客户端配置
	 */
	updateConfig(config: Partial<ApiConfig>): void {
		this.config = { ...this.config, ...config };
	}

	/**
	 * 获取当前配置
	 */
	getConfig(): ApiConfig {
		return { ...this.config };
	}

	/**
	 * 构建完整的请求 URL
	 */
	private buildUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
		const url = new URL(endpoint, this.config.baseUrl || window.location.origin);

		if (params) {
			Object.entries(params).forEach(([key, value]) => {
				url.searchParams.append(key, String(value));
			});
		}

		return url.toString();
	}

	/**
	 * 构建请求头
	 */
	private buildHeaders(customHeaders?: Record<string, string>): Record<string, string> {
		return {
			...this.config.defaultHeaders,
			...customHeaders,
		};
	}

	/**
	 * 执行 HTTP 请求
	 */
	async request<T = unknown>(
		endpoint: string,
		options: RequestOptions = {}
	): Promise<ApiResponse<T>> {
		const {
			method = HttpMethod.GET,
			headers: customHeaders,
			body,
			params,
			timeout = this.config.timeout,
		} = options;

		// 构建请求 URL 和头部
		const url = this.buildUrl(endpoint, params);
		const headers = this.buildHeaders(customHeaders);

		// 创建 AbortController 用于超时控制
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), timeout);

		try {
			// 构建 fetch 选项
			const fetchOptions: RequestInit = {
				method,
				headers,
				signal: controller.signal,
			};

			// 添加请求体（仅对非 GET 请求）
			if (body && method !== HttpMethod.GET) {
				fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
			}

			// 执行请求
			const response = await fetch(url, fetchOptions);

			// 清除超时定时器
			clearTimeout(timeoutId);

			// 检查响应状态
			if (!response.ok) {
				let responseData;
				try {
					responseData = await response.json();
				} catch {
					responseData = await response.text();
				}

				throw ApiErrorHandler.createHttpError(
					response.status,
					response.statusText,
					responseData
				);
			}

			// 解析响应数据
			let data: T;
			const contentType = response.headers.get('content-type');

			if (contentType && contentType.includes('application/json')) {
				data = await response.json();
			} else {
				data = (await response.text()) as unknown as T;
			}

			// 构建响应对象
			const apiResponse: ApiResponse<T> = {
				data,
				status: response.status,
				headers: Object.fromEntries(response.headers.entries()),
			};

			return apiResponse;

		} catch (error) {
			// 清除超时定时器
			clearTimeout(timeoutId);

			// 处理错误
			const apiError = ApiErrorHandler.createApiError(error);
			throw apiError;
		}
	}

	/**
	 * GET 请求
	 */
	async get<T = unknown>(
		endpoint: string,
		params?: Record<string, string | number | boolean>,
		options?: Omit<RequestOptions, 'method' | 'params'>
	): Promise<ApiResponse<T>> {
		return this.request<T>(endpoint, {
			...options,
			method: HttpMethod.GET,
			params,
		});
	}

	/**
	 * POST 请求
	 */
	async post<T = unknown>(
		endpoint: string,
		body?: Record<string, unknown> | FormData | string | null,
		options?: Omit<RequestOptions, 'method' | 'body'>
	): Promise<ApiResponse<T>> {
		return this.request<T>(endpoint, {
			...options,
			method: HttpMethod.POST,
			body,
		});
	}

	/**
	 * PUT 请求
	 */
	async put<T = unknown>(
		endpoint: string,
		body?: Record<string, unknown> | FormData | string | null,
		options?: Omit<RequestOptions, 'method' | 'body'>
	): Promise<ApiResponse<T>> {
		return this.request<T>(endpoint, {
			...options,
			method: HttpMethod.PUT,
			body,
		});
	}

	/**
	 * DELETE 请求
	 */
	async delete<T = unknown>(
		endpoint: string,
		options?: Omit<RequestOptions, 'method'>
	): Promise<ApiResponse<T>> {
		return this.request<T>(endpoint, {
			...options,
			method: HttpMethod.DELETE,
		});
	}

	/**
	 * PATCH 请求
	 */
	async patch<T = unknown>(
		endpoint: string,
		body?: Record<string, unknown> | FormData | string | null,
		options?: Omit<RequestOptions, 'method' | 'body'>
	): Promise<ApiResponse<T>> {
		return this.request<T>(endpoint, {
			...options,
			method: HttpMethod.PATCH,
			body,
		});
	}
}

// ==================== 默认客户端实例 ====================

/**
 * 默认 HTTP 客户端实例
 */
export const httpClient = new HttpClient();

/**
 * 配置默认客户端
 */
export function configureHttpClient(config: Partial<ApiConfig>): void {
	httpClient.updateConfig(config);
}

// ==================== 便捷函数 ====================

/**
 * 快速 GET 请求
 */
export const get = httpClient.get.bind(httpClient);

/**
 * 快速 POST 请求
 */
export const post = httpClient.post.bind(httpClient);

/**
 * 快速 PUT 请求
 */
export const put = httpClient.put.bind(httpClient);

/**
 * 快速 DELETE 请求
 */
export const del = httpClient.delete.bind(httpClient);

/**
 * 快速 PATCH 请求
 */
export const patch = httpClient.patch.bind(httpClient);
