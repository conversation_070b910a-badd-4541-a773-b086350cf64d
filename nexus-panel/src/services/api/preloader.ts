/**
 * API数据预加载器
 * 
 * 在应用启动时预加载关键数据，避免后续的重复请求
 */

import { GlobalTableStructureCache } from './tableStructureCache';

/**
 * 预加载管理器
 */
export class ApiPreloader {
    private static preloadedProjects = new Set<string>();

    /**
     * 预加载项目的表结构数据
     * @param baseId 项目基础ID
     */
    static async preloadProjectData(baseId: string): Promise<void> {
        if (!baseId || baseId === 'default' || this.preloadedProjects.has(baseId)) {
            return;
        }

        console.log(`[ApiPreloader] 🚀 开始预加载项目数据`, {
            baseId,
            timestamp: Date.now(),
            action: 'preload_project_start'
        });

        try {
            // 预加载表结构数据
            GlobalTableStructureCache.preloadTableStructure(baseId, async (id) => {
                const { getTableStructure } = await import('./navigationApi');
                const response = await getTableStructure(id);
                return response.data;
            });

            this.preloadedProjects.add(baseId);

            console.log(`[ApiPreloader] ✅ 项目数据预加载完成`, {
                baseId,
                timestamp: Date.now(),
                action: 'preload_project_success'
            });

        } catch (error) {
            console.error(`[ApiPreloader] ❌ 项目数据预加载失败`, {
                baseId,
                error: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                action: 'preload_project_error'
            });
        }
    }

    /**
     * 批量预加载多个项目
     * @param baseIds 项目ID列表
     */
    static async preloadMultipleProjects(baseIds: string[]): Promise<void> {
        const validBaseIds = baseIds.filter(id => id && id !== 'default');

        if (validBaseIds.length === 0) {
            return;
        }

        console.log(`[ApiPreloader] 🔄 批量预加载项目数据`, {
            baseIds: validBaseIds,
            count: validBaseIds.length,
            timestamp: Date.now(),
            action: 'preload_multiple_start'
        });

        // 并行预加载，但限制并发数量
        const concurrency = 3; // 最多同时预加载3个项目
        for (let i = 0; i < validBaseIds.length; i += concurrency) {
            const batch = validBaseIds.slice(i, i + concurrency);
            await Promise.allSettled(
                batch.map(baseId => this.preloadProjectData(baseId))
            );
        }

        console.log(`[ApiPreloader] ✅ 批量预加载完成`, {
            baseIds: validBaseIds,
            count: validBaseIds.length,
            timestamp: Date.now(),
            action: 'preload_multiple_success'
        });
    }

    /**
     * 获取预加载统计信息
     */
    static getStats() {
        return {
            preloadedProjects: Array.from(this.preloadedProjects),
            preloadedCount: this.preloadedProjects.size,
            cacheStats: GlobalTableStructureCache.getCacheStats(),
            timestamp: Date.now()
        };
    }

    /**
     * 清理预加载记录
     */
    static clearPreloadedProjects(): void {
        this.preloadedProjects.clear();
        console.log(`[ApiPreloader] 🧹 已清理预加载记录`, {
            timestamp: Date.now(),
            action: 'preload_clear'
        });
    }
}