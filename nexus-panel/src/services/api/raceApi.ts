/**
 * 赛事相关 API 服务
 * 
 * 提供赛事数据的获取、处理和转换功能
 */

import { httpClient } from './client';
import type { RaceApiResponse, RaceApiItem, ProcessedRaceItem, ApiResponse } from './types';
import { ApiErrorHandler, createDataFormatError } from './errors';

// ==================== API 配置 ====================

/**
 * 赛事 API 配置
 */
const RACE_API_CONFIG = {
	/** 赛事列表接口地址 */
	endpoint: 'https://noco.ohvfx.com/api/v2/tables/m19dww1xfzsfipk/records',
	/** 视图 ID */
	viewId: 'vwoimmnq6pws8pso',
	/** 认证 Token */
	token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
} as const;

// ==================== 数据处理函数 ====================

/**
 * 验证原始赛事数据项的格式
 */
function validateRaceApiItem(item: unknown): item is RaceApiItem {
	return (
		typeof item === 'object' &&
		item !== null &&
		typeof (item as Record<string, unknown>).Id === 'number' &&
		typeof (item as Record<string, unknown>)['赛事 ID'] === 'string' &&
		typeof (item as Record<string, unknown>)['赛事名称'] === 'string' &&
		typeof (item as Record<string, unknown>)['是否显示'] === 'string'
	);
}

/**
 * 验证赛事 API 响应格式
 */
function validateRaceApiResponse(data: unknown): data is RaceApiResponse {
	return (
		typeof data === 'object' &&
		data !== null &&
		Array.isArray((data as Record<string, unknown>).list) &&
		typeof (data as Record<string, unknown>).pageInfo === 'object' &&
		(data as Record<string, unknown>).pageInfo !== null &&
		typeof ((data as Record<string, unknown>).pageInfo as Record<string, unknown>).totalRows === 'number'
	);
}

/**
 * 将原始赛事数据转换为处理后的格式
 */
function transformRaceItem(item: RaceApiItem): ProcessedRaceItem {
	return {
		id: item['赛事 ID'],
		name: item['赛事名称'],
		visible: item['是否显示'] === '显示',
		dbId: item.Id,
		peopleCount: item['参赛人数'] || 17, // 默认17个设备
	};
}

/**
 * 过滤可见的赛事项目
 */
function filterVisibleRaces(races: ProcessedRaceItem[]): ProcessedRaceItem[] {
	return races.filter(race => race.visible);
}

// ==================== API 服务类 ====================

/**
 * 赛事 API 服务类
 */
export class RaceApiService {
	/**
	 * 获取赛事列表（原始数据）
	 */
	static async getRaceListRaw(): Promise<ApiResponse<RaceApiResponse>> {
		try {
			// 构建请求参数
			const params = {
				viewId: RACE_API_CONFIG.viewId,
				limit: 500, // 设置返回记录数限制，避免数据被分页截断
			};

			// 构建请求头
			const headers = {
				'xc-token': RACE_API_CONFIG.token,
			};

			// 发起请求
			const response = await httpClient.get<RaceApiResponse>(
				RACE_API_CONFIG.endpoint,
				params,
				{ headers }
			);

			// 验证响应数据格式
			if (!validateRaceApiResponse(response.data)) {
				throw createDataFormatError('赛事 API 响应数据格式不正确');
			}

			// 验证列表中每个项目的格式
			for (const item of response.data.list) {
				if (!validateRaceApiItem(item)) {
					throw createDataFormatError(`赛事数据项格式不正确: ${JSON.stringify(item)}`);
				}
			}

			return response;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RaceApiService.getRaceListRaw');
			throw apiError;
		}
	}

	/**
	 * 获取处理后的赛事列表
	 */
	static async getRaceList(): Promise<ProcessedRaceItem[]> {
		try {
			// 获取原始数据
			const response = await this.getRaceListRaw();

			// 转换数据格式
			const processedRaces = response.data.list.map(transformRaceItem);

			return processedRaces;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RaceApiService.getRaceList');
			throw apiError;
		}
	}

	/**
	 * 获取可见的赛事列表
	 */
	static async getVisibleRaceList(): Promise<ProcessedRaceItem[]> {
		try {
			// 获取所有赛事
			const allRaces = await this.getRaceList();

			// 过滤可见的赛事
			const visibleRaces = filterVisibleRaces(allRaces);

			return visibleRaces;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RaceApiService.getVisibleRaceList');
			throw apiError;
		}
	}

	/**
	 * 根据赛事 ID 获取赛事信息
	 */
	static async getRaceById(raceId: string): Promise<ProcessedRaceItem | null> {
		try {
			// 获取所有赛事
			const allRaces = await this.getRaceList();

			// 查找指定 ID 的赛事
			const race = allRaces.find(race => race.id === raceId);

			return race || null;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RaceApiService.getRaceById');
			throw apiError;
		}
	}

	/**
	 * 检查赛事服务是否可用
	 */
	static async checkServiceHealth(): Promise<boolean> {
		try {
			// 尝试获取赛事列表
			await this.getRaceListRaw();
			return true;
		} catch (error) {
			// 记录错误但不抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RaceApiService.checkServiceHealth');
			return false;
		}
	}
}

// ==================== 便捷函数 ====================

/**
 * 获取可见的赛事列表（便捷函数）
 */
export const getVisibleRaces = RaceApiService.getVisibleRaceList.bind(RaceApiService);

/**
 * 获取所有赛事列表（便捷函数）
 */
export const getAllRaces = RaceApiService.getRaceList.bind(RaceApiService);

/**
 * 根据 ID 获取赛事（便捷函数）
 */
export const getRaceById = RaceApiService.getRaceById.bind(RaceApiService);

/**
 * 检查赛事服务健康状态（便捷函数）
 */
export const checkRaceServiceHealth = RaceApiService.checkServiceHealth.bind(RaceApiService);

// ==================== 类型导出 ====================

// 重新导出相关类型，方便外部使用
export type { RaceApiResponse, RaceApiItem, ProcessedRaceItem } from './types';
