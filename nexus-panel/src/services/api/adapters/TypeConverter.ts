/**
 * 类型转换工具
 * 
 * 处理NocoDB API返回数据的类型转换，特别是DECIMAL类型字段的处理
 */

import { isDecimalField, getFieldDefaultValue, STATUS_MAPPINGS } from './fieldMappings';

/**
 * 类型转换选项
 */
export interface TypeConversionOptions {
  /** 是否严格模式（转换失败时抛出错误） */
  strict?: boolean;
  /** 是否应用默认值 */
  applyDefaults?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 类型转换结果
 */
export interface ConversionResult<T = any> {
  /** 转换后的数据 */
  data: T;
  /** 转换统计信息 */
  stats: {
    totalFields: number;
    convertedFields: number;
    failedFields: number;
    defaultsApplied: number;
  };
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 支持的目标类型
 */
export type TargetType = 'string' | 'number' | 'boolean' | 'object' | 'array';

/**
 * 类型转换器类
 */
export class TypeConverter {
  /**
   * 转换DECIMAL类型字段
   */
  static convertDecimalFields<T = any>(
    data: Record<string, any>,
    tableName: string,
    options: TypeConversionOptions = {}
  ): ConversionResult<T> {
    const { strict = false, debug = false } = options;

    const result = { ...data };
    const stats = {
      totalFields: Object.keys(data).length,
      convertedFields: 0,
      failedFields: 0,
      defaultsApplied: 0
    };
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const [fieldName, value] of Object.entries(data)) {
      if (isDecimalField(tableName, fieldName)) {
        try {
          const convertedValue = this.convertToNumber(value, { strict, debug });
          if (convertedValue !== value) {
            result[fieldName] = convertedValue;
            stats.convertedFields++;

            if (debug) {
              console.log(`[TypeConverter] DECIMAL字段转换: ${fieldName}`, {
                original: value,
                converted: convertedValue,
                type: typeof convertedValue
              });
            }
          }
        } catch (error) {
          stats.failedFields++;
          const errorMsg = `DECIMAL字段 ${fieldName} 转换失败: ${error}`;

          if (strict) {
            errors.push(errorMsg);
          } else {
            warnings.push(errorMsg);
            // 非严格模式下使用默认值
            const defaultValue = getFieldDefaultValue(tableName, fieldName) || 0;
            result[fieldName] = defaultValue;
            stats.defaultsApplied++;

            if (debug) {
              console.warn(`[TypeConverter] 使用默认值: ${fieldName} = ${defaultValue}`);
            }
          }
        }
      }
    }

    return {
      data: result as T,
      stats,
      errors,
      warnings
    };
  }

  /**
   * 处理null值
   */
  static handleNullValues<T = any>(
    data: Record<string, any>,
    tableName: string,
    options: TypeConversionOptions = {}
  ): ConversionResult<T> {
    const { applyDefaults = true, debug = false } = options;

    const result = { ...data };
    const stats = {
      totalFields: Object.keys(data).length,
      convertedFields: 0,
      failedFields: 0,
      defaultsApplied: 0
    };
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const [fieldName, value] of Object.entries(data)) {
      if (value === null || value === undefined) {
        if (applyDefaults) {
          const defaultValue = getFieldDefaultValue(tableName, fieldName);
          if (defaultValue !== undefined) {
            result[fieldName] = defaultValue;
            stats.defaultsApplied++;
            stats.convertedFields++;

            if (debug) {
              console.log(`[TypeConverter] 应用默认值: ${fieldName} = ${defaultValue}`);
            }
          } else {
            warnings.push(`字段 ${fieldName} 为null且无默认值`);
          }
        }
      }
    }

    return {
      data: result as T,
      stats,
      errors,
      warnings
    };
  }

  /**
   * 验证数据类型
   */
  static validateTypes(
    data: Record<string, any>,
    typeSchema: Record<string, TargetType>
  ): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const [fieldName, expectedType] of Object.entries(typeSchema)) {
      const value = data[fieldName];
      const actualType = this.getValueType(value);

      if (actualType !== expectedType) {
        if (value === null || value === undefined) {
          warnings.push(`字段 ${fieldName} 为 ${actualType}，期望 ${expectedType}`);
        } else {
          errors.push(`字段 ${fieldName} 类型不匹配: 实际 ${actualType}，期望 ${expectedType}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 安全的类型转换
   */
  static safeConvert(
    value: any,
    targetType: TargetType,
    defaultValue?: any,
    options: { strict?: boolean; debug?: boolean } = {}
  ): any {
    const { strict = false, debug = false } = options;

    try {
      let convertedValue: any;

      switch (targetType) {
        case 'string':
          convertedValue = this.convertToString(value, options);
          break;
        case 'number':
          convertedValue = this.convertToNumber(value, options);
          break;
        case 'boolean':
          convertedValue = this.convertToBoolean(value, options);
          break;
        case 'object':
          convertedValue = this.convertToObject(value, options);
          break;
        case 'array':
          convertedValue = this.convertToArray(value, options);
          break;
        default:
          throw new Error(`不支持的目标类型: ${targetType}`);
      }

      if (debug) {
        console.log(`[TypeConverter] 类型转换成功:`, {
          original: value,
          originalType: typeof value,
          converted: convertedValue,
          targetType
        });
      }

      return convertedValue;

    } catch (error) {
      if (strict) {
        throw error;
      }

      if (debug) {
        console.warn(`[TypeConverter] 类型转换失败，使用默认值:`, {
          value,
          targetType,
          error: error instanceof Error ? error.message : String(error),
          defaultValue
        });
      }

      return defaultValue !== undefined ? defaultValue : this.getTypeDefaultValue(targetType);
    }
  }

  /**
   * 转换状态值
   */
  static convertStatusValues<T = any>(
    data: Record<string, any>,
    tableName: string,
    options: TypeConversionOptions = {}
  ): ConversionResult<T> {
    const { debug = false } = options;

    const result = { ...data };
    const stats = {
      totalFields: Object.keys(data).length,
      convertedFields: 0,
      failedFields: 0,
      defaultsApplied: 0
    };
    const errors: string[] = [];
    const warnings: string[] = [];

    // 处理答题记录表的状态字段
    if (tableName === 'answer_record' && 'status' in data) {
      const statusValue = data.status;
      if (typeof statusValue === 'string' && statusValue in STATUS_MAPPINGS.answer_record_status) {
        // 这里我们保持中文状态值，因为它们可能在UI中使用
        // 如果需要转换为英文，可以取消注释下面的代码
        // result.status = STATUS_MAPPINGS.answer_record_status[statusValue];
        // stats.convertedFields++;

        if (debug) {
          console.log(`[TypeConverter] 状态值验证通过: ${statusValue}`);
        }
      } else if (statusValue && debug) {
        warnings.push(`未知的状态值: ${statusValue}`);
      }
    }

    return {
      data: result as T,
      stats,
      errors,
      warnings
    };
  }

  /**
   * 批量类型转换
   */
  static convertBatch<T = any>(
    dataArray: Record<string, any>[],
    tableName: string,
    options: TypeConversionOptions = {}
  ): ConversionResult<T[]> {
    if (!Array.isArray(dataArray)) {
      throw new Error('输入数据必须是数组');
    }

    const results: T[] = [];
    const aggregatedStats = {
      totalFields: 0,
      convertedFields: 0,
      failedFields: 0,
      defaultsApplied: 0
    };
    const allErrors: string[] = [];
    const allWarnings: string[] = [];

    for (let i = 0; i < dataArray.length; i++) {
      const item = dataArray[i];

      // 先处理DECIMAL字段
      const decimalResult = this.convertDecimalFields(item, tableName, options);

      // 再处理null值
      const nullResult = this.handleNullValues(decimalResult.data, tableName, options);

      results.push(nullResult.data as T);

      // 聚合统计信息
      aggregatedStats.totalFields += decimalResult.stats.totalFields;
      aggregatedStats.convertedFields += decimalResult.stats.convertedFields + nullResult.stats.convertedFields;
      aggregatedStats.failedFields += decimalResult.stats.failedFields + nullResult.stats.failedFields;
      aggregatedStats.defaultsApplied += decimalResult.stats.defaultsApplied + nullResult.stats.defaultsApplied;

      // 收集错误和警告
      decimalResult.errors.forEach(error => allErrors.push(`[${i}] ${error}`));
      decimalResult.warnings.forEach(warning => allWarnings.push(`[${i}] ${warning}`));
      nullResult.errors.forEach(error => allErrors.push(`[${i}] ${error}`));
      nullResult.warnings.forEach(warning => allWarnings.push(`[${i}] ${warning}`));
    }

    return {
      data: results,
      stats: aggregatedStats,
      errors: allErrors,
      warnings: allWarnings
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 转换为字符串
   */
  private static convertToString(value: any, options: { strict?: boolean } = {}): string {
    if (value === null || value === undefined) {
      if (options.strict) {
        throw new Error('无法将null/undefined转换为字符串');
      }
      return '';
    }

    if (typeof value === 'string') {
      return value;
    }

    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch (error) {
        if (options.strict) {
          throw new Error(`对象转换为字符串失败: ${error}`);
        }
        return '[object Object]';
      }
    }

    return String(value);
  }

  /**
   * 转换为数字
   */
  private static convertToNumber(value: any, options: { strict?: boolean; debug?: boolean } = {}): number {
    if (value === null || value === undefined) {
      if (options.strict) {
        throw new Error('无法将null/undefined转换为数字');
      }
      return 0;
    }

    if (typeof value === 'number') {
      if (isNaN(value)) {
        if (options.strict) {
          throw new Error('数字值为NaN');
        }
        return 0;
      }
      return value;
    }

    if (typeof value === 'string') {
      const parsed = parseFloat(value.trim());
      if (isNaN(parsed)) {
        if (options.strict) {
          throw new Error(`字符串 "${value}" 无法转换为数字`);
        }
        return 0;
      }
      return parsed;
    }

    if (typeof value === 'boolean') {
      return value ? 1 : 0;
    }

    if (options.strict) {
      throw new Error(`类型 ${typeof value} 无法转换为数字`);
    }
    return 0;
  }

  /**
   * 转换为布尔值
   */
  private static convertToBoolean(value: any, options: { strict?: boolean } = {}): boolean {
    if (value === null || value === undefined) {
      return false;
    }

    if (typeof value === 'boolean') {
      return value;
    }

    if (typeof value === 'number') {
      return value !== 0;
    }

    if (typeof value === 'string') {
      const lower = value.toLowerCase().trim();
      if (['true', '1', 'yes', 'on'].includes(lower)) {
        return true;
      }
      if (['false', '0', 'no', 'off', ''].includes(lower)) {
        return false;
      }
      if (options.strict) {
        throw new Error(`字符串 "${value}" 无法转换为布尔值`);
      }
      return Boolean(value);
    }

    return Boolean(value);
  }

  /**
   * 转换为对象
   */
  private static convertToObject(value: any, options: { strict?: boolean } = {}): object {
    if (value === null || value === undefined) {
      return {};
    }

    if (typeof value === 'object' && !Array.isArray(value)) {
      return value;
    }

    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (typeof parsed === 'object' && !Array.isArray(parsed)) {
          return parsed;
        }
        if (options.strict) {
          throw new Error('解析结果不是对象');
        }
        return {};
      } catch (error) {
        if (options.strict) {
          throw new Error(`JSON解析失败: ${error}`);
        }
        return {};
      }
    }

    if (options.strict) {
      throw new Error(`类型 ${typeof value} 无法转换为对象`);
    }
    return {};
  }

  /**
   * 转换为数组
   */
  private static convertToArray(value: any, options: { strict?: boolean } = {}): any[] {
    if (value === null || value === undefined) {
      return [];
    }

    if (Array.isArray(value)) {
      return value;
    }

    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) {
          return parsed;
        }
        if (options.strict) {
          throw new Error('解析结果不是数组');
        }
        return [value];
      } catch (error) {
        if (options.strict) {
          throw new Error(`JSON解析失败: ${error}`);
        }
        return [value];
      }
    }

    if (options.strict) {
      throw new Error(`类型 ${typeof value} 无法转换为数组`);
    }
    return [value];
  }

  /**
   * 获取值的类型
   */
  private static getValueType(value: any): TargetType {
    if (value === null || value === undefined) {
      return 'object';
    }

    if (Array.isArray(value)) {
      return 'array';
    }

    const type = typeof value;
    return type as TargetType;
  }

  /**
   * 获取类型的默认值
   */
  private static getTypeDefaultValue(type: TargetType): any {
    switch (type) {
      case 'string':
        return '';
      case 'number':
        return 0;
      case 'boolean':
        return false;
      case 'object':
        return {};
      case 'array':
        return [];
      default:
        return null;
    }
  }
}
