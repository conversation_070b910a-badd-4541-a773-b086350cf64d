/**
 * 字段映射配置
 * 
 * 定义数据库schema字段名与NocoDB API实际字段名之间的映射关系
 */

/**
 * 字段映射配置接口
 */
export interface FieldMappingConfig {
  [tableName: string]: {
    fields: {
      [schemaFieldName: string]: string; // schema字段名 -> API字段名
    };
    decimalFields: string[]; // 需要类型转换的DECIMAL字段
    defaultValues: {
      [fieldName: string]: any; // 默认值配置
    };
    requiredFields: string[]; // 必需字段列表
  };
}

/**
 * 字段映射配置
 * 
 * 注意：这个配置需要根据API验证工具的结果进行调整
 * 当前使用的是基于现有代码分析的映射关系
 */
export const FIELD_MAPPINGS: FieldMappingConfig = {
  // 环节表映射 - schema字段名(英文) -> API字段名(中文)
  session: {
    fields: {
      'session_id': '环节ID',
      'session_name': '环节名称',
      'display_order': '显示顺序',
      'nav_type': '环节类型',
      'session_icon': '环节图标',
      'content_type': '内容类型',
      'initial_stage': '初始阶段',
      'session_config': '环节配置'
    },
    decimalFields: [],
    defaultValues: {
      'display_order': 0,
      'content_type': '规则',
      'initial_stage': '通用题'
    },
    requiredFields: ['session_id', 'session_name']
  },

  // 赛事素材表映射
  material: {
    fields: {
      'info_id': '信息ID',
      'title': '标题',
      'content': '内容',
      'attachment_url': '附件',
      'info_type': '信息类型'
    },
    decimalFields: [],
    defaultValues: {
      'content': ''
    },
    requiredFields: ['title', 'info_type']
  },

  // 题目表映射
  question: {
    fields: {
      'question_id': '题目ID',
      'question_number': '题号',
      'question_type': '题型',
      'prompt': '题干',
      'options': '选项',
      'correct_answer': '答案',
      'points': '分值',
      'explanation': '解析',
      'attachment_url': '附件',
      'session_id': '所属环节',
      'question_pack_id': '题包编号',
      'stage': '所属阶段'
    },
    decimalFields: ['points'],
    defaultValues: {
      'question_number': 0,
      'question_type': '未知题型',
      'points': 0,
      'prompt': '',
      'options': '',
      'correct_answer': '',
      'explanation': ''
    },
    requiredFields: ['session_id', 'question_pack_id']
  },

  // 选手表映射
  player: {
    fields: {
      'user_id': 'userId',
      'user_name': 'userName',
      'avatar_url': '头像',
      'revival_chances': '复活机会'
    },
    decimalFields: [],
    defaultValues: {
      'revival_chances': 0
    },
    requiredFields: ['user_id', 'user_name']
  },

  // 答题记录表映射
  answer_record: {
    fields: {
      'submitted_answer': '选手答案',
      'is_correct': '选手正误',
      'score': '分值',
      'user_id': '关联选手.userId',
      'question_id': '关联题目.Id',
      'question_number': '题号',
      'grading_type': '判分类型',
      'session_id': '所属环节',
      'status': '状态',
      'submission_type': '提交类型',
      'audit_details': '仲裁详情'
    },
    decimalFields: ['score'],
    defaultValues: {
      'is_correct': 0,
      'score': 0,
      'grading_type': '自动判分',
      'status': '有效',
      'submission_type': '常规提交'
    },
    requiredFields: ['user_id', 'question_id', 'session_id']
  },

  // 评委表映射
  judge: {
    fields: {
      'judge_id': '评委ID',
      'judge_name': '评委名称'
    },
    decimalFields: [],
    defaultValues: {},
    requiredFields: ['judge_id', 'judge_name']
  },

  // 评委评分表映射
  judge_score: {
    fields: {
      'judge_score': '评委分数',
      'user_id': '选手ID',
      'judge_id': '评委ID',
      'session_id': '环节ID'
    },
    decimalFields: ['judge_score'],
    defaultValues: {
      'judge_score': 0
    },
    requiredFields: ['user_id', 'judge_id', 'session_id']
  },

  // 投票记录表映射
  vote_record: {
    fields: {
      'voter_id': '投票人ID',
      'voter_type': '投票人类型',
      'user_id': '选手ID',
      'points': '投票分数'
    },
    decimalFields: ['points'],
    defaultValues: {
      'points': 0
    },
    requiredFields: ['voter_id', 'user_id']
  },

  // 日志记录表映射
  log_record: {
    fields: {
      'topic': '主题',
      'payload': '消息内容',
      'status': '处理状态'
    },
    decimalFields: [],
    defaultValues: {
      'status': 'pending'
    },
    requiredFields: ['topic']
  },

  // 最终得分表映射
  final_score: {
    fields: {
      'user_id': '选手ID',
      'user_name': '选手名称',
      'final_rank': '最终排名',
      'final_score': '最终得分',
      'score_details': '得分详情'
    },
    decimalFields: ['final_score'],
    defaultValues: {
      'final_rank': 0,
      'final_score': 0
    },
    requiredFields: ['user_id', 'user_name']
  },

  // 赛事表映射（基于现有代码）
  race: {
    fields: {
      'race_id': '赛事 ID',
      'race_name': '赛事名称',
      'is_visible': '是否显示',
      'participant_count': '参赛人数'
    },
    decimalFields: [],
    defaultValues: {
      'is_visible': '显示',
      'participant_count': 17
    },
    requiredFields: ['race_id', 'race_name']
  }
};

/**
 * 状态值映射配置
 */
export const STATUS_MAPPINGS = {
  answer_record_status: {
    '有效': 'valid',
    '作废': 'invalid',
    '题包作废': 'package_invalid',
    '作废中': 'invalidating',
    '题包作废中': 'package_invalidating'
  },

  submission_type: {
    '常规提交': 'normal',
    '主观放弃': 'subjective_abandon',
    '记分补录': 'score_supplement'
  },

  grading_type: {
    '自动判分': 'auto',
    '人工判分': 'manual'
  },

  voter_type: {
    '专家': 'expert',
    '大众': 'public'
  },

  log_status: {
    'success': '成功',
    'error': '错误',
    'warning': '警告'
  }
};

/**
 * 获取表的字段映射配置
 */
export function getTableMapping(tableName: string): FieldMappingConfig[string] | null {
  return FIELD_MAPPINGS[tableName] || null;
}

/**
 * 获取字段映射关系
 */
export function getFieldMapping(tableName: string, schemaField: string): string | null {
  const tableMapping = getTableMapping(tableName);
  return tableMapping?.fields[schemaField] || null;
}

/**
 * 获取反向字段映射关系（API字段名 -> schema字段名）
 */
export function getReverseFieldMapping(tableName: string, apiField: string): string | null {
  const tableMapping = getTableMapping(tableName);
  if (!tableMapping) return null;

  for (const [schemaField, mappedApiField] of Object.entries(tableMapping.fields)) {
    if (mappedApiField === apiField) {
      return schemaField;
    }
  }
  return null;
}

/**
 * 检查字段是否为DECIMAL类型
 */
export function isDecimalField(tableName: string, fieldName: string): boolean {
  const tableMapping = getTableMapping(tableName);
  return tableMapping?.decimalFields.includes(fieldName) || false;
}

/**
 * 获取字段默认值
 */
export function getFieldDefaultValue(tableName: string, fieldName: string): any {
  const tableMapping = getTableMapping(tableName);
  return tableMapping?.defaultValues[fieldName];
}

/**
 * 检查字段是否为必需字段
 */
export function isRequiredField(tableName: string, fieldName: string): boolean {
  const tableMapping = getTableMapping(tableName);
  return tableMapping?.requiredFields.includes(fieldName) || false;
}

/**
 * 获取所有支持的表名
 */
export function getSupportedTables(): string[] {
  return Object.keys(FIELD_MAPPINGS);
}
