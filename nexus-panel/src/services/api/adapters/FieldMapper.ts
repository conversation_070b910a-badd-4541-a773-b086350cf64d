/**
 * 字段映射适配器
 * 
 * 提供数据库schema字段名与NocoDB API字段名之间的双向转换功能
 */

import {
  FIELD_MAPPINGS,
  getTableMapping,
  getFieldMapping,
  getReverseFieldMapping,
  isDecimalField,
  getFieldDefaultValue
} from './fieldMappings';

/**
 * 字段映射方向枚举
 */
export enum MappingDirection {
  TO_API = 'toApi',      // schema字段名 -> API字段名
  FROM_API = 'fromApi'   // API字段名 -> schema字段名
}

/**
 * 字段映射选项
 */
export interface FieldMappingOptions {
  /** 是否保留未映射的字段 */
  keepUnmappedFields?: boolean;
  /** 是否应用默认值 */
  applyDefaults?: boolean;
  /** 是否进行类型转换 */
  convertTypes?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 字段映射结果
 */
export interface MappingResult<T = any> {
  /** 映射后的数据 */
  data: T;
  /** 映射统计信息 */
  stats: {
    totalFields: number;
    mappedFields: number;
    unmappedFields: number;
    convertedFields: number;
  };
  /** 警告信息 */
  warnings: string[];
}

/**
 * 字段映射适配器类
 */
export class FieldMapper {
  /**
   * 将schema字段名映射为API字段名（用于请求）
   */
  static mapToApiFields<T = any>(
    data: Record<string, any>,
    tableName: string,
    options: FieldMappingOptions = {}
  ): MappingResult<T> {
    return this.mapFields(data, tableName, MappingDirection.TO_API, options);
  }

  /**
   * 将API字段名映射为schema字段名（用于响应）
   */
  static mapFromApiFields<T = any>(
    data: Record<string, any>,
    tableName: string,
    options: FieldMappingOptions = {}
  ): MappingResult<T> {
    return this.mapFields(data, tableName, MappingDirection.FROM_API, options);
  }

  /**
   * 核心字段映射方法
   */
  private static mapFields<T = any>(
    data: Record<string, any>,
    tableName: string,
    direction: MappingDirection,
    options: FieldMappingOptions = {}
  ): MappingResult<T> {
    const {
      keepUnmappedFields = true,
      applyDefaults = false,
      convertTypes = true,
      debug = false
    } = options;

    const tableMapping = getTableMapping(tableName);
    if (!tableMapping) {
      if (debug) {
        console.warn(`[FieldMapper] 未找到表 ${tableName} 的映射配置`);
      }
      return {
        data: data as T,
        stats: {
          totalFields: Object.keys(data).length,
          mappedFields: 0,
          unmappedFields: Object.keys(data).length,
          convertedFields: 0
        },
        warnings: [`未找到表 ${tableName} 的映射配置`]
      };
    }

    const result: Record<string, any> = {};
    const stats = {
      totalFields: Object.keys(data).length,
      mappedFields: 0,
      unmappedFields: 0,
      convertedFields: 0
    };
    const warnings: string[] = [];

    // 处理每个字段
    for (const [originalField, value] of Object.entries(data)) {
      let mappedField: string | null = null;
      let finalValue = value;

      // 根据方向进行字段映射
      if (direction === MappingDirection.TO_API) {
        mappedField = getFieldMapping(tableName, originalField);
      } else {
        mappedField = getReverseFieldMapping(tableName, originalField);
      }

      if (mappedField) {
        // 字段映射成功
        stats.mappedFields++;

        // 类型转换
        if (convertTypes) {
          const targetField = direction === MappingDirection.TO_API ? originalField : mappedField;
          if (isDecimalField(tableName, targetField)) {
            finalValue = this.convertDecimalValue(value);
            if (finalValue !== value) {
              stats.convertedFields++;
            }
          }
        }

        result[mappedField] = finalValue;

        // 字段映射成功 - 调试日志已移除
      } else {
        // 字段映射失败
        stats.unmappedFields++;

        if (keepUnmappedFields) {
          result[originalField] = finalValue;
          // 保留未映射字段 - 调试日志已移除
        } else {
          warnings.push(`字段 ${originalField} 未找到映射关系`);
          if (debug) {
            console.warn(`[FieldMapper] 跳过未映射字段: ${originalField}`);
          }
        }
      }
    }

    // 应用默认值
    if (applyDefaults && direction === MappingDirection.FROM_API) {
      this.applyDefaultValues(result, tableName, stats, debug);
    }

    return {
      data: result as T,
      stats,
      warnings
    };
  }

  /**
   * 批量处理数组数据
   */
  static mapArrayFields<T = any>(
    dataArray: Record<string, any>[],
    tableName: string,
    direction: MappingDirection,
    options: FieldMappingOptions = {}
  ): MappingResult<T[]> {
    if (!Array.isArray(dataArray)) {
      throw new Error('输入数据必须是数组');
    }

    const results: T[] = [];
    const aggregatedStats = {
      totalFields: 0,
      mappedFields: 0,
      unmappedFields: 0,
      convertedFields: 0
    };
    const allWarnings: string[] = [];

    for (let i = 0; i < dataArray.length; i++) {
      const item = dataArray[i];
      const result = this.mapFields<T>(item, tableName, direction, options);

      results.push(result.data);

      // 聚合统计信息
      aggregatedStats.totalFields += result.stats.totalFields;
      aggregatedStats.mappedFields += result.stats.mappedFields;
      aggregatedStats.unmappedFields += result.stats.unmappedFields;
      aggregatedStats.convertedFields += result.stats.convertedFields;

      // 收集警告（避免重复）
      result.warnings.forEach(warning => {
        const indexedWarning = `[${i}] ${warning}`;
        if (!allWarnings.includes(indexedWarning)) {
          allWarnings.push(indexedWarning);
        }
      });
    }

    return {
      data: results,
      stats: aggregatedStats,
      warnings: allWarnings
    };
  }

  /**
   * 转换过滤条件中的字段名
   */
  static mapFilterConditions(
    whereClause: string,
    tableName: string,
    direction: MappingDirection = MappingDirection.TO_API
  ): string {
    const tableMapping = getTableMapping(tableName);
    if (!tableMapping) {
      console.warn(`[FieldMapper] 未找到表 ${tableName} 的映射配置，过滤条件保持不变`);
      return whereClause;
    }

    let mappedClause = whereClause;

    // 解析过滤条件中的字段名
    // 支持格式: (字段名,操作符,值) 和 (字段名,操作符,值)~and(字段名,操作符,值)
    const fieldPattern = /\(([^,]+),/g;
    let match;

    while ((match = fieldPattern.exec(whereClause)) !== null) {
      const originalField = match[1].trim();
      let mappedField: string | null = null;

      if (direction === MappingDirection.TO_API) {
        mappedField = getFieldMapping(tableName, originalField);
      } else {
        mappedField = getReverseFieldMapping(tableName, originalField);
      }

      if (mappedField) {
        mappedClause = mappedClause.replace(
          new RegExp(`\\(${originalField.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')},`, 'g'),
          `(${mappedField},`
        );
      }
    }

    return mappedClause;
  }

  /**
   * 转换DECIMAL类型值
   */
  private static convertDecimalValue(value: any): number {
    if (value === null || value === undefined) {
      return 0;
    }

    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  }

  /**
   * 应用默认值
   */
  private static applyDefaultValues(
    result: Record<string, any>,
    tableName: string,
    stats: any,
    debug: boolean
  ): void {
    const tableMapping = getTableMapping(tableName);
    if (!tableMapping) return;

    for (const [field, defaultValue] of Object.entries(tableMapping.defaultValues)) {
      if (!(field in result) || result[field] === null || result[field] === undefined) {
        result[field] = defaultValue;
        // 应用默认值 - 调试日志已移除
      }
    }
  }

  /**
   * 验证必需字段
   */
  static validateRequiredFields(
    data: Record<string, any>,
    tableName: string
  ): { isValid: boolean; missingFields: string[] } {
    const tableMapping = getTableMapping(tableName);
    if (!tableMapping) {
      return { isValid: true, missingFields: [] };
    }

    const missingFields: string[] = [];

    for (const requiredField of tableMapping.requiredFields) {
      if (!(requiredField in data) || data[requiredField] === null || data[requiredField] === undefined) {
        missingFields.push(requiredField);
      }
    }

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }

  /**
   * 获取映射统计信息
   */
  static getMappingStats(tableName: string): {
    totalMappings: number;
    decimalFields: number;
    requiredFields: number;
    defaultValues: number;
  } {
    const tableMapping = getTableMapping(tableName);
    if (!tableMapping) {
      return { totalMappings: 0, decimalFields: 0, requiredFields: 0, defaultValues: 0 };
    }

    return {
      totalMappings: Object.keys(tableMapping.fields).length,
      decimalFields: tableMapping.decimalFields.length,
      requiredFields: tableMapping.requiredFields.length,
      defaultValues: Object.keys(tableMapping.defaultValues).length
    };
  }
}
