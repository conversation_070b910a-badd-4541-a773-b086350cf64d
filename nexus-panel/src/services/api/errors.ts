/**
 * API 错误处理系统
 * 
 * 提供标准化的错误类型定义和处理方法
 */

import type { ApiError } from './types';

// ==================== 错误类型常量 ====================

/**
 * API 错误类型常量
 */
export const ApiErrorType = {
	/** 网络连接错误 */
	NETWORK_ERROR: 'NETWORK_ERROR',
	/** 请求超时 */
	TIMEOUT_ERROR: 'TIMEOUT_ERROR',
	/** 认证失败 */
	AUTH_ERROR: 'AUTH_ERROR',
	/** 服务器错误 */
	SERVER_ERROR: 'SERVER_ERROR',
	/** 数据格式错误 */
	DATA_FORMAT_ERROR: 'DATA_FORMAT_ERROR',
	/** 未知错误 */
	UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

/**
 * API 错误类型
 */
export type ApiErrorType = typeof ApiErrorType[keyof typeof ApiErrorType];

/**
 * 错误消息映射
 */
export const ERROR_MESSAGES = {
	[ApiErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
	[ApiErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
	[ApiErrorType.AUTH_ERROR]: '认证失败，请检查访问权限',
	[ApiErrorType.SERVER_ERROR]: '服务器错误，请稍后重试',
	[ApiErrorType.DATA_FORMAT_ERROR]: '数据格式异常，请联系技术支持',
	[ApiErrorType.UNKNOWN_ERROR]: '未知错误，请稍后重试'
} as const;

// ==================== 错误处理类 ====================

/**
 * API 错误处理类
 */
export class ApiErrorHandler {
	/**
	 * 根据错误对象创建标准化的 ApiError
	 */
	static createApiError(error: unknown): ApiError {
		// 如果已经是 ApiError，直接返回
		if (this.isApiError(error)) {
			return error;
		}

		// 处理 fetch 错误
		if (error instanceof TypeError && error.message.includes('fetch')) {
			return {
				message: ERROR_MESSAGES[ApiErrorType.NETWORK_ERROR],
				code: ApiErrorType.NETWORK_ERROR,
				originalError: error
			};
		}

		// 处理超时错误
		if (error instanceof Error && error.name === 'AbortError') {
			return {
				message: ERROR_MESSAGES[ApiErrorType.TIMEOUT_ERROR],
				code: ApiErrorType.TIMEOUT_ERROR,
				originalError: error
			};
		}

		// 处理一般 Error 对象
		if (error instanceof Error) {
			return {
				message: error.message || ERROR_MESSAGES[ApiErrorType.UNKNOWN_ERROR],
				code: ApiErrorType.UNKNOWN_ERROR,
				originalError: error
			};
		}

		// 处理字符串错误
		if (typeof error === 'string') {
			return {
				message: error,
				code: ApiErrorType.UNKNOWN_ERROR
			};
		}

		// 兜底处理
		return {
			message: ERROR_MESSAGES[ApiErrorType.UNKNOWN_ERROR],
			code: ApiErrorType.UNKNOWN_ERROR,
			originalError: error instanceof Error ? error : new Error(String(error))
		};
	}

	/**
	 * 根据 HTTP 状态码创建 ApiError
	 */
	static createHttpError(status: number, statusText: string, responseData?: unknown): ApiError {
		let errorType: ApiErrorType;
		let message: string;

		switch (true) {
			case status === 401 || status === 403:
				errorType = ApiErrorType.AUTH_ERROR;
				message = ERROR_MESSAGES[ApiErrorType.AUTH_ERROR];
				break;
			case status >= 500:
				errorType = ApiErrorType.SERVER_ERROR;
				message = ERROR_MESSAGES[ApiErrorType.SERVER_ERROR];
				break;
			case status >= 400:
				errorType = ApiErrorType.DATA_FORMAT_ERROR;
				message = (responseData && typeof responseData === 'object' && 'message' in responseData && typeof responseData.message === 'string')
					? responseData.message
					: ERROR_MESSAGES[ApiErrorType.DATA_FORMAT_ERROR];
				break;
			default:
				errorType = ApiErrorType.UNKNOWN_ERROR;
				message = ERROR_MESSAGES[ApiErrorType.UNKNOWN_ERROR];
		}

		return {
			message,
			code: errorType,
			status,
			originalError: new Error(`HTTP ${status}: ${statusText}`)
		};
	}

	/**
	 * 检查对象是否为 ApiError
	 */
	static isApiError(error: unknown): error is ApiError {
		return (
			typeof error === 'object' &&
			error !== null &&
			'message' in error &&
			typeof (error as Record<string, unknown>).message === 'string'
		);
	}

	/**
	 * 获取用户友好的错误消息
	 */
	static getUserFriendlyMessage(error: ApiError): string {
		// 如果有自定义消息且不是默认错误消息，使用自定义消息
		const errorMessages = Object.values(ERROR_MESSAGES) as string[];
		if (error.message && !errorMessages.includes(error.message)) {
			return error.message;
		}

		// 否则根据错误代码返回标准消息
		if (error.code && typeof error.code === 'string' && error.code in ERROR_MESSAGES) {
			return ERROR_MESSAGES[error.code as ApiErrorType];
		}

		return ERROR_MESSAGES[ApiErrorType.UNKNOWN_ERROR];
	}

	/**
	 * 记录错误详情（用于调试）
	 */
	static logError(error: ApiError, context?: string): void {
		const logContext = context ? `[${context}]` : '';
		console.error(`${logContext} API Error:`, {
			message: error.message,
			code: error.code,
			status: error.status,
			originalError: error.originalError
		});
	}
}

// ==================== 便捷函数 ====================

/**
 * 创建网络错误
 */
export function createNetworkError(originalError?: Error): ApiError {
	return {
		message: ERROR_MESSAGES[ApiErrorType.NETWORK_ERROR],
		code: ApiErrorType.NETWORK_ERROR,
		originalError
	};
}

/**
 * 创建认证错误
 */
export function createAuthError(message?: string): ApiError {
	return {
		message: message || ERROR_MESSAGES[ApiErrorType.AUTH_ERROR],
		code: ApiErrorType.AUTH_ERROR
	};
}

/**
 * 创建服务器错误
 */
export function createServerError(status?: number, message?: string): ApiError {
	return {
		message: message || ERROR_MESSAGES[ApiErrorType.SERVER_ERROR],
		code: ApiErrorType.SERVER_ERROR,
		status
	};
}

/**
 * 创建数据格式错误
 */
export function createDataFormatError(message?: string): ApiError {
	return {
		message: message || ERROR_MESSAGES[ApiErrorType.DATA_FORMAT_ERROR],
		code: ApiErrorType.DATA_FORMAT_ERROR
	};
}
