/**
 * 简化的数据服务
 * 统一处理所有数据获取，避免复杂的流程和硬编码问题
 */

import {
  TABLE_MAPPINGS,
  findTableByLogicalName,
  validateRequiredTables,
  type TableMapping
} from '../../config/tableMapping';
import { ConfigValidator } from '../../utils/configValidator';

export interface SimplifiedDataResult {
  navigationData: any[];
  configurationData: any;
  deviceData: any[];
  success: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 简化的项目数据获取服务
 * 一次性获取所有必需的数据，统一错误处理
 */
export class SimplifiedDataService {
  private cacheManager: any;

  constructor(cacheManager: any) {
    this.cacheManager = cacheManager;
  }

  /**
   * 获取项目的所有数据
   * @param baseId 项目ID
   * @returns 包含所有数据的结果对象
   */
  async fetchAllProjectData(baseId: string): Promise<SimplifiedDataResult> {
    const result: SimplifiedDataResult = {
      navigationData: [],
      configurationData: {},
      deviceData: [],
      success: false,
      errors: [],
      warnings: []
    };

    try {
      console.log('🚀 SimplifiedDataService 开始获取项目数据', {
        baseId,
        timestamp: Date.now()
      });

      // 1. 获取并验证表结构
      const tableValidation = await this.validateTables(baseId);
      if (!tableValidation.success) {
        result.errors.push(...tableValidation.errors);
        return result;
      }

      // 2. 使用配置验证器进行深度验证
      const configValidation = ConfigValidator.validateProjectConfig(baseId, tableValidation.tables);
      if (!configValidation.success) {
        result.errors.push(...configValidation.errors);
      }
      result.warnings.push(...configValidation.warnings);

      // 3. 并行获取所有数据
      const dataResults = await this.fetchAllDataInParallel(baseId, tableValidation.tables);

      // 4. 整合结果
      result.navigationData = dataResults.navigationData || [];
      result.configurationData = dataResults.configurationData || {};
      result.deviceData = dataResults.deviceData || [];
      result.warnings.push(...dataResults.warnings);

      // 5. 检查是否有关键数据缺失
      if (result.navigationData.length === 0) {
        result.warnings.push('导航数据为空，可能影响界面显示');
      }

      result.success = true;

      console.log('✅ SimplifiedDataService 数据获取完成', {
        baseId,
        navigationCount: result.navigationData.length,
        configurationKeys: Object.keys(result.configurationData),
        deviceCount: result.deviceData.length,
        warningsCount: result.warnings.length,
        timestamp: Date.now()
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      result.errors.push(`数据获取失败: ${errorMessage}`);

      console.error('❌ SimplifiedDataService 数据获取失败', {
        baseId,
        error: errorMessage,
        timestamp: Date.now()
      });
    }

    return result;
  }

  /**
   * 验证必需的表是否存在
   */
  private async validateTables(baseId: string): Promise<{
    success: boolean;
    errors: string[];
    tables: any[];
  }> {
    try {
      const tableStructureData = await this.cacheManager.getTableStructureWithCache(baseId);
      const tables = tableStructureData.list;

      const requiredTables = ['NAVIGATION', 'CONFIGURATION'] as const;
      const validation = validateRequiredTables(tables, requiredTables);

      if (!validation.success) {
        return {
          success: false,
          errors: [
            `缺少必需的表: ${validation.missingTables.join(', ')}`,
            `可用的表: ${validation.availableTables.join(', ')}`
          ],
          tables: []
        };
      }

      console.log('✅ SimplifiedDataService 表结构验证通过', {
        baseId,
        tablesCount: tables.length,
        availableTables: validation.availableTables,
        timestamp: Date.now()
      });

      return {
        success: true,
        errors: [],
        tables
      };

    } catch (error) {
      return {
        success: false,
        errors: [`表结构验证失败: ${error instanceof Error ? error.message : String(error)}`],
        tables: []
      };
    }
  }

  /**
   * 并行获取所有数据
   */
  private async fetchAllDataInParallel(baseId: string, tables: any[]): Promise<{
    navigationData?: any[];
    configurationData?: any;
    deviceData?: any[];
    warnings: string[];
  }> {
    const warnings: string[] = [];

    const [navigationData, configurationData, deviceData] = await Promise.allSettled([
      this.fetchNavigationData(baseId),
      this.fetchConfigurationData(baseId, tables),
      this.fetchDeviceData(baseId)
    ]);

    // 处理导航数据结果
    const navData = navigationData.status === 'fulfilled'
      ? navigationData.value
      : (() => {
        warnings.push(`导航数据获取失败: ${navigationData.reason}`);
        return [];
      })();

    // 处理配置数据结果
    const configData = configurationData.status === 'fulfilled'
      ? configurationData.value
      : (() => {
        warnings.push(`配置数据获取失败: ${configurationData.reason}`);
        return {};
      })();

    // 处理设备数据结果
    const devData = deviceData.status === 'fulfilled'
      ? deviceData.value
      : (() => {
        warnings.push(`设备数据获取失败: ${deviceData.reason}`);
        return [];
      })();

    return {
      navigationData: navData,
      configurationData: configData,
      deviceData: devData,
      warnings
    };
  }

  /**
   * 获取导航数据
   */
  private async fetchNavigationData(baseId: string): Promise<any[]> {
    const { getNavigationData } = await import('../api');
    return await getNavigationData(baseId);
  }

  /**
   * 获取配置数据
   */
  private async fetchConfigurationData(baseId: string, tables: any[]): Promise<any> {
    const configTable = findTableByLogicalName(tables, 'CONFIGURATION');
    const { getGroupedConfigurationData } = await import('../api');
    return await getGroupedConfigurationData(configTable.id);
  }

  /**
   * 获取设备数据
   */
  private async fetchDeviceData(baseId: string): Promise<any[]> {
    const { getDeviceData } = await import('../api');
    return await getDeviceData(baseId);
  }
}

/**
 * 创建简化数据服务实例
 */
export function createSimplifiedDataService(cacheManager: any): SimplifiedDataService {
  return new SimplifiedDataService(cacheManager);
}
