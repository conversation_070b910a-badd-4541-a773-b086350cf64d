/**
 * API 服务类型定义
 * 
 * 定义所有与 HTTP API 相关的类型接口和枚举
 */

// ==================== 基础 API 类型 ====================

/**
 * API 请求配置接口
 */
export interface ApiConfig {
	/** API 基础 URL */
	baseUrl: string;
	/** 默认请求头 */
	defaultHeaders: Record<string, string>;
	/** 请求超时时间（毫秒） */
	timeout: number;
}

/**
 * API 响应基础接口
 */
export interface ApiResponse<T = unknown> {
	/** 响应数据 */
	data: T;
	/** HTTP 状态码 */
	status: number;
	/** 响应头 */
	headers: Record<string, string>;
}

/**
 * API 错误接口
 */
export interface ApiError {
	/** 错误消息 */
	message: string;
	/** 错误代码 */
	code?: string | number;
	/** HTTP 状态码 */
	status?: number;
	/** 原始错误对象 */
	originalError?: Error;
}

// ==================== 赛事相关类型 ====================

/**
 * 原始赛事 API 响应数据项
 * 对应后端返回的原始数据结构
 */
export interface RaceApiItem {
	/** 数据库 ID */
	Id: number;
	/** 赛事唯一标识 */
	"赛事 ID": string;
	/** 赛事显示名称 */
	"赛事名称": string;
	/** 是否在前端显示 */
	"是否显示": string;
	/** 参赛人数 */
	"参赛人数"?: number;
}

/**
 * 赛事 API 完整响应接口
 */
export interface RaceApiResponse {
	/** 赛事数据列表 */
	list: RaceApiItem[];
	/** 分页信息 */
	pageInfo: {
		/** 总记录数 */
		totalRows: number;
		/** 当前页码 */
		page: number;
		/** 每页大小 */
		pageSize: number;
		/** 是否为第一页 */
		isFirstPage: boolean;
		/** 是否为最后一页 */
		isLastPage: boolean;
	};
}

/**
 * 处理后的赛事数据项
 * 用于前端组件的标准化数据结构
 */
export interface ProcessedRaceItem {
	/** 赛事唯一标识（对应原始数据的"赛事 ID"） */
	id: string;
	/** 赛事显示名称（对应原始数据的"赛事名称"） */
	name: string;
	/** 是否可见（对应原始数据的"是否显示" === "显示"） */
	visible: boolean;
	/** 原始数据库 ID */
	dbId: number;
	/** 参赛人数（对应原始数据的"参赛人数"，默认为17） */
	peopleCount: number;
}

// ==================== 导航相关类型 ====================

/**
 * 表结构 API 响应数据项
 */
export interface TableStructureItem {
	/** 表ID */
	id: string;
	/** 表标题 */
	title: string;
	/** 表类型 */
	type?: string;
	/** 其他属性 */
	[key: string]: unknown;
}

// ==================== 配置信息相关类型 ====================

/**
 * 附件对象接口
 */
export interface AttachmentItem {
	/** 文件URL */
	url: string;
	/** 文件标题 */
	title: string;
	/** MIME类型 */
	mimetype: string;
	/** 文件大小 */
	size: number;
	/** 图片宽度 */
	width?: number;
	/** 图片高度 */
	height?: number;
	/** 附件ID */
	id: string;
	/** 缩略图配置 */
	thumbnails?: {
		tiny?: { signedUrl: string };
		small?: { signedUrl: string };
		card_cover?: { signedUrl: string };
	};
	/** 签名URL */
	signedUrl: string;
}

/**
 * 配置信息 API 响应数据项（使用schema定义的英文字段名）
 */
export interface ConfigurationDataItem {
	/** 记录ID */
	Id: number;
	/** 信息ID */
	info_id?: number;
	/** 标题 */
	title: string;
	/** 内容（可能为null） */
	content: string | null;
	/** 附件数组（可能为null） */
	attachment_url: AttachmentItem[] | null;
	/** 信息类型 */
	info_type: string;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 配置信息 API 响应接口
 */
export interface ConfigurationDataResponse {
	/** 配置数据列表 */
	list: ConfigurationDataItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}

/**
 * 处理后的配置数据项
 */
export interface ProcessedConfigurationItem {
	/** 记录ID */
	id: number;
	/** 标题 */
	title: string;
	/** 内容 */
	content: string;
	/** 附件数组 */
	attachments: AttachmentItem[];
	/** 信息类型 */
	infoType: string;
	/** 主图片URL（附件中第一个图片的signedUrl） */
	primaryImageUrl?: string;
}

/**
 * 按信息类型分组的配置数据
 */
export interface GroupedConfigurationData {
	/** 领导显示类型数据 */
	leaderDisplay: ProcessedConfigurationItem[];
	/** 选手显示类型数据 */
	playerDisplay: ProcessedConfigurationItem[];
	/** 奖项显示类型数据 */
	awardDisplay: ProcessedConfigurationItem[];
	/** 节目显示类型数据 */
	programDisplay: ProcessedConfigurationItem[];
}

/**
 * 合并项目数据响应接口
 */
export interface CombinedProjectDataResponse {
	/** 导航数据 */
	navigationData: NavigationNode[];
	/** 配置数据 */
	configurationData: GroupedConfigurationData;
}

/**
 * 表结构 API 响应接口
 */
export interface TableStructureResponse {
	/** 表结构列表 */
	list: TableStructureItem[];
}

/**
 * 环节数据 API 响应数据项
 */
export interface SectionDataItem {
	/** 记录ID */
	Id: number;
	/** 环节名称 */
	"环节名称": string;
	/** 显示顺序 */
	"显示顺序": number;
	/** 环节类型 */
	"环节类型": string;
	/** 环节图标 */
	"环节图标": string;
	/** 内容类型 - 用于动态组件绑定 */
	"内容类型"?: string;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 环节数据 API 响应接口
 */
export interface SectionDataResponse {
	/** 环节数据列表 */
	list: SectionDataItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}

/**
 * 处理后的导航节点
 */
export interface NavigationNode {
	/** 节点ID */
	id: string;
	/** 节点名称 */
	name: string;
	/** 节点图标名称 */
	icon: string;
	/** 父节点ID */
	parentId?: string;
	/** 子节点列表 */
	children?: NavigationNode[];
	/** 显示顺序 */
	order: number;
	/** 节点类型 */
	type: string;
	/** 内容类型 - 用于动态组件绑定 */
	contentType?: string;
	/** 关联的表ID */
	tableId?: string;
	/** 初始阶段 */
	initialStage?: string;
}

// ==================== API 状态类型 ====================

/**
 * API 请求状态类型
 */
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * API 请求状态常量
 */
export const ApiStatus = {
	/** 空闲状态 */
	IDLE: 'idle' as const,
	/** 加载中 */
	LOADING: 'loading' as const,
	/** 成功 */
	SUCCESS: 'success' as const,
	/** 错误 */
	ERROR: 'error' as const
} as const;

/**
 * API Hook 配置选项
 */
export interface UseApiOptions<T = unknown> {
	/** 是否在 Hook 初始化时立即执行请求 */
	immediate?: boolean;
	/** 请求成功回调 */
	onSuccess?: (data: T) => void;
	/** 请求失败回调 */
	onError?: (error: ApiError) => void;
	/** 请求开始回调 */
	onStart?: () => void;
	/** 请求完成回调（无论成功失败） */
	onFinally?: () => void;
}

/**
 * API Hook 返回值接口
 */
export interface UseApiReturn<T = unknown> {
	/** 响应数据 */
	data: T | null;
	/** 加载状态 */
	loading: boolean;
	/** 错误信息 */
	error: ApiError | null;
	/** 当前状态 */
	status: ApiStatus;
	/** 手动执行请求 */
	execute: () => Promise<void>;
	/** 重置状态 */
	reset: () => void;
	/** 重新执行请求（等同于 execute） */
	refetch: () => Promise<void>;
}

// ==================== 题目相关类型 ====================

/**
 * 题目 API 响应数据项（使用schema定义的英文字段名）
 */
export interface QuestionApiItem {
	/** 记录ID */
	Id: number;
	/** 题目ID */
	question_id?: number | null;
	/** 题号 */
	question_number?: number | null;
	/** 题型 */
	question_type?: string | null;
	/** 题干 */
	prompt?: string | null;
	/** 选项 */
	options?: string | null;
	/** 正确答案 */
	correct_answer?: string | null;
	/** 分值 */
	points?: number | null;
	/** 解析 */
	explanation?: string | null;
	/** 所属环节 */
	session_id: string;
	/** 题包编号 */
	question_pack_id: string;
	/** 附件数组（音频文件） */
	attachment_url?: AttachmentItem[] | null;
	/** 所属阶段 */
	stage?: string | null;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 题目 API 响应接口
 */
export interface QuestionApiResponse {
	/** 题目数据列表 */
	list: QuestionApiItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}

/**
 * 解析后的选项接口
 */
export interface ParsedOption {
	/** 选项标识（A、B、C、D等） */
	label: string;
	/** 选项内容 */
	content: string;
}

/**
 * 处理后的题目数据项
 */
export interface ProcessedQuestionItem {
	/** 记录ID */
	id: number;
	/** 题号 */
	questionNumber: number;
	/** 题型 */
	questionType: string;
	/** 分值 */
	score: number;
	/** 题干 */
	stem: string;
	/** 解析后的选项数组 */
	options: ParsedOption[];
	/** 答案 */
	answer: string;
	/** 解析内容 */
	explanation: string;
	/** 所属环节 */
	section: string;
	/** 题包编号 */
	packageNumber: string;
	/** 所属阶段 */
	stage?: string;
	/** 音频URL数组 */
	audioUrls: string[];
	/** 主音频URL（第一个音频的signedUrl） */
	primaryAudioUrl?: string;
}

// ==================== 排名相关类型 ====================

/**
 * 答题记录 API 响应数据项（使用schema定义的英文字段名）
 */
export interface AnswerRecordApiItem {
	/** 记录ID */
	Id: number;
	/** 选手答案 */
	submitted_answer: string;
	/** 选手正误（1为正确，0为错误） */
	is_correct: number;
	/** 分值 */
	score: number;
	/** 选手ID */
	user_id: number;
	/** 题目ID */
	question_id: number;
	/** 题号 */
	question_number?: number;
	/** 判分类型 */
	grading_type?: string;
	/** 所属环节 */
	session_id: string;
	/** 状态 */
	status?: string;
	/** 提交类型 */
	submission_type?: string;
	/** 仲裁详情 */
	audit_details?: string;
	/** 提交时间 */
	CreatedAt?: string;
	/** 更新时间 */
	UpdatedAt?: string;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 答题记录 API 响应接口
 */
export interface AnswerRecordApiResponse {
	/** 答题记录数据列表 */
	list: AnswerRecordApiItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}

/**
 * 选手信息 API 响应数据项（使用schema定义的英文字段名）
 */
export interface PlayerInfoApiItem {
	/** 记录ID */
	Id: number;
	/** 选手ID */
	user_id: number;
	/** 选手名称 */
	user_name: string;
	/** 头像URL */
	avatar_url?: string;
	/** 复活机会次数 */
	revival_chances?: number;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 选手信息 API 响应接口
 */
export interface PlayerInfoApiResponse {
	/** 选手信息数据列表 */
	list: PlayerInfoApiItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}

/**
 * 处理后的选手得分数据
 */
export interface PlayerScore {
	/** 选手ID */
	playerId: string;
	/** 选手名称 */
	playerName: string;
	/** 各环节得分（环节名称 -> 得分） */
	stageScores: Record<string, number>;
	/** 总分 */
	totalScore: number;
	/** 排名（可选，计算后填入） */
	rank?: number;
}

/**
 * 完整的排名数据
 */
export interface RankingData {
	/** 选手得分列表（已排序） */
	players: PlayerScore[];
	/** 所有环节名称列表 */
	stages: string[];
	/** 数据最后更新时间戳 */
	lastUpdated: number;
	/** 参赛选手总数 */
	totalPlayers: number;
	/** API数据获取时间戳（用于跟踪真正的API更新，可选字段） */
	fetchTimestamp?: number;
}

/**
 * 排名分页信息
 */
export interface RankingPaginationInfo {
	/** 当前页码（从1开始） */
	currentPage: number;
	/** 总页数 */
	totalPages: number;
	/** 每页显示数量 */
	pageSize: number;
	/** 是否有多页（用于控制分页按钮显示） */
	hasMultiplePages: boolean;
}

/**
 * 分页后的排名数据
 */
export interface PaginatedRankingData extends RankingData {
	/** 分页信息 */
	paginationInfo: RankingPaginationInfo;
}

/**
 * 排名数据获取进度信息
 */
export interface RankingProgress {
	/** 当前阶段 */
	stage: 'table_structure' | 'answer_records' | 'player_info' | 'calculating' | 'complete';
	/** 进度百分比（0-100） */
	progress: number;
	/** 进度描述信息 */
	message: string;
	/** 当前页码（分页获取时） */
	currentPage?: number;
	/** 总页数（分页获取时） */
	totalPages?: number;
}

// ==================== 请求方法类型 ====================

/**
 * HTTP 请求方法类型
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * HTTP 请求方法常量
 */
export const HttpMethod = {
	GET: 'GET' as const,
	POST: 'POST' as const,
	PUT: 'PUT' as const,
	DELETE: 'DELETE' as const,
	PATCH: 'PATCH' as const
} as const;

/**
 * 请求参数接口
 */
export interface RequestOptions {
	/** 请求方法 */
	method?: HttpMethod;
	/** 请求头 */
	headers?: Record<string, string>;
	/** 请求体 */
	body?: Record<string, unknown> | FormData | string | null;
	/** URL 查询参数 */
	params?: Record<string, string | number | boolean>;
	/** 请求超时时间 */
	timeout?: number;
}

// ==================== 规则介绍相关类型 ====================

/**
 * 原始规则介绍 API 响应数据项
 * 对应后端返回的原始数据结构
 */
export interface RulesIntroductionApiItem {
	/** 数据库 ID */
	Id: number;
	/** 创建时间 */
	CreatedAt: string;
	/** 更新时间 */
	UpdatedAt: string;
	/** 信息 ID */
	"信息 ID": number;
	/** 规则标题 */
	"标题": string;
	/** 规则内容（支持Markdown格式） */
	"内容": string;
	/** 附件数组（音频文件等） */
	"附件": AttachmentItem[] | null;
	/** 信息类型（固定为"规则介绍"） */
	"信息类型": string;
}

/**
 * 规则介绍 API 完整响应接口
 */
export interface RulesIntroductionApiResponse {
	/** 规则介绍数据列表 */
	list: RulesIntroductionApiItem[];
	/** 分页信息 */
	pageInfo: {
		/** 总记录数 */
		totalRows: number;
		/** 当前页码 */
		page: number;
		/** 每页大小 */
		pageSize: number;
		/** 是否为第一页 */
		isFirstPage: boolean;
		/** 是否为最后一页 */
		isLastPage: boolean;
	};
}

/**
 * 处理后的规则介绍数据项
 * 用于前端组件渲染
 */
export interface ProcessedRulesIntroductionItem {
	/** 唯一标识 */
	id: number;
	/** 规则标题 */
	title: string;
	/** 规则内容（Markdown格式） */
	content: string;
	/** 音频文件URL列表 */
	audioUrls: string[];
	/** 信息类型 */
	infoType: string;
	/** 创建时间 */
	createdAt: string;
	/** 更新时间 */
	updatedAt: string;
}

// ==================== 缓存相关类型 ====================

/**
 * 表结构缓存统计接口
 */
export interface CacheStats {
	/** 缓存命中次数 */
	hits: number;
	/** 缓存未命中次数 */
	misses: number;
	/** 总请求次数 */
	totalRequests: number;
	/** 上次报告时间 */
	lastReportTime: number;
}

/**
 * 缓存性能统计接口
 */
export interface CachePerformanceStats {
	/** 当前缓存大小 */
	cacheSize: number;
	/** 总请求次数 */
	totalRequests: number;
	/** 缓存命中次数 */
	hits: number;
	/** 缓存未命中次数 */
	misses: number;
	/** 命中率（百分比） */
	hitRate: number;
	/** 未命中率（百分比） */
	missRate: number;
	/** 缓存效率评级 */
	efficiency: 'good' | 'fair' | 'poor';
	/** 内存使用情况 */
	memoryUsage: {
		/** 估算的内存使用量（KB） */
		estimatedKB: number;
		/** 建议的最大缓存大小 */
		maxRecommendedSize: number;
	};
}

/**
 * 缓存验证结果接口
 */
export interface CacheValidationResult {
	/** 验证是否通过 */
	isValid: boolean;
	/** 验证失败的原因 */
	reason?: string;
	/** 验证的详细信息 */
	details?: {
		/** 数据类型检查 */
		typeCheck: boolean;
		/** 必需字段检查 */
		requiredFieldsCheck: boolean;
		/** 表结构检查 */
		tableStructureCheck: boolean;
		/** 可用表列表 */
		availableTables?: string[];
	};
}

/**
 * 缓存操作类型联合类型
 */
export type CacheActionType =
	| 'cache_hit'
	| 'cache_miss'
	| 'cache_store'
	| 'cache_clear'
	| 'cache_validate'
	| 'performance_report'
	| 'auto_repair';

/**
 * 缓存操作类型常量
 */
export const CACHE_ACTION_TYPES = {
	/** 缓存命中 */
	HIT: 'cache_hit' as const,
	/** 缓存未命中 */
	MISS: 'cache_miss' as const,
	/** 缓存存储 */
	STORE: 'cache_store' as const,
	/** 缓存清理 */
	CLEAR: 'cache_clear' as const,
	/** 缓存验证 */
	VALIDATE: 'cache_validate' as const,
	/** 性能报告 */
	PERFORMANCE_REPORT: 'performance_report' as const,
	/** 自动修复 */
	AUTO_REPAIR: 'auto_repair' as const
} as const;

/**
 * 缓存日志条目接口
 */
export interface CacheLogEntry {
	/** 操作类型 */
	action: CacheActionType;
	/** 项目ID */
	baseId: string;
	/** 时间戳 */
	timestamp: number;
	/** 缓存大小 */
	cacheSize: number;
	/** 性能统计（可选） */
	performanceStats?: Partial<CachePerformanceStats>;
	/** 错误信息（可选） */
	error?: string;
	/** 额外的上下文信息 */
	context?: Record<string, unknown>;
}
