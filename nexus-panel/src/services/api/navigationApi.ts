/**
 * 导航数据相关 API 服务
 * 
 * 提供动态导航数据的获取、处理和转换功能
 * 包括表结构获取和环节数据获取
 */

import { httpClient } from './client';
import type { ApiResponse, NavigationNode, ConfigurationDataResponse, ConfigurationDataItem, ProcessedConfigurationItem, GroupedConfigurationData, AttachmentItem, RulesIntroductionApiResponse, RulesIntroductionApiItem, ProcessedRulesIntroductionItem } from './types';
import { ApiErrorHandler, createDataFormatError } from './errors';
import { FieldMapper, MappingDirection } from './adapters/FieldMapper';
import { TypeConverter } from './adapters/TypeConverter';

// ==================== API 配置 ====================

/**
 * 导航 API 配置
 */
const NAVIGATION_API_CONFIG = {
	/** 认证 Token */
	token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
} as const;

// ==================== 类型定义 ====================

/**
 * 表结构 API 响应数据项
 */
export interface TableStructureItem {
	/** 表ID */
	id: string;
	/** 表标题 */
	title: string;
	/** 表类型 */
	type?: string;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 表结构 API 响应接口
 */
export interface TableStructureResponse {
	/** 表结构列表 */
	list: TableStructureItem[];
}

/**
 * 环节数据 API 响应数据项（使用schema定义的英文字段名）
 */
export interface SectionDataItem {
	/** 记录ID */
	Id: number;
	/** 环节ID */
	session_id?: string;
	/** 环节名称 */
	session_name: string;
	/** 显示顺序 */
	display_order: number;
	/** 环节类型 */
	nav_type: string;
	/** 环节图标 */
	session_icon: string;
	/** 内容类型 - 用于动态组件绑定 */
	content_type?: string;
	/** 初始阶段 - 用于题目数据筛选 */
	initial_stage?: string;
	/** 环节配置 */
	session_config?: string;
	/** 其他属性 */
	[key: string]: unknown;
}

/**
 * 环节数据 API 响应接口
 */
export interface SectionDataResponse {
	/** 环节数据列表 */
	list: SectionDataItem[];
	/** 分页信息 */
	pageInfo: {
		totalRows: number;
		page: number;
		pageSize: number;
		isFirstPage: boolean;
		isLastPage: boolean;
	};
}



// ==================== 数据验证函数 ====================

/**
 * 验证表结构响应数据格式
 */
function validateTableStructureResponse(data: unknown): data is TableStructureResponse {
	if (!data || typeof data !== 'object') {
		return false;
	}

	const response = data as Record<string, unknown>;

	// 检查是否有 list 属性且为数组
	if (!Array.isArray(response.list)) {
		return false;
	}

	// 检查数组中每个项目的基本结构
	return response.list.every((item: unknown) => {
		if (!item || typeof item !== 'object') {
			return false;
		}
		const tableItem = item as Record<string, unknown>;
		return typeof tableItem.id === 'string' && typeof tableItem.title === 'string';
	});
}

/**
 * 验证环节数据响应格式
 */
function validateSectionDataResponse(data: unknown): data is SectionDataResponse {
	if (!data || typeof data !== 'object') {
		return false;
	}

	const response = data as Record<string, unknown>;

	// 检查是否有 list 属性且为数组
	if (!Array.isArray(response.list)) {
		return false;
	}

	// 检查数组中每个项目的基本结构（使用英文字段名）
	return response.list.every((item: unknown) => {
		if (!item || typeof item !== 'object') {
			return false;
		}
		const sectionItem = item as Record<string, unknown>;
		return (
			typeof sectionItem.session_name === 'string' &&
			typeof sectionItem.display_order === 'number' &&
			typeof sectionItem.nav_type === 'string' &&
			typeof sectionItem.session_icon === 'string'
		);
	});
}

/**
 * 验证配置数据响应格式
 */
function validateConfigurationDataResponse(data: unknown): data is ConfigurationDataResponse {
	if (!data || typeof data !== 'object') {
		console.error('[配置数据验证] 数据不是对象:', data);
		return false;
	}

	const response = data as Record<string, unknown>;

	// 检查是否有 list 属性且为数组
	if (!Array.isArray(response.list)) {
		console.error('[配置数据验证] list属性不存在或不是数组:', {
			hasListProperty: 'list' in response,
			listType: typeof response.list,
			responseKeys: Object.keys(response)
		});
		return false;
	}

	// 如果list为空，也是有效的
	if (response.list.length === 0) {
		console.log('[配置数据验证] list为空数组，验证通过');
		return true;
	}

	// 检查数组中每个项目的基本结构
	const validationResults = response.list.map((item: unknown, index: number) => {
		if (!item || typeof item !== 'object') {
			console.error(`[配置数据验证] 项目${index}不是对象:`, item);
			return false;
		}
		const configItem = item as Record<string, unknown>;

		const validations = {
			hasId: 'Id' in configItem && typeof configItem.Id === 'number',
			hasTitle: 'title' in configItem && typeof configItem.title === 'string',
			// 修正：内容字段可能为null或字符串
			hasContent: 'content' in configItem && (
				configItem.content === null ||
				typeof configItem.content === 'string'
			),
			hasInfoType: 'info_type' in configItem && typeof configItem.info_type === 'string',
			// 修正：附件字段可能为null或数组
			hasAttachments: 'attachment_url' in configItem && (
				configItem.attachment_url === null ||
				Array.isArray(configItem.attachment_url)
			)
		};

		const isValid = Object.values(validations).every(Boolean);

		if (!isValid) {
			// 详细输出每个字段的验证结果
			console.error(`[配置数据验证] 项目${index}验证失败详情:`);
			console.error(`  - Id字段: 存在=${('Id' in configItem)}, 类型=${typeof configItem.Id}, 值=${configItem.Id}`);
			console.error(`  - title字段: 存在=${('title' in configItem)}, 类型=${typeof configItem.title}, 值=${configItem.title}`);
			console.error(`  - content字段: 存在=${('content' in configItem)}, 类型=${typeof configItem.content}, 值=${configItem.content}`);
			console.error(`  - info_type字段: 存在=${('info_type' in configItem)}, 类型=${typeof configItem.info_type}, 值=${configItem.info_type}`);
			console.error(`  - attachment_url字段: 存在=${('attachment_url' in configItem)}, 类型=${typeof configItem.attachment_url}, 值=${configItem.attachment_url}`);
			console.error(`  - 完整项目数据:`, configItem);
			console.error(`  - 验证结果:`, validations);
		}

		return isValid;
	});

	const allValid = validationResults.every(Boolean);

	if (!allValid) {
		console.error('[配置数据验证] 部分项目验证失败');
	} else {
		console.log(`[配置数据验证] 所有${response.list.length}个项目验证通过`);
	}

	return allValid;
}

/**
 * 验证附件对象格式
 */
function validateAttachmentItem(item: unknown): item is AttachmentItem {
	if (!item || typeof item !== 'object') {
		return false;
	}

	const attachment = item as Record<string, unknown>;
	return (
		typeof attachment.id === 'string' &&
		typeof attachment.title === 'string' &&
		typeof attachment.signedUrl === 'string'
	);
}

// ==================== 数据处理函数 ====================

/**
 * 转换环节数据项为导航节点
 */
function transformSectionToNavigationNode(item: SectionDataItem): NavigationNode {
	// 转换环节数据为导航节点 - 调试日志已移除

	const result = {
		id: `section-${item.Id}`,
		name: item.session_name,
		icon: item.session_icon,
		order: item.display_order,
		type: item.nav_type,
		contentType: item.content_type, // 转换内容类型字段
		initialStage: item.initial_stage, // 转换初始阶段字段
	};

	// 导航节点转换完成 - 调试日志已移除

	return result;
}

/**
 * 转换配置数据项为处理后的配置项
 */
function transformConfigurationItem(item: ConfigurationDataItem): ProcessedConfigurationItem {
	// 处理附件字段（可能为null）
	const attachments = item.attachment_url || [];

	// 提取主图片URL（附件中第一个图片的signedUrl）
	let primaryImageUrl: string | undefined;
	if (Array.isArray(attachments) && attachments.length > 0) {
		const firstAttachment = attachments[0];
		if (validateAttachmentItem(firstAttachment)) {
			primaryImageUrl = firstAttachment.signedUrl;
		}
	}

	return {
		id: item.Id,
		title: item.title,
		content: item.content || "", // 处理null值，提供空字符串作为默认值
		attachments: attachments,
		infoType: item.info_type,
		primaryImageUrl,
	};
}

/**
 * 按信息类型分组配置数据
 */
function groupConfigurationData(items: ProcessedConfigurationItem[]): GroupedConfigurationData {
	const grouped: GroupedConfigurationData = {
		leaderDisplay: [],
		playerDisplay: [],
		awardDisplay: [],
		programDisplay: [],
	};

	items.forEach(item => {
		switch (item.infoType) {
			case "领导显示":
				grouped.leaderDisplay.push(item);
				break;
			case "选手显示":
				grouped.playerDisplay.push(item);
				break;
			case "奖项显示":
				grouped.awardDisplay.push(item);
				break;
			case "节目显示":
				// 将节目显示数据添加到专门的 programDisplay 分组中
				grouped.programDisplay.push(item);
				// 配置数据分组完成 - 调试日志已移除
				break;
			default:
				console.warn(`未知的信息类型: ${item.infoType}，数据:`, item);
		}
	});

	return grouped;
}

/**
 * 构建树形导航结构
 */
function buildNavigationTree(sections: NavigationNode[]): NavigationNode[] {
	// 创建节点映射表，便于快速查找
	const nodeMap = new Map<string, NavigationNode>();

	// 初始化所有节点并建立映射
	sections.forEach(section => {
		const node: NavigationNode = {
			...section,
			children: []
		};
		nodeMap.set(section.id, node);
	});

	// 分离根节点和子节点
	const rootNodes: NavigationNode[] = [];
	const childNodes: NavigationNode[] = [];

	sections.forEach(section => {
		const node = nodeMap.get(section.id)!;

		if (section.type === "父节点") {
			// 父节点作为根节点
			rootNodes.push(node);
		} else {
			// 其他节点作为子节点
			childNodes.push(node);
		}
	});

	// 建立父子关系
	childNodes.forEach(child => {
		// 根据子节点的type字段匹配对应的父节点
		const parentNode = rootNodes.find(parent => {
			// 规则介绍类子节点归属到"规则介绍"父节点
			if (child.type === "规则介绍") {
				return parent.name === "规则介绍";
			}
			// 环节切换类子节点归属到"环节切换"父节点
			if (child.type === "环节切换") {
				return parent.name === "环节切换";
			}
			return false;
		});

		if (parentNode && parentNode.children) {
			child.parentId = parentNode.id;
			parentNode.children.push(child);
		}
	});

	// 对父节点按order排序
	rootNodes.sort((a, b) => a.order - b.order);

	// 对每个父节点的子节点按order排序
	rootNodes.forEach(parent => {
		if (parent.children) {
			parent.children.sort((a, b) => a.order - b.order);
		}
	});

	// 添加固定的首页节点
	const homeNode: NavigationNode = {
		id: 'home',
		name: '首页',
		icon: '首页',
		order: 0,
		type: '独立节点'
	};

	return [homeNode, ...rootNodes];
}

// ==================== API 服务类 ====================

/**
 * 导航 API 服务类
 */
export class NavigationApiService {
	/**
	 * 获取表结构数据
	 */
	static async getTableStructure(baseId: string): Promise<ApiResponse<TableStructureResponse>> {
		// 使用全局请求去重器防止重复请求
		const { GlobalRequestDeduplicator } = await import('./requestDeduplicator');

		return GlobalRequestDeduplicator.execute(
			`table_structure_${baseId}`,
			async () => {
				try {
					// 构建请求端点
					const endpoint = `https://noco.ohvfx.com/api/v2/meta/bases/${baseId}/tables`;

					// 构建请求头
					const headers = {
						'xc-token': NAVIGATION_API_CONFIG.token,
					};

					console.log(`[NavigationApiService] 🚀 发起表结构API请求`, {
						baseId,
						endpoint,
						timestamp: Date.now(),
						action: 'navigation_api_table_structure_request'
					});

					// 发起请求
					const response = await httpClient.get<TableStructureResponse>(
						endpoint,
						undefined, // 无查询参数
						{ headers }
					);

					// 验证响应数据格式
					if (!validateTableStructureResponse(response.data)) {
						throw createDataFormatError('表结构 API 响应数据格式不正确');
					}

					console.log(`[NavigationApiService] ✅ 表结构API请求成功`, {
						baseId,
						tablesCount: response.data.list.length,
						timestamp: Date.now(),
						action: 'navigation_api_table_structure_success'
					});

					return response;

				} catch (error) {
					// 记录错误并重新抛出
					const apiError = ApiErrorHandler.createApiError(error);
					ApiErrorHandler.logError(apiError, 'NavigationApiService.getTableStructure');
					throw apiError;
				}
			}
		);
	}

	/**
	 * 获取环节数据
	 */
	static async getSectionData(tableId: string): Promise<ApiResponse<SectionDataResponse>> {
		// 使用全局请求去重器防止重复请求
		const { GlobalRequestDeduplicator } = await import('./requestDeduplicator');

		return GlobalRequestDeduplicator.execute(
			`section_data_${tableId}`,
			async () => {
				try {
					// 构建请求端点
					const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;

					// 构建请求参数
					const params = {
						limit: 500, // 设置返回记录数限制，避免数据被分页截断
					};

					// 构建请求头
					const headers = {
						'xc-token': NAVIGATION_API_CONFIG.token,
					};

					console.log(`[NavigationApiService] 🚀 发起环节数据API请求`, {
						tableId,
						endpoint,
						timestamp: Date.now(),
						action: 'navigation_api_section_data_request'
					});

					// 发起请求
					const response = await httpClient.get<SectionDataResponse>(
						endpoint,
						params,
						{ headers }
					);

					// 验证响应数据格式
					if (!validateSectionDataResponse(response.data)) {
						throw createDataFormatError('环节数据 API 响应数据格式不正确');
					}

					// API响应数据处理 - 调试日志已移除

					console.log(`[NavigationApiService] ✅ 环节数据API请求成功`, {
						tableId,
						recordsCount: response.data.list.length,
						timestamp: Date.now(),
						action: 'navigation_api_section_data_success'
					});

					return response;

				} catch (error) {
					// 记录错误并重新抛出
					const apiError = ApiErrorHandler.createApiError(error);
					ApiErrorHandler.logError(apiError, 'NavigationApiService.getSectionData');
					throw apiError;
				}
			}
		);
	}

	/**
	 * 获取配置信息数据
	 */
	static async getConfigurationData(tableId: string): Promise<ApiResponse<ConfigurationDataResponse>> {
		try {
			// 调试：确认函数被正确调用
			console.log('[配置数据API] 🎯 getConfigurationData 函数被调用', {
				tableId,
				functionName: 'getConfigurationData',
				timestamp: Date.now()
			});

			// 构建请求端点
			const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;

			// 构建请求参数 - 过滤掉"规则介绍"类型（使用英文字段名）
			const params = {
				where: "(info_type,neq,规则介绍)",
				limit: 500, // 设置返回记录数限制，避免数据被分页截断
			};

			// 调试：记录请求参数
			console.log('[配置数据API] 🚀 即将发起请求', {
				endpoint,
				params,
				timestamp: Date.now()
			});

			// 构建请求头
			const headers = {
				'xc-token': NAVIGATION_API_CONFIG.token,
			};

			// 发起请求
			const rawResponse = await httpClient.get<any>(
				endpoint,
				params,
				{ headers }
			);

			// 处理API响应数据 - 调试日志已移除

			// 验证原始响应数据格式
			if (!rawResponse.data || !Array.isArray(rawResponse.data.list)) {
				throw createDataFormatError('配置信息 API 响应数据格式不正确');
			}

			// 使用字段映射适配器转换响应数据
			const mappingResult = FieldMapper.mapArrayFields(
				rawResponse.data.list,
				'material',
				MappingDirection.FROM_API,
				{
					keepUnmappedFields: true,
					applyDefaults: true,
					convertTypes: true,
					debug: true
				}
			);

			// 字段映射完成 - 调试日志已移除

			// 应用类型转换
			const typeConversionResult = TypeConverter.convertBatch(
				mappingResult.data,
				'material',
				{ applyDefaults: true, debug: true }
			);

			// 类型转换完成 - 调试日志已移除

			// 构建最终响应
			const response: ApiResponse<ConfigurationDataResponse> = {
				data: {
					list: typeConversionResult.data as ConfigurationDataItem[],
					pageInfo: rawResponse.data.pageInfo
				},
				status: rawResponse.status,
				statusText: rawResponse.statusText,
				headers: rawResponse.headers
			};

			// 验证转换后的响应数据格式
			if (!validateConfigurationDataResponse(response.data)) {
				console.error('[配置数据API] 数据验证失败，完整响应:', response);
				throw createDataFormatError('配置信息 API 响应数据格式不正确');
			}

			return response;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.getConfigurationData');
			throw apiError;
		}
	}

	/**
	 * 获取处理后的配置数据
	 */
	static async getProcessedConfigurationData(tableId: string): Promise<ProcessedConfigurationItem[]> {
		try {
			// 获取原始配置数据
			const response = await this.getConfigurationData(tableId);

			// 转换数据格式
			const processedItems = response.data.list.map(transformConfigurationItem);

			return processedItems;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.getProcessedConfigurationData');
			throw apiError;
		}
	}

	/**
	 * 获取分组的配置数据
	 */
	static async getGroupedConfigurationData(tableId: string): Promise<GroupedConfigurationData> {
		try {
			// 获取处理后的配置数据
			const processedItems = await this.getProcessedConfigurationData(tableId);

			// 按信息类型分组
			const groupedData = groupConfigurationData(processedItems);

			return groupedData;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.getGroupedConfigurationData');
			throw apiError;
		}
	}

	/**
	 * 获取完整的导航数据
	 */
	static async getNavigationData(baseId: string): Promise<NavigationNode[]> {
		try {
			// 第一步：获取表结构（使用全局缓存避免重复请求）
			const { GlobalTableStructureCache } = await import('./tableStructureCache');
			const tableStructureData = await GlobalTableStructureCache.getWithCache(
				baseId,
				async (id) => {
					const response = await this.getTableStructure(id);
					return response.data;
				}
			);
			const tables = tableStructureData.list;

			// 第二步：查找环节表
			const sectionTable = tables.find(table => table.title === "环节表");
			if (!sectionTable) {
				throw createDataFormatError('未找到环节表');
			}

			// 第三步：获取环节数据
			const sectionDataResponse = await this.getSectionData(sectionTable.id);
			const sectionItems = sectionDataResponse.data.list;

			// 第四步：转换数据格式
			const navigationNodes = sectionItems.map(transformSectionToNavigationNode);

			// 第五步：构建树形结构
			const navigationTree = buildNavigationTree(navigationNodes);

			return navigationTree;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.getNavigationData');
			throw apiError;
		}
	}

	/**
	 * 检查导航服务是否可用
	 */
	static async checkServiceHealth(baseId: string): Promise<boolean> {
		try {
			// 使用全局缓存检查表结构，避免重复请求
			const { GlobalTableStructureCache } = await import('./tableStructureCache');
			await GlobalTableStructureCache.getWithCache(
				baseId,
				async (id) => {
					const response = await this.getTableStructure(id);
					return response.data;
				}
			);
			return true;
		} catch (error) {
			// 记录错误但不抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.checkServiceHealth');
			return false;
		}
	}

	/**
	 * 简化的规则介绍数据获取函数
	 * 直接获取并处理数据，避免复杂的字段映射
	 */
	static async getSimpleRulesIntroductionData(tableId: string): Promise<ProcessedRulesIntroductionItem[]> {
		try {
			console.log('[简化规则介绍API] 开始获取数据', { tableId });

			// 构建请求
			const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;
			const params = {
				where: "(info_type,eq,规则介绍)",
				limit: 500,
			};
			const headers = {
				'xc-token': NAVIGATION_API_CONFIG.token,
			};

			// 发起请求
			const response = await httpClient.get<{ list: any[] }>(endpoint, params, { headers });

			// 基本验证
			if (!response.data?.list || !Array.isArray(response.data.list)) {
				console.warn('[简化规则介绍API] 响应数据格式异常，返回空数组');
				return [];
			}

			// 直接处理数据，不使用复杂的字段映射
			const processedData = response.data.list.map((item: any) => {
				// 提取音频文件URL
				const audioUrls: string[] = [];
				if (item.attachment_url && Array.isArray(item.attachment_url)) {
					item.attachment_url.forEach((attachment: any) => {
						if (attachment.signedUrl) {
							audioUrls.push(attachment.signedUrl);
						}
					});
				}

				return {
					id: item.Id || 0,
					title: item.title || '',
					content: item.content || '',
					audioUrls,
					infoType: item.info_type || '',
					createdAt: item.CreatedAt || '',
					updatedAt: item.UpdatedAt || '',
				} as ProcessedRulesIntroductionItem;
			});

			console.log('[简化规则介绍API] 数据处理完成', {
				count: processedData.length,
				titles: processedData.map(item => item.title)
			});

			return processedData;
		} catch (error) {
			console.error('[简化规则介绍API] 获取失败', error);
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'NavigationApiService.getSimpleRulesIntroductionData');
			throw apiError;
		}
	}

	/**
	 * @deprecated 使用 getSimpleRulesIntroductionData 替代
	 */
	static async getRulesIntroductionData(tableId: string): Promise<ApiResponse<RulesIntroductionApiResponse>> {
		console.warn('[规则介绍API] 此方法已废弃，请使用 getSimpleRulesIntroductionData');
		const data = await this.getSimpleRulesIntroductionData(tableId);
		return {
			success: true,
			data: { list: data as any, pageInfo: { totalRows: data.length, page: 1, pageSize: data.length, isFirstPage: true, isLastPage: true } },
			message: '规则介绍数据获取成功'
		};
	}

	/**
	 * @deprecated 使用 getSimpleRulesIntroductionData 替代
	 */
	static async getProcessedRulesIntroductionData(tableId: string): Promise<ProcessedRulesIntroductionItem[]> {
		console.warn('[处理后规则介绍API] 此方法已废弃，请使用 getSimpleRulesIntroductionData');
		return this.getSimpleRulesIntroductionData(tableId);
	}
}

// ==================== 便捷函数导出 ====================

/**
 * 获取表结构数据
 */
export const getTableStructure = NavigationApiService.getTableStructure.bind(NavigationApiService);

/**
 * 获取环节数据
 */
export const getSectionData = NavigationApiService.getSectionData.bind(NavigationApiService);

/**
 * 获取完整的导航数据
 */
export const getNavigationData = NavigationApiService.getNavigationData.bind(NavigationApiService);

/**
 * 检查导航服务健康状态
 */
export const checkNavigationServiceHealth = NavigationApiService.checkServiceHealth.bind(NavigationApiService);

/**
 * 获取配置信息数据
 */
export const getConfigurationData = NavigationApiService.getConfigurationData.bind(NavigationApiService);

/**
 * 获取处理后的配置数据
 */
export const getProcessedConfigurationData = NavigationApiService.getProcessedConfigurationData.bind(NavigationApiService);

/**
 * 获取分组的配置数据
 */
export const getGroupedConfigurationData = NavigationApiService.getGroupedConfigurationData.bind(NavigationApiService);

/**
 * 简化的规则介绍数据获取函数
 */
export const getSimpleRulesIntroductionData = NavigationApiService.getSimpleRulesIntroductionData.bind(NavigationApiService);

/**
 * @deprecated 使用 getSimpleRulesIntroductionData 替代
 */
export const getRulesIntroductionData = NavigationApiService.getRulesIntroductionData.bind(NavigationApiService);

/**
 * @deprecated 使用 getSimpleRulesIntroductionData 替代
 */
export const getProcessedRulesIntroductionData = NavigationApiService.getProcessedRulesIntroductionData.bind(NavigationApiService);

// ==================== 规则介绍相关验证函数 ====================

/**
 * 验证规则介绍API响应格式
 */
function validateRulesIntroductionApiResponse(data: unknown): data is RulesIntroductionApiResponse {
	if (!data || typeof data !== 'object') {
		console.error('[规则介绍验证] 数据不是对象:', data);
		return false;
	}

	const response = data as Record<string, unknown>;

	// 检查是否有 list 属性且为数组
	if (!Array.isArray(response.list)) {
		console.error('[规则介绍验证] list属性不存在或不是数组:', {
			hasListProperty: 'list' in response,
			listType: typeof response.list,
			responseKeys: Object.keys(response)
		});
		return false;
	}

	// 如果list为空，也是有效的
	if (response.list.length === 0) {
		console.log('[规则介绍验证] list为空数组，验证通过');
		return true;
	}

	// 检查数组中每个项目的基本结构
	const validationResults = response.list.map((item: unknown, index: number) => {
		if (!item || typeof item !== 'object') {
			console.error(`[规则介绍验证] 项目${index}不是对象:`, item);
			return false;
		}
		const rulesItem = item as Record<string, unknown>;

		const validations = {
			hasId: 'Id' in rulesItem && typeof rulesItem.Id === 'number',
			hasTitle: '标题' in rulesItem && typeof rulesItem["标题"] === 'string',
			hasContent: '内容' in rulesItem && typeof rulesItem["内容"] === 'string',
			hasInfoType: '信息类型' in rulesItem && rulesItem["信息类型"] === '规则介绍',
			hasAttachments: '附件' in rulesItem && (
				rulesItem["附件"] === null ||
				Array.isArray(rulesItem["附件"])
			)
		};

		const isValid = Object.values(validations).every(Boolean);

		if (!isValid) {
			console.error(`[规则介绍验证] 项目${index}验证失败详情:`);
			console.error(`  - Id字段: 存在=${('Id' in rulesItem)}, 类型=${typeof rulesItem.Id}, 值=${rulesItem.Id}`);
			console.error(`  - 标题字段: 存在=${('标题' in rulesItem)}, 类型=${typeof rulesItem["标题"]}, 值=${rulesItem["标题"]}`);
			console.error(`  - 内容字段: 存在=${('内容' in rulesItem)}, 类型=${typeof rulesItem["内容"]}, 值=${rulesItem["内容"]}`);
			console.error(`  - 信息类型字段: 存在=${('信息类型' in rulesItem)}, 类型=${typeof rulesItem["信息类型"]}, 值=${rulesItem["信息类型"]}`);
			console.error(`  - 附件字段: 存在=${('附件' in rulesItem)}, 类型=${typeof rulesItem["附件"]}, 值=${rulesItem["附件"]}`);
			console.error(`  - 完整项目数据:`, rulesItem);
			console.error(`  - 验证结果:`, validations);
		}

		return isValid;
	});

	const allValid = validationResults.every(Boolean);
	if (!allValid) {
		console.error('[规则介绍验证] 部分数据项验证失败');
		return false;
	}

	console.log('[规则介绍验证] 所有数据项验证通过');
	return true;
}

/**
 * 处理规则介绍原始数据
 */
function processRulesIntroductionData(rawData: RulesIntroductionApiItem[]): ProcessedRulesIntroductionItem[] {
	return rawData.map(item => {
		// 提取音频文件URL
		const audioUrls: string[] = [];
		if (item["附件"] && Array.isArray(item["附件"])) {
			item["附件"].forEach(attachment => {
				if (attachment.signedUrl) {
					audioUrls.push(attachment.signedUrl);
				}
			});
		}

		return {
			id: item.Id,
			title: item["标题"],
			content: item["内容"],
			audioUrls,
			infoType: item["信息类型"],
			createdAt: item.CreatedAt,
			updatedAt: item.UpdatedAt,
		};
	});
}
