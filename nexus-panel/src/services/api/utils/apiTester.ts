/**
 * API字段验证工具
 * 
 * 用于测试NocoDB API实际返回的字段名，与database_schema.md进行对比
 */

import { httpClient } from '../client';

// API配置
const API_CONFIG = {
    baseUrl: 'https://noco.ohvfx.com/api/v2',
    token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
    headers: {
        'xc-token': 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp'
    }
};

// 测试用的表ID（需要根据实际项目调整）
const TEST_TABLE_IDS = {
    race: 'm19dww1xfzsfipk', // 赛事表
    // 其他表ID需要通过实际API调用获取
};

/**
 * 字段验证结果接口
 */
interface FieldValidationResult {
    tableName: string;
    expectedFields: string[]; // database_schema.md中定义的字段
    actualFields: string[];   // API实际返回的字段
    missingFields: string[];  // 缺失的字段
    extraFields: string[];    // 额外的字段
    sampleData?: any;         // 示例数据
}

/**
 * 验证报告接口
 */
interface ValidationReport {
    timestamp: string;
    results: FieldValidationResult[];
    summary: {
        totalTables: number;
        tablesWithIssues: number;
        totalFieldMismatches: number;
    };
}

/**
 * 数据库Schema中定义的字段映射
 */
const SCHEMA_FIELDS = {
    session: [
        'Id', 'session_id', 'session_name', 'display_order', 
        'nav_type', 'session_icon', 'content_type', 'initial_stage', 'session_config'
    ],
    material: [
        'Id', 'info_id', 'title', 'content', 'attachment_url', 'info_type'
    ],
    question: [
        'Id', 'question_id', 'question_number', 'question_type', 'prompt', 
        'options', 'correct_answer', 'points', 'explanation', 'attachment_url',
        'session_id', 'question_pack_id', 'stage'
    ],
    player: [
        'Id', 'user_id', 'user_name', 'avatar_url', 'revival_chances'
    ],
    answer_record: [
        'Id', 'submitted_answer', 'is_correct', 'score', 'user_id', 
        'question_id', 'question_number', 'grading_type', 'session_id',
        'status', 'submission_type', 'audit_details'
    ],
    judge: [
        'Id', 'judge_id', 'judge_name'
    ],
    judge_score: [
        'Id', 'judge_score', 'user_id', 'judge_id', 'session_id'
    ],
    vote_record: [
        'Id', 'voter_id', 'voter_type', 'user_id', 'points'
    ],
    log_record: [
        'Id', 'topic', 'payload', 'status'
    ],
    final_score: [
        'Id', 'user_id', 'user_name', 'final_rank', 'final_score', 'score_details'
    ]
};

/**
 * API字段验证器类
 */
export class ApiFieldValidator {
    /**
     * 获取表结构信息
     */
    static async getTableStructure(baseId: string): Promise<any> {
        try {
            const endpoint = `${API_CONFIG.baseUrl}/meta/bases/${baseId}/tables`;
            const response = await httpClient.get(endpoint, undefined, {
                headers: API_CONFIG.headers
            });
            return response.data;
        } catch (error) {
            console.error('获取表结构失败:', error);
            throw error;
        }
    }

    /**
     * 获取表数据样本
     */
    static async getTableSample(tableId: string, limit: number = 1): Promise<any> {
        try {
            const endpoint = `${API_CONFIG.baseUrl}/tables/${tableId}/records`;
            const response = await httpClient.get(endpoint, { limit }, {
                headers: API_CONFIG.headers
            });
            return response.data;
        } catch (error) {
            console.error(`获取表${tableId}数据失败:`, error);
            throw error;
        }
    }

    /**
     * 验证单个表的字段
     */
    static async validateTableFields(
        tableName: string, 
        tableId: string, 
        expectedFields: string[]
    ): Promise<FieldValidationResult> {
        try {
            console.log(`正在验证表: ${tableName} (${tableId})`);
            
            // 获取表数据样本
            const sampleData = await this.getTableSample(tableId, 1);
            
            if (!sampleData.list || sampleData.list.length === 0) {
                console.warn(`表 ${tableName} 没有数据，无法验证字段`);
                return {
                    tableName,
                    expectedFields,
                    actualFields: [],
                    missingFields: expectedFields,
                    extraFields: [],
                    sampleData: null
                };
            }

            // 获取实际字段名
            const actualFields = Object.keys(sampleData.list[0]);
            
            // 计算差异
            const missingFields = expectedFields.filter(field => !actualFields.includes(field));
            const extraFields = actualFields.filter(field => !expectedFields.includes(field));

            console.log(`表 ${tableName} 验证完成:`, {
                expectedCount: expectedFields.length,
                actualCount: actualFields.length,
                missingCount: missingFields.length,
                extraCount: extraFields.length
            });

            return {
                tableName,
                expectedFields,
                actualFields,
                missingFields,
                extraFields,
                sampleData: sampleData.list[0]
            };

        } catch (error) {
            console.error(`验证表 ${tableName} 失败:`, error);
            throw error;
        }
    }

    /**
     * 验证所有表的字段
     */
    static async validateAllFields(baseId: string): Promise<ValidationReport> {
        try {
            console.log('开始验证API字段...');
            
            // 获取表结构
            const tableStructure = await this.getTableStructure(baseId);
            const tables = tableStructure.list || [];
            
            console.log(`找到 ${tables.length} 个表`);
            
            const results: FieldValidationResult[] = [];
            
            // 验证每个表
            for (const table of tables) {
                const tableName = table.title;
                const tableId = table.id;
                
                // 查找对应的schema字段定义
                let expectedFields: string[] = [];
                
                // 根据表名匹配schema定义
                if (tableName.includes('环节') || tableName.includes('session')) {
                    expectedFields = SCHEMA_FIELDS.session;
                } else if (tableName.includes('素材') || tableName.includes('配置')) {
                    expectedFields = SCHEMA_FIELDS.material;
                } else if (tableName.includes('题目') || tableName.includes('question')) {
                    expectedFields = SCHEMA_FIELDS.question;
                } else if (tableName.includes('选手') || tableName.includes('player')) {
                    expectedFields = SCHEMA_FIELDS.player;
                } else if (tableName.includes('答题记录') || tableName.includes('answer')) {
                    expectedFields = SCHEMA_FIELDS.answer_record;
                } else if (tableName.includes('评委表') || tableName.includes('judge') && !tableName.includes('评分')) {
                    expectedFields = SCHEMA_FIELDS.judge;
                } else if (tableName.includes('评委评分') || tableName.includes('judge_score')) {
                    expectedFields = SCHEMA_FIELDS.judge_score;
                } else if (tableName.includes('投票') || tableName.includes('vote')) {
                    expectedFields = SCHEMA_FIELDS.vote_record;
                } else if (tableName.includes('日志') || tableName.includes('log')) {
                    expectedFields = SCHEMA_FIELDS.log_record;
                } else if (tableName.includes('最终得分') || tableName.includes('final')) {
                    expectedFields = SCHEMA_FIELDS.final_score;
                }
                
                if (expectedFields.length > 0) {
                    try {
                        const result = await this.validateTableFields(tableName, tableId, expectedFields);
                        results.push(result);
                    } catch (error) {
                        console.error(`跳过表 ${tableName}:`, error);
                    }
                }
            }

            // 生成报告
            const tablesWithIssues = results.filter(r => 
                r.missingFields.length > 0 || r.extraFields.length > 0
            ).length;
            
            const totalFieldMismatches = results.reduce((sum, r) => 
                sum + r.missingFields.length + r.extraFields.length, 0
            );

            const report: ValidationReport = {
                timestamp: new Date().toISOString(),
                results,
                summary: {
                    totalTables: results.length,
                    tablesWithIssues,
                    totalFieldMismatches
                }
            };

            console.log('验证完成:', report.summary);
            return report;

        } catch (error) {
            console.error('验证过程失败:', error);
            throw error;
        }
    }

    /**
     * 生成验证报告
     */
    static generateReport(report: ValidationReport): string {
        let output = `# API字段验证报告\n\n`;
        output += `**生成时间**: ${report.timestamp}\n\n`;
        output += `## 概要\n`;
        output += `- 验证表数量: ${report.summary.totalTables}\n`;
        output += `- 存在问题的表: ${report.summary.tablesWithIssues}\n`;
        output += `- 字段不匹配总数: ${report.summary.totalFieldMismatches}\n\n`;

        output += `## 详细结果\n\n`;

        for (const result of report.results) {
            output += `### ${result.tableName}\n\n`;
            
            if (result.missingFields.length === 0 && result.extraFields.length === 0) {
                output += `✅ **字段完全匹配**\n\n`;
            } else {
                if (result.missingFields.length > 0) {
                    output += `❌ **缺失字段** (${result.missingFields.length}个):\n`;
                    result.missingFields.forEach(field => {
                        output += `- ${field}\n`;
                    });
                    output += `\n`;
                }
                
                if (result.extraFields.length > 0) {
                    output += `⚠️ **额外字段** (${result.extraFields.length}个):\n`;
                    result.extraFields.forEach(field => {
                        output += `- ${field}\n`;
                    });
                    output += `\n`;
                }
            }

            output += `**实际字段列表**:\n`;
            result.actualFields.forEach(field => {
                output += `- ${field}\n`;
            });
            output += `\n---\n\n`;
        }

        return output;
    }
}

/**
 * 便捷函数：运行完整的字段验证
 */
export async function runFieldValidation(baseId: string): Promise<string> {
    try {
        const report = await ApiFieldValidator.validateAllFields(baseId);
        return ApiFieldValidator.generateReport(report);
    } catch (error) {
        console.error('字段验证失败:', error);
        throw error;
    }
}
