/**
 * Schema验证器
 * 
 * 验证API数据是否符合database schema的定义
 */

import { STATUS_MAPPINGS } from '../adapters/fieldMappings';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingFields: string[];
  invalidFields: string[];
}

/**
 * Schema验证器类
 */
export class SchemaValidator {
  /**
   * 验证答题记录数据
   */
  static validateAnswerRecord(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingFields: string[] = [];
    const invalidFields: string[] = [];

    // 必需字段验证
    const requiredFields = ['user_id', 'question_id', 'session_id'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        missingFields.push(field);
      }
    }

    // 类型验证
    if ('user_id' in data && typeof data.user_id !== 'number') {
      invalidFields.push('user_id (应为number类型)');
    }

    if ('question_id' in data && typeof data.question_id !== 'number') {
      invalidFields.push('question_id (应为number类型)');
    }

    if ('is_correct' in data && typeof data.is_correct !== 'number') {
      invalidFields.push('is_correct (应为number类型)');
    }

    if ('score' in data && typeof data.score !== 'number') {
      invalidFields.push('score (应为number类型)');
    }

    // 状态值验证
    if ('status' in data && data.status) {
      const validStatuses = Object.keys(STATUS_MAPPINGS.answer_record_status);
      if (!validStatuses.includes(data.status)) {
        warnings.push(`未知的状态值: ${data.status}，有效值: ${validStatuses.join(', ')}`);
      }
    }

    // 判分类型验证
    if ('grading_type' in data && data.grading_type) {
      const validGradingTypes = Object.keys(STATUS_MAPPINGS.grading_type);
      if (!validGradingTypes.includes(data.grading_type)) {
        warnings.push(`未知的判分类型: ${data.grading_type}，有效值: ${validGradingTypes.join(', ')}`);
      }
    }

    // 提交类型验证
    if ('submission_type' in data && data.submission_type) {
      const validSubmissionTypes = Object.keys(STATUS_MAPPINGS.submission_type);
      if (!validSubmissionTypes.includes(data.submission_type)) {
        warnings.push(`未知的提交类型: ${data.submission_type}，有效值: ${validSubmissionTypes.join(', ')}`);
      }
    }

    return {
      isValid: missingFields.length === 0 && invalidFields.length === 0,
      errors,
      warnings,
      missingFields,
      invalidFields
    };
  }

  /**
   * 验证选手数据
   */
  static validatePlayer(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingFields: string[] = [];
    const invalidFields: string[] = [];

    // 必需字段验证
    const requiredFields = ['user_id', 'user_name'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        missingFields.push(field);
      }
    }

    // 类型验证
    if ('user_id' in data && typeof data.user_id !== 'number') {
      invalidFields.push('user_id (应为number类型)');
    }

    if ('user_name' in data && typeof data.user_name !== 'string') {
      invalidFields.push('user_name (应为string类型)');
    }

    if ('revival_chances' in data && data.revival_chances !== null && typeof data.revival_chances !== 'number') {
      invalidFields.push('revival_chances (应为number类型)');
    }

    // 新字段验证
    if ('revival_chances' in data && typeof data.revival_chances === 'number' && data.revival_chances < 0) {
      warnings.push('复活机会次数不应为负数');
    }

    return {
      isValid: missingFields.length === 0 && invalidFields.length === 0,
      errors,
      warnings,
      missingFields,
      invalidFields
    };
  }

  /**
   * 验证题目数据
   */
  static validateQuestion(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingFields: string[] = [];
    const invalidFields: string[] = [];

    // 必需字段验证
    const requiredFields = ['session_id', 'question_pack_id'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        missingFields.push(field);
      }
    }

    // 类型验证
    if ('question_id' in data && data.question_id !== null && typeof data.question_id !== 'number') {
      invalidFields.push('question_id (应为number类型)');
    }

    if ('question_number' in data && data.question_number !== null && typeof data.question_number !== 'number') {
      invalidFields.push('question_number (应为number类型)');
    }

    if ('points' in data && data.points !== null && typeof data.points !== 'number') {
      invalidFields.push('points (应为number类型)');
    }

    // 分值验证
    if ('points' in data && typeof data.points === 'number' && data.points < 0) {
      warnings.push('题目分值不应为负数');
    }

    return {
      isValid: missingFields.length === 0 && invalidFields.length === 0,
      errors,
      warnings,
      missingFields,
      invalidFields
    };
  }

  /**
   * 验证配置数据
   */
  static validateConfiguration(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingFields: string[] = [];
    const invalidFields: string[] = [];

    // 必需字段验证
    const requiredFields = ['title', 'info_type'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        missingFields.push(field);
      }
    }

    // 类型验证
    if ('info_id' in data && data.info_id !== null && typeof data.info_id !== 'number') {
      invalidFields.push('info_id (应为number类型)');
    }

    if ('title' in data && typeof data.title !== 'string') {
      invalidFields.push('title (应为string类型)');
    }

    if ('info_type' in data && typeof data.info_type !== 'string') {
      invalidFields.push('info_type (应为string类型)');
    }

    return {
      isValid: missingFields.length === 0 && invalidFields.length === 0,
      errors,
      warnings,
      missingFields,
      invalidFields
    };
  }

  /**
   * 验证环节数据
   */
  static validateSession(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingFields: string[] = [];
    const invalidFields: string[] = [];

    // 必需字段验证
    const requiredFields = ['session_name'];
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        missingFields.push(field);
      }
    }

    // 类型验证
    if ('display_order' in data && typeof data.display_order !== 'number') {
      invalidFields.push('display_order (应为number类型)');
    }

    if ('session_name' in data && typeof data.session_name !== 'string') {
      invalidFields.push('session_name (应为string类型)');
    }

    // 显示顺序验证
    if ('display_order' in data && typeof data.display_order === 'number' && data.display_order < 0) {
      warnings.push('显示顺序不应为负数');
    }

    return {
      isValid: missingFields.length === 0 && invalidFields.length === 0,
      errors,
      warnings,
      missingFields,
      invalidFields
    };
  }

  /**
   * 批量验证数据
   */
  static validateBatch(
    dataArray: Record<string, any>[],
    tableName: string
  ): {
    overallValid: boolean;
    results: ValidationResult[];
    summary: {
      totalItems: number;
      validItems: number;
      invalidItems: number;
      totalErrors: number;
      totalWarnings: number;
    };
  } {
    const results: ValidationResult[] = [];
    let validItems = 0;
    let totalErrors = 0;
    let totalWarnings = 0;

    for (const data of dataArray) {
      let result: ValidationResult;

      switch (tableName) {
        case 'answer_record':
          result = this.validateAnswerRecord(data);
          break;
        case 'player':
          result = this.validatePlayer(data);
          break;
        case 'question':
          result = this.validateQuestion(data);
          break;
        case 'material':
          result = this.validateConfiguration(data);
          break;
        case 'session':
          result = this.validateSession(data);
          break;
        default:
          result = {
            isValid: true,
            errors: [],
            warnings: [`未知的表类型: ${tableName}`],
            missingFields: [],
            invalidFields: []
          };
      }

      results.push(result);
      if (result.isValid) validItems++;
      totalErrors += result.errors.length;
      totalWarnings += result.warnings.length;
    }

    return {
      overallValid: validItems === dataArray.length,
      results,
      summary: {
        totalItems: dataArray.length,
        validItems,
        invalidItems: dataArray.length - validItems,
        totalErrors,
        totalWarnings
      }
    };
  }
}
