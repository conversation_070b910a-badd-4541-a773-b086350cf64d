/**
 * MQTT服务模块导出
 *
 * 统一导出MQTT相关的所有类型、服务和工具
 */

// 核心服务类
export { MQTTService } from "./MQTTService";
export { MQTTMemoryManager } from "./MQTTMemoryManager";

// 类型定义
export type {
	MQTTConfig,
	MQTTMessage,
	MQTTTopicStructure,
	MQTTMessageHandler,
	MQTTServiceEvents,
	MQTTMemoryConfig,
	MQTTMemoryStats,
	MQTTCleanupResult,
	SessionData,
	RankData,
	PlayerData,
	RuleData,
	PrizeData,
	RaceData,
	QuestionData,
	SystemRefreshData,
} from "./types";

// 枚举导出
export {
	MQTTDomain,
	MQTTContext,
	MQTTAction,
	MQTTTarget,
	MQTTConnectionStatus,
} from "./types";

// 导入枚举和类型以便在工具函数中使用
import {
	MQTTDomain,
	MQTTContext,
	MQTTAction,
	MQTTTarget,
	MQTTConnectionStatus,
} from "./types";
import type {
	MQTTConfig,
	MQTTTopicStructure,
	SessionData,
	RankData,
	PlayerData,
	SystemRefreshData,
} from "./types";

// 便捷工具函数
export const MQTTUtils = {
	/**
	 * 创建标准的MQTT配置
	 */
	createConfig: (brokerUrl: string, clientId?: string): MQTTConfig => ({
		brokerUrl,
		clientId: clientId || "Nexus-Panel",
		keepalive: 60,
		reconnectPeriod: 1000,
		connectTimeout: 30000,
	}),

	/**
	 * 创建Topic结构
	 */
	createTopic: (
		domain: MQTTDomain,
		context: MQTTContext,
		target: string,
		action: MQTTAction,
	): MQTTTopicStructure => ({
		domain,
		context,
		target,
		action,
	}),

	/**
	 * 解析Topic字符串为结构
	 */
	parseTopic: (topicString: string): MQTTTopicStructure | null => {
		const parts = topicString.split("/");
		if (parts.length !== 4) {
			return null;
		}

		const [domain, context, target, action] = parts;

		// 验证枚举值
		const validDomains = Object.values(MQTTDomain);
		const validContexts = Object.values(MQTTContext);
		const validActions = Object.values(MQTTAction);

		if (!validDomains.includes(domain as MQTTDomain)) {
			return null;
		}
		if (!validContexts.includes(context as MQTTContext)) {
			return null;
		}
		if (!validActions.includes(action as MQTTAction)) {
			return null;
		}

		return {
			domain: domain as MQTTDomain,
			context: context as MQTTContext,
			target,
			action: action as MQTTAction,
		};
	},

	/**
	 * 验证Topic结构
	 */
	validateTopic: (topic: MQTTTopicStructure): boolean => {
		const validDomains = Object.values(MQTTDomain);
		const validContexts = Object.values(MQTTContext);
		const validActions = Object.values(MQTTAction);

		return (
			validDomains.includes(topic.domain) &&
			validContexts.includes(topic.context) &&
			validActions.includes(topic.action) &&
			typeof topic.target === "string" &&
			topic.target.length > 0
		);
	},

	/**
	 * 格式化连接状态为中文
	 */
	formatConnectionStatus: (status: MQTTConnectionStatus): string => {
		const statusMap: Record<MQTTConnectionStatus, string> = {
			[MQTTConnectionStatus.DISCONNECTED]: "未连接",
			[MQTTConnectionStatus.CONNECTING]: "连接中",
			[MQTTConnectionStatus.CONNECTED]: "已连接",
			[MQTTConnectionStatus.RECONNECTING]: "重连中",
			[MQTTConnectionStatus.ERROR]: "连接错误",
		};
		return statusMap[status] || "未知状态";
	},

	/**
	 * 生成唯一的客户端ID
	 */
	generateClientId: (prefix = "nexus-panel"): string => {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 8);
		return `${prefix}-${timestamp}-${random}`;
	},

	/**
	 * 检查Topic是否匹配通配符模式
	 */
	matchesTopic: (actualTopic: string, pattern: string): boolean => {
		// 完全匹配
		if (actualTopic === pattern) {
			return true;
		}

		// 多级通配符 #
		if (pattern.includes("#")) {
			const prefix = pattern.replace("/#", "").replace("#", "");
			return actualTopic.startsWith(prefix);
		}

		// 单级通配符 +
		if (pattern.includes("+")) {
			const regexPattern = pattern.replace(/\+/g, "[^/]+");
			const regex = new RegExp(`^${regexPattern}$`);
			return regex.test(actualTopic);
		}

		return false;
	},
};

// 预定义的常用配置
export const MQTTPresets = {
	/**
	 * 开发环境配置
	 */
	development: {
		brokerUrl: "wss://ws.ohvfx.com:8084/mqtt",
		clientId: "Nexus-Panel",
		username: "1001",
		password: "1001",
		keepalive: 60,
		reconnectPeriod: 10000, // 增加到10秒避免频繁重连
		connectTimeout: 15000,
	},

	/**
	 * 生产环境配置
	 */
	production: {
		brokerUrl: "wss://ws.ohvfx.com:8084/mqtt",
		clientId: "Nexus-Panel",
		username: "1001",
		password: "1001",
		keepalive: 60,
		reconnectPeriod: 15000, // 增加到15秒避免频繁重连
		connectTimeout: 30000,
	},

	/**
	 * 测试环境配置
	 */
	testing: {
		brokerUrl: "wss://ws.ohvfx.com:8084/mqtt",
		clientId: "Nexus-Panel",
		username: "1001",
		password: "1001",
		keepalive: 45,
		reconnectPeriod: 3000,
		connectTimeout: 15000,
	},
};

// 常用的消息模板
export const MessageTemplates = {
	/**
	 * 创建环节开始消息
	 */
	createSessionStart: (
		sessionType: string,
		sessionName: string,
	): { topic: MQTTTopicStructure; data: SessionData } => ({
		topic: MQTTUtils.createTopic(
			MQTTDomain.QUIZ,
			MQTTContext.SESSION,
			MQTTTarget.ALL,
			MQTTAction.START,
		),
		data: {
			sessionType,
			sessionId: Date.now(),
			sessionName,
			config: {},
		},
	}),

	/**
	 * 创建排行榜显示消息
	 */
	createRankDisplay: (
		rankType: "general" | "speedrun" | "speedrun-plus",
	): { topic: MQTTTopicStructure; data: RankData } => ({
		topic: MQTTUtils.createTopic(
			MQTTDomain.DISPLAY,
			MQTTContext.RANK,
			MQTTTarget.SCREEN,
			MQTTAction.SHOW,
		),
		data: { rankType },
	}),

	/**
	 * 创建选手显示消息
	 */
	createPlayerDisplay: (
		playerId: number,
		playerName: string,
	): { topic: MQTTTopicStructure; data: PlayerData } => ({
		topic: MQTTUtils.createTopic(
			MQTTDomain.DISPLAY,
			MQTTContext.PLAYER,
			MQTTTarget.SCREEN,
			MQTTAction.SHOW,
		),
		data: {
			playerId,
			playerName,
			position: playerId,
			status: "active",
		},
	}),

	/**
	 * 创建系统刷新消息
	 */
	createSystemRefresh: (): {
		topic: MQTTTopicStructure;
		data: SystemRefreshData;
	} => ({
		topic: MQTTUtils.createTopic(
			MQTTDomain.SYSTEM,
			MQTTContext.CLIENT,
			MQTTTarget.ALL,
			MQTTAction.REFRESH,
		),
		data: { refreshType: "full", timestamp: Date.now() },
	}),
};
