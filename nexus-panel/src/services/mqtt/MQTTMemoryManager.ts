/**
 * MQTT 内存管理器
 * 
 * 提供智能的内存泄漏防护机制，包括：
 * - 定期清理过期的消息处理器和订阅
 * - 监控内存使用情况和资源状态
 * - 智能阈值检测和自动清理
 * - 详细的清理日志和统计报告
 */

import { MQTTService } from './MQTTService';

/**
 * 内存管理配置接口
 */
export interface MQTTMemoryConfig {
	/** 定期清理间隔（毫秒），默认30分钟 */
	cleanupInterval?: number;
	/** 消息处理器数量阈值，默认50 */
	handlerThreshold?: number;
	/** 控制台日志数量阈值，默认1000 */
	logThreshold?: number;
	/** 处理器过期时间（毫秒），默认1小时 */
	handlerExpireTime?: number;
	/** 是否启用调试日志，默认false */
	debug?: boolean;
}

/**
 * 内存使用统计接口
 */
export interface MQTTMemoryStats {
	/** 当前消息处理器数量 */
	handlerCount: number;
	/** 当前订阅数量 */
	subscriptionCount: number;
	/** 控制台日志数量 */
	logCount: number;
	/** 最后清理时间 */
	lastCleanupTime: Date | null;
	/** 总清理次数 */
	totalCleanups: number;
	/** 内存健康状态 */
	healthStatus: 'healthy' | 'warning' | 'critical';
}

/**
 * 清理操作结果接口
 */
export interface MQTTCleanupResult {
	/** 清理时间戳 */
	timestamp: Date;
	/** 清理的处理器数量 */
	handlersRemoved: number;
	/** 清理的订阅数量 */
	subscriptionsRemoved: number;
	/** 清理的日志数量 */
	logsRemoved: number;
	/** 清理耗时（毫秒） */
	duration: number;
	/** 清理原因 */
	reason: 'scheduled' | 'threshold' | 'manual';
}

/**
 * 处理器使用统计
 */
interface HandlerUsageStats {
	/** 最后使用时间 */
	lastUsed: Date;
	/** 使用次数 */
	usageCount: number;
	/** 创建时间 */
	createdAt: Date;
}

/**
 * MQTT 内存管理器类
 */
export class MQTTMemoryManager {
	private config: Required<MQTTMemoryConfig>;
	private mqttService: MQTTService | null = null;
	private cleanupTimer: NodeJS.Timeout | null = null;
	private handlerUsageStats: Map<string, HandlerUsageStats> = new Map();
	private cleanupHistory: MQTTCleanupResult[] = [];
	private logCountCallback: (() => number) | null = null;
	private logCleanupCallback: ((count: number) => void) | null = null;

	constructor(config: MQTTMemoryConfig = {}) {
		this.config = {
			cleanupInterval: config.cleanupInterval ?? 30 * 60 * 1000, // 30分钟
			handlerThreshold: config.handlerThreshold ?? 50,
			logThreshold: config.logThreshold ?? 1000,
			handlerExpireTime: config.handlerExpireTime ?? 60 * 60 * 1000, // 1小时
			debug: config.debug ?? false,
		};

		this.log('内存管理器已初始化', this.config);
	}

	/**
	 * 启动内存管理器
	 */
	start(mqttService: MQTTService): void {
		this.mqttService = mqttService;
		this.startPeriodicCleanup();
		this.log('内存管理器已启动');
	}

	/**
	 * 停止内存管理器
	 */
	stop(): void {
		this.stopPeriodicCleanup();
		this.mqttService = null;
		this.handlerUsageStats.clear();
		this.log('内存管理器已停止');
	}

	/**
	 * 设置日志计数回调
	 */
	setLogCountCallback(callback: () => number): void {
		this.logCountCallback = callback;
	}

	/**
	 * 设置日志清理回调
	 */
	setLogCleanupCallback(callback: (count: number) => void): void {
		this.logCleanupCallback = callback;
	}

	/**
	 * 记录处理器使用情况
	 */
	recordHandlerUsage(topic: string): void {
		const stats = this.handlerUsageStats.get(topic) || {
			lastUsed: new Date(),
			usageCount: 0,
			createdAt: new Date(),
		};

		stats.lastUsed = new Date();
		stats.usageCount++;
		this.handlerUsageStats.set(topic, stats);
	}

	/**
	 * 获取内存使用统计
	 */
	getMemoryStats(): MQTTMemoryStats {
		const handlerCount = this.mqttService?.getHandlerCount() ?? 0;
		const subscriptionCount = this.mqttService?.getSubscriptionCount() ?? 0;
		const logCount = this.logCountCallback?.() ?? 0;

		const healthStatus = this.calculateHealthStatus(handlerCount, logCount);

		return {
			handlerCount,
			subscriptionCount,
			logCount,
			lastCleanupTime: this.getLastCleanupTime(),
			totalCleanups: this.cleanupHistory.length,
			healthStatus,
		};
	}

	/**
	 * 手动执行清理
	 */
	async manualCleanup(): Promise<MQTTCleanupResult> {
		this.log('开始手动清理');
		return this.performCleanup('manual');
	}

	/**
	 * 获取清理历史（最近10次）
	 */
	getCleanupHistory(): MQTTCleanupResult[] {
		return this.cleanupHistory.slice(-10);
	}

	/**
	 * 启动定期清理
	 */
	private startPeriodicCleanup(): void {
		this.stopPeriodicCleanup();
		this.cleanupTimer = setInterval(() => {
			this.performCleanup('scheduled').catch((error) => {
				console.error('定期清理失败:', error);
			});
		}, this.config.cleanupInterval);
	}

	/**
	 * 停止定期清理
	 */
	private stopPeriodicCleanup(): void {
		if (this.cleanupTimer) {
			clearInterval(this.cleanupTimer);
			this.cleanupTimer = null;
		}
	}

	/**
	 * 执行清理操作
	 */
	private async performCleanup(reason: MQTTCleanupResult['reason']): Promise<MQTTCleanupResult> {
		const startTime = Date.now();
		let handlersRemoved = 0;
		let subscriptionsRemoved = 0;
		let logsRemoved = 0;

		try {
			// 清理过期的处理器使用统计
			handlersRemoved = this.cleanupExpiredHandlers();

			// 清理 MQTT 服务中的过期资源
			if (this.mqttService) {
				subscriptionsRemoved = await this.cleanupMQTTResources();
			}

			// 清理控制台日志
			logsRemoved = this.cleanupConsoleLogs();

			const result: MQTTCleanupResult = {
				timestamp: new Date(),
				handlersRemoved,
				subscriptionsRemoved,
				logsRemoved,
				duration: Date.now() - startTime,
				reason,
			};

			this.cleanupHistory.push(result);
			this.log('清理完成', result);

			return result;
		} catch (error) {
			console.error('清理操作失败:', error);
			throw error;
		}
	}

	/**
	 * 清理过期的处理器统计
	 */
	private cleanupExpiredHandlers(): number {
		const now = new Date();
		const expireTime = this.config.handlerExpireTime;
		let removed = 0;

		for (const [topic, stats] of this.handlerUsageStats.entries()) {
			if (now.getTime() - stats.lastUsed.getTime() > expireTime) {
				this.handlerUsageStats.delete(topic);
				removed++;
			}
		}

		return removed;
	}

	/**
	 * 清理 MQTT 服务资源
	 */
	private async cleanupMQTTResources(): Promise<number> {
		// 这里可以添加更多的 MQTT 资源清理逻辑
		// 目前主要依赖 MQTTService 自身的清理机制
		return 0;
	}

	/**
	 * 清理控制台日志
	 */
	private cleanupConsoleLogs(): number {
		const currentLogCount = this.logCountCallback?.() ?? 0;

		if (currentLogCount > this.config.logThreshold) {
			const logsToRemove = Math.floor(currentLogCount * 0.3); // 清理30%的日志
			this.logCleanupCallback?.(logsToRemove);
			return logsToRemove;
		}

		return 0;
	}

	/**
	 * 计算健康状态
	 */
	private calculateHealthStatus(handlerCount: number, logCount: number): MQTTMemoryStats['healthStatus'] {
		if (handlerCount > this.config.handlerThreshold || logCount > this.config.logThreshold) {
			return 'critical';
		}

		if (handlerCount > this.config.handlerThreshold * 0.7 || logCount > this.config.logThreshold * 0.7) {
			return 'warning';
		}

		return 'healthy';
	}

	/**
	 * 获取最后清理时间
	 */
	private getLastCleanupTime(): Date | null {
		return this.cleanupHistory.length > 0
			? this.cleanupHistory[this.cleanupHistory.length - 1].timestamp
			: null;
	}

	/**
	 * 调试日志
	 */
	private log(message: string, data?: unknown): void {
		if (this.config.debug) {
			console.log(`[MQTTMemoryManager] ${message}`, data || '');
		}
	}
}
