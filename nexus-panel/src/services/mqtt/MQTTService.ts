/**
 * MQTT服务核心实现
 *
 * 提供MQTT连接管理、消息发布订阅、Topic构建等核心功能
 */

import mqtt from "mqtt";
import type {
	MqttClient,
	IClientOptions,
	IClientPublishOptions,
} from "mqtt";
import type { QoS } from "mqtt-packet";
import {
	type MQTTConfig,
	type MQTTMessage,
	type MQTTTopicStructure,
	type MQTTMessageHandler,
	MQTTConnectionStatus,
	type MQTTServiceEvents,
	type MQTTMessageData,
} from "./types";

export class MQTTService {
	private client: MqttClient | null = null;
	private config: MQTTConfig;
	private messageHandlers: Map<string, MQTTMessageHandler[]> = new Map();
	private eventHandlers: Partial<MQTTServiceEvents> = {};
	private connectionStatus: MQTTConnectionStatus =
		MQTTConnectionStatus.DISCONNECTED;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectTimer: NodeJS.Timeout | null = null;
	private isManualDisconnect = false; // 标记是否为手动断开

	constructor(config: MQTTConfig) {
		this.config = {
			keepalive: 60,
			reconnectPeriod: 1000,
			connectTimeout: 30000,
			...config,
		};
	}

	/**
	 * 连接到MQTT代理服务器
	 */
	async connect(): Promise<void> {
		if (
			this.client &&
			this.connectionStatus === MQTTConnectionStatus.CONNECTED
		) {
			console.warn("MQTT客户端已连接");
			return;
		}

		// 防止重复连接请求
		if (this.connectionStatus === MQTTConnectionStatus.CONNECTING) {
			console.warn("MQTT正在连接中，跳过重复连接请求");
			return;
		}

		this.setConnectionStatus(MQTTConnectionStatus.CONNECTING);

		const options: IClientOptions = {
			clientId: this.config.clientId,
			username: this.config.username,
			password: this.config.password,
			keepalive: this.config.keepalive,
			// 禁用自动重连，使用我们自己的智能重连逻辑
			reconnectPeriod: 0, // 禁用自动重连
			connectTimeout: this.config.connectTimeout,
			clean: true,
			rejectUnauthorized: false, // 开发环境可以设置为false
		};

		return new Promise((resolve, reject) => {
			try {
				this.client = mqtt.connect(this.config.brokerUrl, options);

				// 连接成功事件
				this.client.on("connect", () => {
					this.setConnectionStatus(MQTTConnectionStatus.CONNECTED);
					this.reconnectAttempts = 0; // 重置重连计数
					this.isManualDisconnect = false;
					// 清除重连定时器
					if (this.reconnectTimer) {
						clearTimeout(this.reconnectTimer);
						this.reconnectTimer = null;
					}
					resolve();
				});

				// 连接错误事件
				this.client.on("error", (error) => {
					const errorDetails = this.analyzeConnectionError(error);
					console.error("MQTT连接错误:", errorDetails);
					this.setConnectionStatus(MQTTConnectionStatus.ERROR);
					this.eventHandlers.error?.(error);

					// 如果不是手动断开，尝试智能重连
					if (!this.isManualDisconnect) {
						this.scheduleReconnect();
					}

					reject(error);
				});

				// 断开连接事件
				this.client.on("close", () => {
					this.setConnectionStatus(MQTTConnectionStatus.DISCONNECTED);

					// 如果不是手动断开，尝试智能重连
					if (!this.isManualDisconnect) {
						this.scheduleReconnect();
					}
				});

				// 消息接收事件
				this.client.on("message", this.handleMessage.bind(this));

				// 离线事件
				this.client.on("offline", () => {
					this.setConnectionStatus(MQTTConnectionStatus.DISCONNECTED);
				});
			} catch (error) {
				console.error("MQTT连接初始化失败:", error);
				this.setConnectionStatus(MQTTConnectionStatus.ERROR);
				reject(error);
			}
		});
	}

	/**
	 * 构建Topic字符串
	 */
	buildTopic(structure: MQTTTopicStructure): string {
		return `${structure.domain}/${structure.context}/${structure.target}/${structure.action}`;
	}

	/**
	 * 发布消息
	 */
	publish(
		topicStructure: MQTTTopicStructure,
		data: MQTTMessageData,
		options?: { qos?: QoS; retain?: boolean },
	): void {
		if (
			!this.client ||
			this.connectionStatus !== MQTTConnectionStatus.CONNECTED
		) {
			throw new Error("MQTT客户端未连接，无法发布消息");
		}

		const topic = this.buildTopic(topicStructure);
		const message: MQTTMessage = {
			timestamp: Date.now(),
			sender: this.config.clientId,
			version: "1.0",
			data,
		};

		const publishOptions: IClientPublishOptions = {
			qos: options?.qos || 0,
			retain: options?.retain || false,
		};

		this.client.publish(
			topic,
			JSON.stringify(message),
			publishOptions,
			(error) => {
				if (error) {
					console.error(`消息发布失败 [${topic}]:`, error);
				} else {
					// 仅在调试模式下输出底层发布日志，避免与应用层日志重复
					if (process.env.NODE_ENV === 'development') {
						console.log(`[MQTT Service] 消息发布成功 [${topic}]:`, data);
					}
				}
			},
		);
	}

	/**
	 * 订阅Topic
	 */
	subscribe(
		topic: string,
		handler: MQTTMessageHandler,
		options?: { qos?: QoS },
	): void {
		if (
			!this.client ||
			this.connectionStatus !== MQTTConnectionStatus.CONNECTED
		) {
			throw new Error("MQTT客户端未连接，无法订阅");
		}

		const subscribeOptions = {
			qos: (options?.qos || 0) as QoS,
		};

		this.client.subscribe(topic, subscribeOptions, (error) => {
			if (error) {
				console.error(`订阅失败 [${topic}]:`, error);
			} else {
				// 添加消息处理器
				if (!this.messageHandlers.has(topic)) {
					this.messageHandlers.set(topic, []);
				}
				this.messageHandlers.get(topic)!.push(handler);

				this.eventHandlers.subscribed?.(topic);
			}
		});
	}

	/**
	 * 取消订阅
	 */
	unsubscribe(topic: string, handler?: MQTTMessageHandler): void {
		if (!this.client) {
			return;
		}

		if (handler) {
			// 移除特定处理器
			const handlers = this.messageHandlers.get(topic);
			if (handlers) {
				const index = handlers.indexOf(handler);
				if (index > -1) {
					handlers.splice(index, 1);
				}

				// 如果没有处理器了，取消订阅
				if (handlers.length === 0) {
					this.messageHandlers.delete(topic);
					this.client.unsubscribe(topic);
				}
			}
		} else {
			// 移除所有处理器并取消订阅
			this.messageHandlers.delete(topic);
			this.client.unsubscribe(topic, (error) => {
				if (error) {
					console.error(`取消订阅失败 [${topic}]:`, error);
				} else {
					this.eventHandlers.unsubscribed?.(topic);
				}
			});
		}
	}

	/**
	 * 处理接收到的消息
	 */
	private handleMessage(topic: string, payload: Buffer): void {
		try {
			const message: MQTTMessage = JSON.parse(payload.toString());

			// 仅在调试模式下输出底层消息日志，避免与应用层日志重复
			if (process.env.NODE_ENV === 'development') {
				console.log(`[MQTT Service] 收到消息 [${topic}]:`, message);
			}

			// 触发全局消息事件
			this.eventHandlers.message?.(topic, message);

			// 查找匹配的处理器
			for (const [subscribedTopic, handlers] of this.messageHandlers) {
				if (this.topicMatches(topic, subscribedTopic)) {
					handlers.forEach((handler) => {
						try {
							handler(message, topic);
						} catch (error) {
							console.error(`消息处理器执行错误 [${topic}]:`, error);
						}
					});
				}
			}
		} catch (error) {
			console.error(`消息解析错误 [${topic}]:`, error);
		}
	}

	/**
	 * Topic匹配逻辑（支持MQTT通配符）
	 */
	private topicMatches(actualTopic: string, subscribedTopic: string): boolean {
		// 完全匹配
		if (actualTopic === subscribedTopic) {
			return true;
		}

		// 多级通配符 #
		if (subscribedTopic.includes("#")) {
			const prefix = subscribedTopic.replace("/#", "").replace("#", "");
			return actualTopic.startsWith(prefix);
		}

		// 单级通配符 +
		if (subscribedTopic.includes("+")) {
			const pattern = subscribedTopic.replace(/\+/g, "[^/]+");
			const regex = new RegExp(`^${pattern}$`);
			return regex.test(actualTopic);
		}

		return false;
	}

	/**
	 * 设置连接状态
	 */
	private setConnectionStatus(status: MQTTConnectionStatus): void {
		if (this.connectionStatus !== status) {
			this.connectionStatus = status;
			this.eventHandlers.statusChange?.(status);
		}
	}

	/**
	 * 注册事件处理器
	 */
	on<K extends keyof MQTTServiceEvents>(
		event: K,
		handler: MQTTServiceEvents[K],
	): void {
		this.eventHandlers[event] = handler;
	}

	/**
	 * 获取连接状态
	 */
	getConnectionStatus(): MQTTConnectionStatus {
		return this.connectionStatus;
	}

	/**
	 * 检查是否已连接
	 */
	isConnected(): boolean {
		return this.connectionStatus === MQTTConnectionStatus.CONNECTED;
	}

	/**
	 * 断开连接
	 */
	disconnect(): void {
		this.isManualDisconnect = true; // 标记为手动断开

		// 清除重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		if (this.client) {
			this.client.end(true);
			this.client = null;
			this.setConnectionStatus(MQTTConnectionStatus.DISCONNECTED);
			this.messageHandlers.clear();
		}

		// 重置重连计数
		this.reconnectAttempts = 0;
	}

	/**
	 * 手动重连（重置重连计数）
	 */
	async reconnect(): Promise<void> {
		// 先断开现有连接
		this.disconnect();

		// 重置状态
		this.isManualDisconnect = false;
		this.reconnectAttempts = 0;

		// 等待一小段时间确保断开完成
		await new Promise(resolve => setTimeout(resolve, 1000));

		// 开始连接
		return this.connect();
	}

	/**
	 * 获取客户端信息
	 */
	getClientInfo(): {
		clientId: string;
		brokerUrl: string;
		status: MQTTConnectionStatus;
	} {
		return {
			clientId: this.config.clientId,
			brokerUrl: this.config.brokerUrl,
			status: this.connectionStatus,
		};
	}

	/**
	 * 获取消息处理器数量（用于内存管理）
	 */
	getHandlerCount(): number {
		let totalHandlers = 0;
		for (const handlers of this.messageHandlers.values()) {
			totalHandlers += handlers.length;
		}
		return totalHandlers;
	}

	/**
	 * 获取订阅数量（用于内存管理）
	 */
	getSubscriptionCount(): number {
		return this.messageHandlers.size;
	}

	/**
	 * 获取所有订阅的主题列表（用于内存管理）
	 */
	getSubscribedTopics(): string[] {
		return Array.from(this.messageHandlers.keys());
	}

	/**
	 * 清理指定主题的所有处理器（用于内存管理）
	 */
	cleanupTopicHandlers(topic: string): number {
		const handlers = this.messageHandlers.get(topic);
		if (handlers) {
			const count = handlers.length;
			this.messageHandlers.delete(topic);

			// 如果客户端存在，取消订阅
			if (this.client) {
				this.client.unsubscribe(topic, (error) => {
					if (error) {
						console.error(`清理订阅失败 [${topic}]:`, error);
					} else {
						// 清理订阅成功
					}
				});
			}

			return count;
		}
		return 0;
	}

	/**
	 * 获取内存使用统计（用于内存管理）
	 */
	getMemoryUsage(): {
		handlerCount: number;
		subscriptionCount: number;
		subscribedTopics: string[];
		connectionStatus: MQTTConnectionStatus;
		reconnectAttempts: number;
	} {
		return {
			handlerCount: this.getHandlerCount(),
			subscriptionCount: this.getSubscriptionCount(),
			subscribedTopics: this.getSubscribedTopics(),
			connectionStatus: this.connectionStatus,
			reconnectAttempts: this.reconnectAttempts,
		};
	}

	/**
	 * 计算指数退避重连延迟时间
	 * @param attempt 重连尝试次数（从0开始）
	 * @returns 延迟时间（毫秒）
	 */
	private calculateReconnectDelay(attempt: number): number {
		const baseDelay = 1000; // 基础延迟1秒
		const maxDelay = 30000; // 最大延迟30秒
		const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
		return delay + Math.random() * 1000; // 添加随机抖动0-1秒
	}

	/**
	 * 安排智能重连
	 */
	private scheduleReconnect(): void {
		// 清除现有的重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		// 检查是否已达到最大重连次数
		if (this.reconnectAttempts >= this.maxReconnectAttempts) {
			console.error(`MQTT重连次数已达到最大限制(${this.maxReconnectAttempts})，停止重连以避免被服务器拉黑`);
			this.setConnectionStatus(MQTTConnectionStatus.ERROR);
			return;
		}

		// 计算延迟时间
		const delay = this.calculateReconnectDelay(this.reconnectAttempts);

		this.setConnectionStatus(MQTTConnectionStatus.RECONNECTING);

		// 设置重连定时器
		this.reconnectTimer = setTimeout(async () => {
			this.reconnectAttempts++;
			try {
				await this.connect();
			} catch (error) {
				console.error(`第${this.reconnectAttempts}次重连失败:`, error);
				// 连接失败会触发error事件，进而再次调用scheduleReconnect
			}
		}, delay);
	}

	/**
	 * 分析连接错误并提供详细信息
	 */
	private analyzeConnectionError(error: Error | unknown): {
		type: string;
		message: string;
		details: string;
		suggestions: string[];
	} {
		// 类型安全的错误属性提取
		const errorObj = error as { message?: string; code?: string | number; reasonCode?: number };
		const errorMessage = errorObj.message || String(error);
		const errorCode = errorObj.code;
		const reasonCode = errorObj.reasonCode;

		// 分析不同类型的错误
		if (errorMessage.includes("Connection refused: Not authorized") || reasonCode === 5) {
			return {
				type: "认证失败",
				message: "MQTT服务器拒绝连接：认证失败",
				details: `用户名: ${this.config.username}, 客户端ID: ${this.config.clientId}`,
				suggestions: [
					"检查用户名和密码是否正确",
					"确认客户端ID是否被其他连接占用",
					"联系服务器管理员确认账户状态"
				]
			};
		}

		if (errorMessage.includes("Connection refused: Server unavailable") || reasonCode === 3) {
			return {
				type: "服务器不可用",
				message: "MQTT服务器当前不可用",
				details: `服务器地址: ${this.config.brokerUrl}`,
				suggestions: [
					"检查网络连接",
					"确认服务器地址是否正确",
					"稍后重试连接"
				]
			};
		}

		if (errorMessage.includes("Connection refused: Bad username or password") || reasonCode === 4) {
			return {
				type: "用户名或密码错误",
				message: "用户名或密码不正确",
				details: `用户名: ${this.config.username}`,
				suggestions: [
					"检查用户名拼写",
					"检查密码是否正确",
					"确认账户是否已激活"
				]
			};
		}

		if (errorMessage.includes("ENOTFOUND") || errorMessage.includes("getaddrinfo")) {
			return {
				type: "DNS解析失败",
				message: "无法解析服务器地址",
				details: `服务器地址: ${this.config.brokerUrl}`,
				suggestions: [
					"检查网络连接",
					"确认服务器地址拼写正确",
					"尝试使用IP地址代替域名"
				]
			};
		}

		if (errorMessage.includes("ECONNREFUSED")) {
			return {
				type: "连接被拒绝",
				message: "服务器拒绝连接",
				details: `服务器地址: ${this.config.brokerUrl}`,
				suggestions: [
					"检查服务器是否运行",
					"确认端口号是否正确",
					"检查防火墙设置"
				]
			};
		}

		if (errorMessage.includes("timeout")) {
			return {
				type: "连接超时",
				message: "连接服务器超时",
				details: `超时时间: ${this.config.connectTimeout}ms`,
				suggestions: [
					"检查网络连接速度",
					"增加连接超时时间",
					"稍后重试"
				]
			};
		}

		// 默认错误处理
		return {
			type: "未知错误",
			message: errorMessage,
			details: `错误代码: ${errorCode || 'N/A'}, 原因代码: ${reasonCode || 'N/A'}`,
			suggestions: [
				"检查网络连接",
				"确认服务器配置",
				"查看详细错误日志"
			]
		};
	}
}
