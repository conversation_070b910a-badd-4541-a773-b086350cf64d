/* ===== 鼠标样式统一管理 ===== */

:root {
  --cursor-default: default;
  --cursor-pointer: pointer;
  --cursor-not-allowed: not-allowed;
  --cursor-grab: grab;
  --cursor-grabbing: grabbing;
  --cursor-text: text;
  --cursor-crosshair: crosshair;
}

/* ===== 基础交互元素样式 ===== */

/* 可点击元素 */
.clickable,
.spectrum-Button:not(:disabled),
.spectrum-ActionButton:not(:disabled),
.spectrum-Picker:not(:disabled),
.spectrum-Menu-item:not(:disabled),
.spectrum-TreeView-item[role="treeitem"]:not([aria-disabled="true"]) {
  cursor: var(--cursor-pointer);
  transition: cursor 0.15s ease, opacity 0.15s ease;
}

/* 禁用元素 */
.disabled,
[disabled],
[aria-disabled="true"] {
  cursor: var(--cursor-not-allowed);
  pointer-events: auto; /* 保持基本的鼠标事件 */
}

/* 文本输入区域 */
input[type="text"],
textarea,
.spectrum-Textfield-input {
  cursor: var(--cursor-text);
}

/* 拖拽元素 */
.draggable {
  cursor: var(--cursor-grab);
}

.draggable:active {
  cursor: var(--cursor-grabbing);
}

/* ===== React Spectrum 组件增强 ===== */

/* 按钮组件 */
.spectrum-Button:hover:not(:disabled) {
  cursor: var(--cursor-pointer);
}

.spectrum-ActionButton:hover:not(:disabled) {
  cursor: var(--cursor-pointer);
}

/* 选择器组件 */
.spectrum-Picker:hover:not(:disabled) {
  cursor: var(--cursor-pointer);
}

/* 树形视图 */
.spectrum-TreeView-item:hover:not([aria-disabled="true"]) {
  cursor: var(--cursor-pointer);
}

/* ===== 自定义组件增强 ===== */

/* 侧边栏按钮组 */
.sidebar-button-group .spectrum-Button:hover:not(:disabled) {
  cursor: var(--cursor-pointer);
}

/* 设备状态指示器 */
.device-status-item:hover:not(.disabled) {
  cursor: var(--cursor-pointer);
}

/* 导航树节点 */
.navigation-tree-view .spectrum-TreeView-item:hover {
  cursor: var(--cursor-pointer);
}

/* ===== 响应式适配 ===== */

@media (max-width: 768px) {
  /* 移动端触摸优化 */
  .clickable,
  .spectrum-Button:not(:disabled),
  .spectrum-ActionButton:not(:disabled) {
    cursor: var(--cursor-pointer);
    /* 移动端不需要悬停效果 */
  }
}

/* ===== 无障碍访问增强 ===== */

/* 键盘焦点指示器 */
.interactive-element:focus-visible {
  cursor: var(--cursor-pointer);
  outline: 2px solid var(--spectrum-global-color-blue-500);
  outline-offset: 2px;
}

/* 高对比模式适配 */
@media (prefers-contrast: high) {
  .clickable:hover {
    cursor: var(--cursor-pointer);
    outline: 1px solid currentColor;
  }
}

/* ===== 动画过渡 ===== */

.cursor-transition {
  transition: cursor 0.2s ease, 
              opacity 0.2s ease,
              transform 0.2s ease;
}

/* ===== 实用工具类 ===== */

.cursor-pointer { cursor: var(--cursor-pointer); }
.cursor-default { cursor: var(--cursor-default); }
.cursor-not-allowed { cursor: var(--cursor-not-allowed); }
.cursor-text { cursor: var(--cursor-text); }
.cursor-grab { cursor: var(--cursor-grab); }
.cursor-grabbing { cursor: var(--cursor-grabbing); }

/* ===== 防止样式冲突 ===== */

/* 确保React Spectrum组件的优先级 */
.spectrum-Button,
.spectrum-ActionButton,
.spectrum-Picker {
  /* 避免被全局样式覆盖 */
  cursor: var(--cursor-pointer) !important;
}

.spectrum-Button:disabled,
.spectrum-ActionButton:disabled,
.spectrum-Picker:disabled {
  cursor: var(--cursor-not-allowed) !important;
}