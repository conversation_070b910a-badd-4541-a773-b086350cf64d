/**
 * 统一请求管理器
 * 
 * 用于管理争分夺秒环节的API请求，实现请求去重、队列管理和防抖机制
 */

import type { RankingData } from '../services/api/types';

// 请求类型常量
const RequestType = {
  SECTION_RANKING: 'section_ranking',
  PLAYER_LIST: 'player_list',
  TABLE_STRUCTURE: 'table_structure'
} as const;

// 请求优先级常量
const RequestPriority = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  URGENT: 4
} as const;

// 请求状态常量
const RequestStatus = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

// 类型定义
type RequestType = typeof RequestType[keyof typeof RequestType];
type RequestPriority = typeof RequestPriority[keyof typeof RequestPriority];
type RequestStatus = typeof RequestStatus[keyof typeof RequestStatus];

// 导出常量和类型
export { RequestType, RequestPriority, RequestStatus };

// 请求项接口
export interface RequestItem<T = unknown> {
  id: string;
  type: RequestType;
  priority: RequestPriority;
  status: RequestStatus;
  params: Record<string, unknown>;
  timestamp: number;
  promise?: Promise<T>;
  resolve?: (value: T) => void;
  reject?: (error: Error) => void;
  retryCount: number;
  maxRetries: number;
}

// 请求管理器配置
export interface RequestManagerConfig {
  debounceTime: number;
  maxConcurrentRequests: number;
  defaultRetries: number;
  cacheTimeout: number;
}

// 默认配置
const DEFAULT_CONFIG: RequestManagerConfig = {
  debounceTime: 2000, // 2秒防抖
  maxConcurrentRequests: 3,
  defaultRetries: 2,
  cacheTimeout: 30000 // 30秒缓存
};

// 缓存项接口
interface CacheItem<T = unknown> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

/**
 * 统一请求管理器类
 */
export class RequestManager {
  private config: RequestManagerConfig;
  private requestQueue: RequestItem[] = [];
  private activeRequests: Map<string, RequestItem<unknown>> = new Map();
  private cache: Map<string, CacheItem> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private requestCounter = 0;

  constructor(config: Partial<RequestManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 定期清理过期缓存
    setInterval(() => {
      this.cleanExpiredCache();
    }, this.config.cacheTimeout);
  }

  /**
   * 生成请求指纹
   */
  private generateRequestFingerprint(type: RequestType, params: Record<string, unknown>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as Record<string, unknown>);

    return `${type}:${JSON.stringify(sortedParams)}`;
  }

  /**
   * 生成唯一请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestCounter}`;
  }

  /**
   * 检查缓存
   */
  private getCachedData<T>(fingerprint: string): T | null {
    const cached = this.cache.get(fingerprint);
    if (cached && Date.now() < cached.expiresAt) {
      return cached.data as T;
    }

    // 清理过期缓存
    if (cached) {
      this.cache.delete(fingerprint);
    }

    return null;
  }

  /**
   * 设置缓存
   */
  private setCachedData<T>(fingerprint: string, data: T): void {
    this.cache.set(fingerprint, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.config.cacheTimeout
    });
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now >= item.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 检查是否有相同的进行中请求
   */
  private findActiveRequest(fingerprint: string): RequestItem | null {
    for (const [, request] of this.activeRequests) {
      if (this.generateRequestFingerprint(request.type, request.params) === fingerprint) {
        return request;
      }
    }
    return null;
  }

  /**
   * 添加请求到队列
   */
  public async request<T>(
    type: RequestType,
    params: Record<string, unknown>,
    executor: () => Promise<T>,
    priority: RequestPriority = RequestPriority.NORMAL,
    useCache: boolean = true
  ): Promise<T> {
    const fingerprint = this.generateRequestFingerprint(type, params);

    // 检查缓存
    if (useCache) {
      const cachedData = this.getCachedData<T>(fingerprint);
      if (cachedData) {
        console.log(`[RequestManager] 返回缓存数据`, {
          type,
          params,
          fingerprint,
          timestamp: Date.now()
        });
        return cachedData;
      }
    }

    // 检查是否有相同的进行中请求
    const activeRequest = this.findActiveRequest(fingerprint);
    if (activeRequest && activeRequest.promise) {
      console.log(`[RequestManager] 复用进行中的请求`, {
        type,
        params,
        fingerprint,
        activeRequestId: activeRequest.id,
        timestamp: Date.now()
      });
      return activeRequest.promise as Promise<T>;
    }

    // 创建新请求项
    const requestItem: RequestItem<T> = {
      id: this.generateRequestId(),
      type,
      priority,
      status: RequestStatus.PENDING,
      params,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: this.config.defaultRetries
    };

    // 创建Promise并保存resolve/reject
    return new Promise<T>((resolve, reject) => {
      requestItem.resolve = resolve;
      requestItem.reject = reject;

      // 实现防抖
      const debounceKey = fingerprint;
      const existingTimer = this.debounceTimers.get(debounceKey);

      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      const debounceTimer = setTimeout(async () => {
        this.debounceTimers.delete(debounceKey);

        try {
          // 执行请求
          requestItem.status = RequestStatus.IN_PROGRESS;
          this.activeRequests.set(requestItem.id, requestItem as RequestItem<unknown>);

          console.log(`[RequestManager] 开始执行请求`, {
            id: requestItem.id,
            type,
            params,
            priority,
            timestamp: Date.now()
          });

          const result = await executor();

          // 缓存结果
          if (useCache) {
            this.setCachedData<T>(fingerprint, result);
          }

          requestItem.status = RequestStatus.COMPLETED;
          this.activeRequests.delete(requestItem.id);

          console.log(`[RequestManager] 请求执行成功`, {
            id: requestItem.id,
            type,
            params,
            timestamp: Date.now()
          });

          resolve(result);

        } catch (error) {
          requestItem.status = RequestStatus.FAILED;
          this.activeRequests.delete(requestItem.id);

          console.error(`[RequestManager] 请求执行失败`, {
            id: requestItem.id,
            type,
            params,
            error: error instanceof Error ? error.message : String(error),
            timestamp: Date.now()
          });

          reject(error);
        }
      }, this.config.debounceTime);

      this.debounceTimers.set(debounceKey, debounceTimer);

      // 保存Promise到请求项
      requestItem.promise = new Promise<T>((res, rej) => {
        requestItem.resolve = res;
        requestItem.reject = rej;
      });
    });
  }

  /**
   * 取消所有待处理的请求
   */
  public cancelAllPendingRequests(): void {
    // 清理防抖定时器
    for (const [, timer] of this.debounceTimers) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    // 取消队列中的请求
    for (const request of this.requestQueue) {
      if (request.status === RequestStatus.PENDING) {
        request.status = RequestStatus.CANCELLED;
        request.reject?.(new Error('Request cancelled'));
      }
    }

    this.requestQueue = [];

    console.log(`[RequestManager] 已取消所有待处理请求`, {
      timestamp: Date.now()
    });
  }

  /**
   * 获取管理器状态
   */
  public getStatus() {
    return {
      activeRequestsCount: this.activeRequests.size,
      queueLength: this.requestQueue.length,
      cacheSize: this.cache.size,
      debounceTimersCount: this.debounceTimers.size,
      config: this.config
    };
  }

  /**
   * 清理所有缓存
   */
  public clearCache(): void {
    this.cache.clear();
    console.log(`[RequestManager] 已清理所有缓存`, {
      timestamp: Date.now()
    });
  }

  /**
   * 立即执行请求（跳过防抖，用于高优先级请求）
   */
  public async immediateRequest<T>(
    type: RequestType,
    params: Record<string, unknown>,
    executor: () => Promise<T>,
    useCache: boolean = true
  ): Promise<T> {
    const fingerprint = this.generateRequestFingerprint(type, params);

    // 检查缓存
    if (useCache) {
      const cachedData = this.getCachedData<T>(fingerprint);
      if (cachedData) {
        console.log(`[RequestManager] 立即请求返回缓存数据`, {
          type,
          params,
          fingerprint,
          timestamp: Date.now()
        });
        return cachedData;
      }
    }

    // 检查是否有相同的进行中请求
    const activeRequest = this.findActiveRequest(fingerprint);
    if (activeRequest && activeRequest.promise) {
      console.log(`[RequestManager] 立即请求复用进行中的请求`, {
        type,
        params,
        fingerprint,
        activeRequestId: activeRequest.id,
        timestamp: Date.now()
      });
      return activeRequest.promise as Promise<T>;
    }

    // 取消相同类型的防抖请求
    const debounceKey = fingerprint;
    const existingTimer = this.debounceTimers.get(debounceKey);
    if (existingTimer) {
      clearTimeout(existingTimer);
      this.debounceTimers.delete(debounceKey);
      console.log(`[RequestManager] 取消防抖请求，执行立即请求`, {
        type,
        params,
        timestamp: Date.now()
      });
    }

    // 创建新请求项
    const requestItem: RequestItem<T> = {
      id: this.generateRequestId(),
      type,
      priority: RequestPriority.URGENT,
      status: RequestStatus.IN_PROGRESS,
      params,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: this.config.defaultRetries
    };

    this.activeRequests.set(requestItem.id, requestItem as RequestItem<unknown>);

    try {
      console.log(`[RequestManager] 开始执行立即请求`, {
        id: requestItem.id,
        type,
        params,
        timestamp: Date.now()
      });

      const result = await executor();

      // 缓存结果
      if (useCache) {
        this.setCachedData<T>(fingerprint, result);
      }

      requestItem.status = RequestStatus.COMPLETED;
      this.activeRequests.delete(requestItem.id);

      console.log(`[RequestManager] 立即请求执行成功`, {
        id: requestItem.id,
        type,
        params,
        timestamp: Date.now()
      });

      return result;

    } catch (error) {
      requestItem.status = RequestStatus.FAILED;
      this.activeRequests.delete(requestItem.id);

      console.error(`[RequestManager] 立即请求执行失败`, {
        id: requestItem.id,
        type,
        params,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now()
      });

      throw error;
    }
  }

  /**
   * 重置轮询计时器（用于手动刷新时重置轮询）
   */
  public resetPollingTimer(callback: () => void, interval: number): NodeJS.Timeout {
    return setTimeout(callback, interval);
  }

  /**
   * 获取请求统计信息
   */
  public getStatistics() {
    const now = Date.now();
    const recentRequests = Array.from(this.activeRequests.values())
      .filter(req => now - req.timestamp < 60000); // 最近1分钟的请求

    return {
      totalActiveRequests: this.activeRequests.size,
      recentRequestsCount: recentRequests.length,
      cacheHitRate: this.cache.size > 0 ?
        Array.from(this.cache.values()).filter(item => now < item.expiresAt).length / this.cache.size : 0,
      averageRequestAge: recentRequests.length > 0 ?
        recentRequests.reduce((sum, req) => sum + (now - req.timestamp), 0) / recentRequests.length : 0,
      requestsByType: recentRequests.reduce((acc, req) => {
        acc[req.type] = (acc[req.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  }
}

// 创建全局请求管理器实例
export const globalRequestManager = new RequestManager();

// 导出便捷函数
export const requestSectionRanking = (
  baseId: string,
  sectionName: string,
  executor: () => Promise<RankingData>,
  priority: RequestPriority = RequestPriority.NORMAL
) => {
  return globalRequestManager.request(
    RequestType.SECTION_RANKING,
    { baseId, sectionName },
    executor,
    priority
  );
};

export const immediateRequestSectionRanking = (
  baseId: string,
  sectionName: string,
  executor: () => Promise<RankingData>
) => {
  return globalRequestManager.immediateRequest(
    RequestType.SECTION_RANKING,
    { baseId, sectionName },
    executor
  );
};
