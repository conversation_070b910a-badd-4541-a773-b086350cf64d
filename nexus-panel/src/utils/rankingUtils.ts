/**
 * 排名相关的工具函数
 * 
 * 提供排名数据处理、验证、格式转换等功能
 */

import type {
	NavigationNode,
	RankingData,
	PlayerScore,
	AnswerRecordApiItem,
	PlayerInfoApiItem
} from '../services/api/types';

// ==================== 节点判断工具函数 ====================

/**
 * 判断导航节点是否为排名节点
 * @param nodeKey 节点的唯一标识符
 * @param navigationData 导航数据数组
 * @returns 是否为排名节点
 */
export function isRankingNode(nodeKey: string, navigationData: NavigationNode[] | null): boolean {
	if (!navigationData) return false;

	// 递归查找节点
	const findNode = (nodes: NavigationNode[]): NavigationNode | null => {
		for (const node of nodes) {
			if (node.id === nodeKey) {
				return node;
			}
			if (node.children && node.children.length > 0) {
				const found = findNode(node.children);
				if (found) return found;
			}
		}
		return null;
	};

	const node = findNode(navigationData);
	return !!(node && node.contentType === "排名");
}

/**
 * 查找导航树中的特定节点
 * @param nodes 导航节点数组
 * @param targetId 目标节点ID
 * @returns 找到的节点或null
 */
export function findNodeInTree(nodes: NavigationNode[], targetId: string): NavigationNode | null {
	for (const node of nodes) {
		if (node.id === targetId) return node;
		if (node.children && node.children.length > 0) {
			const found = findNodeInTree(node.children, targetId);
			if (found) return found;
		}
	}
	return null;
}

// ==================== 数据验证函数 ====================

/**
 * 验证排名数据的完整性和有效性
 * @param data 排名数据
 * @returns 验证结果和错误信息
 */
export function validateRankingData(data: RankingData): { isValid: boolean; errors: string[] } {
	const errors: string[] = [];

	// 检查基本结构
	if (!data) {
		errors.push('排名数据为空');
		return { isValid: false, errors };
	}

	// 检查选手数据
	if (!Array.isArray(data.players)) {
		errors.push('选手数据格式错误');
	} else {
		// 检查每个选手的数据完整性
		data.players.forEach((player, index) => {
			if (!player.playerId) {
				errors.push(`第${index + 1}个选手缺少选手ID`);
			}
			if (!player.playerName) {
				errors.push(`第${index + 1}个选手缺少选手名称`);
			}
			if (typeof player.totalScore !== 'number') {
				errors.push(`第${index + 1}个选手的总分格式错误`);
			}
			if (!player.stageScores || typeof player.stageScores !== 'object') {
				errors.push(`第${index + 1}个选手的环节得分数据格式错误`);
			}
		});
	}

	// 检查环节数据
	if (!Array.isArray(data.stages)) {
		errors.push('环节数据格式错误');
	}

	// 检查时间戳
	if (typeof data.lastUpdated !== 'number' || data.lastUpdated <= 0) {
		errors.push('数据更新时间格式错误');
	}

	// 检查选手总数
	if (typeof data.totalPlayers !== 'number' || data.totalPlayers < 0) {
		errors.push('选手总数格式错误');
	}

	return { isValid: errors.length === 0, errors };
}

/**
 * 验证选手得分数据的有效性
 * @param player 选手得分数据
 * @returns 是否有效
 */
export function validatePlayerScore(player: PlayerScore): boolean {
	return !!(
		player &&
		typeof player.playerId === 'string' &&
		player.playerId.length > 0 &&
		typeof player.playerName === 'string' &&
		player.playerName.length > 0 &&
		typeof player.totalScore === 'number' &&
		player.totalScore >= 0 &&
		player.stageScores &&
		typeof player.stageScores === 'object'
	);
}

// ==================== 排名计算函数 ====================

/**
 * 计算选手排名（严格连续序号排列）
 * 排序规则：
 * 1. 第一优先级：总分降序
 * 2. 第二优先级：选手ID升序（确保结果一致性）
 * 排名序号：连续整数序列 1、2、3、4、5...
 * @param players 选手得分数组
 * @returns 带排名的选手数组
 */
export function calculatePlayerRanks(players: PlayerScore[]): PlayerScore[] {
	// 按总分降序排序，总分相同时按选手ID升序排序（确保连续排名）
	const sortedPlayers = [...players].sort((a, b) => {
		// 第一优先级：总分降序
		if (b.totalScore !== a.totalScore) {
			return b.totalScore - a.totalScore;
		}
		// 第二优先级：选手ID升序（确保结果一致性和可预测性）
		return a.playerId.localeCompare(b.playerId);
	});

	// 分配连续排名序号（1、2、3、4、5...）
	sortedPlayers.forEach((player, index) => {
		player.rank = index + 1;
	});

	return sortedPlayers;
}

/**
 * 重新计算排名数据（用于数据更新后的重新排序）
 * @param data 原始排名数据
 * @returns 重新计算后的排名数据
 */
export function recalculateRankingData(data: RankingData): RankingData {
	const playersWithRanks = calculatePlayerRanks(data.players);

	return {
		...data,
		players: playersWithRanks,
		lastUpdated: Date.now()
	};
}

// ==================== 数据格式转换函数 ====================

/**
 * 将API原始数据转换为标准的选手得分格式
 * @param answerRecords 答题记录数据
 * @param playerInfo 选手信息数据
 * @returns 标准格式的选手得分数组
 */
export function convertApiDataToPlayerScores(
	answerRecords: AnswerRecordApiItem[],
	playerInfo: PlayerInfoApiItem[]
): PlayerScore[] {
	// 创建选手基础信息映射
	const playerMap = new Map<string, PlayerScore>();

	// 初始化所有选手的基础数据
	playerInfo.forEach(player => {
		playerMap.set(String(player.user_id), {
			playerId: String(player.user_id),
			playerName: player.user_name,
			stageScores: {},
			totalScore: 0
		});
	});

	// 统计所有环节
	const stagesSet = new Set<string>();

	// 处理答题记录，累计得分
	answerRecords.forEach(record => {
		const playerId = String(record.user_id);
		const stageName = record.session_id;
		const isCorrect = record.is_correct === 1;
		const score = isCorrect ? record.score : 0;

		stagesSet.add(stageName);

		const player = playerMap.get(playerId);
		if (player) {
			// 累加环节得分
			if (!player.stageScores[stageName]) {
				player.stageScores[stageName] = 0;
			}
			player.stageScores[stageName] += score;
		}
	});

	// 计算总分并转换为数组
	const players = Array.from(playerMap.values()).map(player => {
		player.totalScore = Object.values(player.stageScores).reduce((sum, score) => sum + score, 0);
		return player;
	});

	return players;
}

// ==================== 数据统计函数 ====================

/**
 * 计算排名数据的统计信息
 * @param data 排名数据
 * @returns 统计信息对象
 */
export function calculateRankingStatistics(data: RankingData) {
	const players = data.players;
	const stages = data.stages;

	// 基本统计
	const totalPlayers = players.length;
	const totalStages = stages.length;

	// 分数统计
	const scores = players.map(p => p.totalScore);
	const maxScore = Math.max(...scores);
	const minScore = Math.min(...scores);
	const avgScore = scores.reduce((sum, score) => sum + score, 0) / totalPlayers;

	// 环节统计
	const stageStats = stages.map(stageName => {
		const stageScores = players.map(p => p.stageScores[stageName] || 0);
		return {
			stageName,
			maxScore: Math.max(...stageScores),
			minScore: Math.min(...stageScores),
			avgScore: stageScores.reduce((sum, score) => sum + score, 0) / totalPlayers,
			participantCount: stageScores.filter(score => score > 0).length
		};
	});

	// 排名分布
	const rankDistribution = new Map<number, number>();
	players.forEach(player => {
		if (player.rank) {
			rankDistribution.set(player.rank, (rankDistribution.get(player.rank) || 0) + 1);
		}
	});

	return {
		totalPlayers,
		totalStages,
		scoreStats: {
			max: maxScore,
			min: minScore,
			avg: Math.round(avgScore * 100) / 100
		},
		stageStats,
		rankDistribution: Object.fromEntries(rankDistribution),
		lastUpdated: data.lastUpdated
	};
}

// ==================== 导出便捷函数 ====================

/**
 * 导出数据行的类型定义
 */
interface ExportDataRow {
	'排名': number | string;
	'选手ID': string;
	'选手名称': string;
	'总分': number;
	[stageName: string]: string | number; // 动态环节得分字段
}

/**
 * 格式化排名数据为可导出的格式
 * @param data 排名数据
 * @returns 格式化后的数据
 */
export function formatRankingDataForExport(data: RankingData): ExportDataRow[] {
	return data.players.map(player => {
		const row: ExportDataRow = {
			'排名': player.rank || '-',
			'选手ID': player.playerId,
			'选手名称': player.playerName,
			'总分': player.totalScore,
		};

		// 添加各环节得分
		data.stages.forEach(stageName => {
			row[stageName] = player.stageScores[stageName] || 0;
		});

		return row;
	});
}

/**
 * 生成排名数据的摘要信息
 * @param data 排名数据
 * @returns 摘要字符串
 */
export function generateRankingSummary(data: RankingData): string {
	const stats = calculateRankingStatistics(data);
	const updateTime = new Date(data.lastUpdated).toLocaleString();

	return `排名数据摘要：
- 参赛选手：${stats.totalPlayers} 名
- 比赛环节：${stats.totalStages} 个
- 最高分：${stats.scoreStats.max} 分
- 最低分：${stats.scoreStats.min} 分
- 平均分：${stats.scoreStats.avg} 分
- 更新时间：${updateTime}`;
}
