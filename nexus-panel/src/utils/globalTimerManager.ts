/**
 * 全局计时器管理器
 * 
 * 用于管理争分夺秒和同分加赛环节的计时器状态
 */

interface TimerState {
  time: number;
  isRunning: boolean;
  maxTime: number;
}

class GlobalTimerManager {
  private timer: TimerState = {
    time: 0,
    isRunning: false,
    maxTime: 0
  };

  private interval: NodeJS.Timeout | null = null;
  private onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
  private listeners: Set<() => void> = new Set();

  /**
   * 初始化计时器
   */
  initialize(timeInSeconds: number, onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void) {
    this.timer = {
      time: timeInSeconds,
      isRunning: false,
      maxTime: timeInSeconds
    };
    this.onLog = onLog;
    this.notifyListeners();
  }

  /**
   * 格式化时间显示
   */
  formatTime(time: number): string {
    if (isNaN(time)) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  /**
   * 开始计时器
   */
  start(): void {
    if (this.timer.time <= 0) {
      this.onLog?.('warning', '计时器时间已到，无法启动', {
        remainingTime: this.timer.time,
        timestamp: Date.now()
      });
      return;
    }

    this.timer.isRunning = true;
    this.notifyListeners();

    this.interval = setInterval(() => {
      this.timer.time = Math.max(0, this.timer.time - 1);
      this.notifyListeners();

      // 时间到达时停止
      if (this.timer.time === 0) {
        this.timer.isRunning = false;
        if (this.interval) {
          clearInterval(this.interval);
          this.interval = null;
        }
        this.onLog?.('warning', '比赛时间到', {
          timestamp: Date.now()
        });
        this.notifyListeners();
      }
    }, 1000);

    this.onLog?.('info', '比赛计时器启动', {
      remainingTime: this.timer.time,
      maxTime: this.timer.maxTime,
      timestamp: Date.now()
    });
  }

  /**
   * 暂停计时器
   */
  pause(): void {
    this.timer.isRunning = false;
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    this.notifyListeners();

    this.onLog?.('info', '比赛计时器暂停', {
      remainingTime: this.timer.time,
      timestamp: Date.now()
    });
  }

  /**
   * 重置计时器
   */
  reset(): void {
    this.timer = {
      time: this.timer.maxTime,
      isRunning: false,
      maxTime: this.timer.maxTime
    };
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    this.notifyListeners();

    this.onLog?.('info', '比赛计时器重置', {
      resetTime: this.timer.maxTime,
      timestamp: Date.now()
    });
  }

  /**
   * 获取当前状态
   */
  getState(): TimerState {
    return { ...this.timer };
  }

  /**
   * 获取格式化的时间
   */
  getFormattedTime(): string {
    return this.formatTime(this.timer.time);
  }

  /**
   * 添加状态变化监听器
   */
  addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  /**
   * 移除状态变化监听器
   */
  removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Timer listener error:', error);
      }
    });
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 停止计时器
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }

    // 重置计时器状态
    this.timer = {
      time: 0,
      isRunning: false,
      maxTime: 0
    };

    // 清理监听器
    this.listeners.clear();

    // 清理日志回调
    this.onLog = undefined;
  }
}

// 创建全局实例
export const globalTimerManager = new GlobalTimerManager();

// 在window对象上暴露，供按钮组使用
declare global {
  interface Window {
    timeRaceTimerManager?: GlobalTimerManager;
  }
}

if (typeof window !== 'undefined') {
  window.timeRaceTimerManager = globalTimerManager;
}
