/**
 * 争分夺秒和同分加赛工具函数
 * 
 * 提供内容类型解析、时长提取等功能
 */

/**
 * 解析争分夺秒内容类型，提取倒计时时长
 *
 * @param contentType 内容类型字符串，如"快答180"、"快答120"、"快答"
 * @param nodeName 节点名称（可选），用于从名称中提取时长
 * @returns 倒计时时长（秒），如果解析失败返回null
 *
 * @example
 * parseTimeRaceContentType("快答180") // 返回 180
 * parseTimeRaceContentType("快答120") // 返回 120
 * parseTimeRaceContentType("快答", "争分夺秒180秒") // 返回 180
 * parseTimeRaceContentType("题目") // 返回 null
 */
export function parseTimeRaceContentType(contentType: string, nodeName?: string): number | null {
  if (!contentType || typeof contentType !== 'string') {
    return null;
  }

  // 首先尝试匹配"快答[数字]"或"快答 [数字]"格式（支持空格）
  const directMatch = contentType.match(/^快答\s*(\d+)$/);
  if (directMatch && directMatch[1]) {
    const timeInSeconds = parseInt(directMatch[1], 10);
    if (!isNaN(timeInSeconds) && timeInSeconds >= 1 && timeInSeconds <= 3600) {
      return timeInSeconds;
    }
  }

  // 如果内容类型是"快答"，尝试从节点名称中提取时长
  if (contentType === "快答" && nodeName) {
    // 匹配节点名称中的数字（如"争分夺秒180秒"、"同分加赛120"等）
    const nameMatch = nodeName.match(/(\d+)/);
    if (nameMatch && nameMatch[1]) {
      const timeInSeconds = parseInt(nameMatch[1], 10);
      if (!isNaN(timeInSeconds) && timeInSeconds >= 1 && timeInSeconds <= 3600) {
        return timeInSeconds;
      }
    }
  }

  return null;
}

/**
 * 检查内容类型是否为争分夺秒类型
 *
 * @param contentType 内容类型字符串
 * @param nodeName 节点名称（可选），用于从名称中提取时长
 * @returns 是否为争分夺秒类型
 *
 * @example
 * isTimeRaceContentType("快答180") // 返回 true
 * isTimeRaceContentType("快答", "争分夺秒180秒") // 返回 true
 * isTimeRaceContentType("题目") // 返回 false
 */
export function isTimeRaceContentType(contentType: string, nodeName?: string): boolean {
  return parseTimeRaceContentType(contentType, nodeName) !== null;
}

/**
 * 格式化时长为可读字符串
 * 
 * @param seconds 时长（秒）
 * @returns 格式化后的字符串
 * 
 * @example
 * formatDuration(180) // 返回 "3分钟"
 * formatDuration(120) // 返回 "2分钟"
 * formatDuration(90) // 返回 "1分30秒"
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) {
    return "0秒";
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes === 0) {
    return `${remainingSeconds}秒`;
  } else if (remainingSeconds === 0) {
    return `${minutes}分钟`;
  } else {
    return `${minutes}分${remainingSeconds}秒`;
  }
}

/**
 * 获取争分夺秒环节的显示名称
 * 
 * @param contentType 内容类型字符串
 * @returns 显示名称
 * 
 * @example
 * getTimeRaceDisplayName("快答180") // 返回 "争分夺秒（3分钟）"
 * getTimeRaceDisplayName("快答120") // 返回 "争分夺秒（2分钟）"
 */
export function getTimeRaceDisplayName(contentType: string): string {
  const timeInSeconds = parseTimeRaceContentType(contentType);

  if (timeInSeconds === null) {
    return "争分夺秒";
  }

  const duration = formatDuration(timeInSeconds);
  return `争分夺秒（${duration}）`;
}

/**
 * 从节点名称中提取阶段信息
 *
 * @param nodeName 节点名称，如"争分夺秒180秒"、"同分加赛120"等
 * @returns 提取的阶段名称，如果无法提取返回null
 *
 * @example
 * extractStageFromNodeName("争分夺秒180秒") // 返回 "争分夺秒"
 * extractStageFromNodeName("同分加赛120") // 返回 "同分加赛"
 * extractStageFromNodeName("普通题目") // 返回 null
 */
export function extractStageFromNodeName(nodeName: string): string | null {
  if (!nodeName || typeof nodeName !== 'string') {
    return null;
  }

  // 匹配"争分夺秒"关键词
  if (nodeName.includes('争分夺秒')) {
    return '争分夺秒';
  }

  // 匹配"同分加赛"关键词
  if (nodeName.includes('同分加赛')) {
    return '同分加赛';
  }

  return null;
}

/**
 * 将内容类型映射到正确的数据库阶段字段值
 *
 * @param contentType 内容类型字符串，如"快答180"、"快答120"、"快答"等
 * @param nodeName 节点名称（可选），用于从名称中提取阶段信息
 * @returns 映射后的阶段名称，如果无法映射返回原始contentType
 *
 * @example
 * mapContentTypeToStage("快答180") // 返回 "争分夺秒"
 * mapContentTypeToStage("快答", "争分夺秒180秒") // 返回 "争分夺秒"
 * mapContentTypeToStage("快答", "同分加赛120") // 返回 "同分加赛"
 * mapContentTypeToStage("题目") // 返回 "题目"
 */
export function mapContentTypeToStage(contentType: string, nodeName?: string): string {
  if (!contentType || typeof contentType !== 'string') {
    return contentType || '';
  }

  // 如果内容类型包含"快答"，需要进行映射
  if (contentType.includes('快答')) {
    // 优先从节点名称中提取阶段信息
    if (nodeName) {
      const stageFromNode = extractStageFromNodeName(nodeName);
      if (stageFromNode) {
        return stageFromNode;
      }
    }

    // 如果节点名称无法提取阶段，默认映射为"争分夺秒"
    // 这是因为大多数"快答"类型的题目都属于争分夺秒环节
    return '争分夺秒';
  }

  // 非快答类型，直接返回原始内容类型
  return contentType;
}

/**
 * 争分夺秒环节的预设时长配置
 */
export const TIME_RACE_PRESETS = {
  /** 180秒（3分钟） */
  STANDARD: 180,
  /** 120秒（2分钟） */
  SHORT: 120,
  /** 60秒（1分钟） */
  QUICK: 60,
} as const;

/**
 * 验证时长是否为预设值
 * 
 * @param seconds 时长（秒）
 * @returns 是否为预设值
 */
export function isPresetDuration(seconds: number): boolean {
  return Object.values(TIME_RACE_PRESETS).includes(seconds);
}
