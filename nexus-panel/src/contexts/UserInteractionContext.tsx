import React, { createContext } from 'react';
import type { ReactNode } from 'react';
import { useUserInteraction } from '../hooks/useUserInteraction';
import type { UseUserInteractionReturn } from '../hooks/useUserInteraction';

/**
 * 用户交互Context类型
 */
type UserInteractionContextType = UseUserInteractionReturn | null;

/**
 * 用户交互Context
 */
const UserInteractionContext = createContext<UserInteractionContextType>(null);

/**
 * UserInteractionProvider组件的Props接口
 */
export interface UserInteractionProviderProps {
	/** 子组件 */
	children: ReactNode;
}

/**
 * 用户交互Context Provider组件
 * 
 * 功能特性：
 * - 为整个应用提供全局的用户交互状态
 * - 自动检测和跟踪用户交互事件
 * - 支持音频自动播放权限检查
 * - 提供交互状态的持久化存储
 * 
 * 使用方式：
 * ```tsx
 * // 在应用根组件中包装
 * <UserInteractionProvider>
 *   <App />
 * </UserInteractionProvider>
 * 
 * // 在子组件中使用
 * import { useUserInteractionContext } from '../hooks/useUserInteractionContext';
 * const { hasInteracted, canAutoPlayAudio } = useUserInteractionContext();
 * ```
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const UserInteractionProvider: React.FC<UserInteractionProviderProps> = ({
	children
}) => {
	const userInteraction = useUserInteraction();

	return (
		<UserInteractionContext.Provider value={userInteraction}>
			{children}
		</UserInteractionContext.Provider>
	);
};



/**
 * 用户交互Context的默认导出
 */
export default UserInteractionContext;
