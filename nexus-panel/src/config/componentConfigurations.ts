// 组件配置映射模块
import type { NavigationNode } from "../services/api";
import {
	ruleButtonGroups,
	questionButtonGroups,
	finalPKButtonGroups,
	rankingButtonGroups,
	timeRaceAndTieBreakButtonGroups,
	createTimeRaceAndTieBreakButtonGroups,
	homeButtonGroups,
	emptyButtonGroups,
	safetyPublicClassButtonGroups,
	createSafetyPublicClassButtonGroupsWithRealData,
	type ButtonGroupConfig,
} from "./buttonGroupConfigurations";

/**
 * 骨架屏组件类型
 */
export type SkeletonComponentType =
	| 'RuleIntroSkeleton'
	| 'QuestionSkeleton'
	| 'UltimatePKSkeleton'
	| 'RankingSkeleton'
	| 'HomePageSkeleton'
	| 'SafetyPublicClassSkeleton';

/**
 * 按钮组类型
 */
export type ButtonGroupType =
	| 'ruleButtonGroups'
	| 'questionButtonGroups'
	| 'finalPKButtonGroups'
	| 'rankingButtonGroups'
	| 'timeRaceAndTieBreakButtonGroups'
	| 'homeButtonGroups'
	| 'emptyButtonGroups'
	| 'safetyPublicClassButtonGroups';

/**
 * 组件配置接口
 */
export interface ComponentConfig {
	skeleton: SkeletonComponentType;
	buttonGroups: ButtonGroupType;
	showBreadcrumbs: boolean;
	showAudioPlayer: boolean;
}

/**
 * 内容类型到组件配置的映射
 */
export const CONTENT_TYPE_MAPPING: Record<string, ComponentConfig> = {
	"规则": {
		skeleton: "RuleIntroSkeleton",
		buttonGroups: "ruleButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: true
	},
	"题目": {
		skeleton: "QuestionSkeleton",
		buttonGroups: "questionButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: true
	},
	"计时": {
		skeleton: "UltimatePKSkeleton",
		buttonGroups: "finalPKButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: false
	},
	"排名": {
		skeleton: "RankingSkeleton",
		buttonGroups: "rankingButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: false
	},
	"快答": {
		skeleton: "QuestionSkeleton",
		buttonGroups: "timeRaceAndTieBreakButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: false
	},
	"评分": {
		skeleton: "SafetyPublicClassSkeleton",
		buttonGroups: "safetyPublicClassButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: false
	},
	"安全公开课": {
		skeleton: "SafetyPublicClassSkeleton",
		buttonGroups: "safetyPublicClassButtonGroups",
		showBreadcrumbs: true,
		showAudioPlayer: false
	},
};

/**
 * 在导航树中递归查找指定ID的节点
 * @param nodes 导航节点数组
 * @param targetId 目标节点ID
 * @returns 找到的节点或null
 */
export const findNavigationNode = (nodes: NavigationNode[], targetId: string): NavigationNode | null => {
	for (const node of nodes) {
		if (node.id === targetId) {
			return node;
		}
		if (node.children && node.children.length > 0) {
			const found = findNavigationNode(node.children, targetId);
			if (found) {
				return found;
			}
		}
	}
	return null;
};

/**
 * 获取当前选中节点的内容类型（标准化处理）
 * @param selectedKey 当前选中的导航键
 * @param navigationData 动态导航数据
 * @returns 内容类型字符串或undefined
 */
export const getCurrentContentType = (selectedKey: string | null, navigationData: NavigationNode[] | null): string | undefined => {
	try {
		if (!selectedKey || !navigationData || navigationData.length === 0) {
			return undefined;
		}

		const node = findNavigationNode(navigationData, selectedKey);
		let contentType = node?.contentType;

		// 特殊处理：如果是"安全公开课"环节但contentType为空，推断为"评分"
		if (node?.name === '安全公开课' && !contentType) {
			contentType = '评分';
		}

		// 标准化争分夺秒内容类型
		if (contentType && contentType.match(/^快答\s*\d+$/)) {
			// 将"快答 180"、"快答180"等格式标准化为"快答"
			contentType = "快答";
		}

		return contentType;
	} catch (error) {
		console.warn('[动态绑定] 获取内容类型时发生错误:', error);
		return undefined;
	}
};

/**
 * 根据按钮组类型获取对应的按钮组配置
 * @param buttonGroupType 按钮组类型
 * @returns 按钮组配置数组
 */
export const getButtonGroupsByType = (buttonGroupType: ButtonGroupType): ButtonGroupConfig[] => {
	switch (buttonGroupType) {
		case "ruleButtonGroups":
			return ruleButtonGroups;
		case "finalPKButtonGroups":
			return finalPKButtonGroups;
		case "questionButtonGroups":
			return questionButtonGroups;
		case "rankingButtonGroups":
			return rankingButtonGroups;
		case "timeRaceAndTieBreakButtonGroups":
			return timeRaceAndTieBreakButtonGroups;
		case "homeButtonGroups":
			return homeButtonGroups;
		case "emptyButtonGroups":
			return emptyButtonGroups;
		case "safetyPublicClassButtonGroups":
			return safetyPublicClassButtonGroups;
		default:
			return [];
	}
};

/**
 * 根据按钮组类型和内容类型获取对应的按钮组配置（支持动态配置）
 * @param buttonGroupType 按钮组类型
 * @param contentType 内容类型（可选，用于动态配置）
 * @param nodeName 节点名称（可选，用于从名称中提取时长）
 * @param onLog 日志记录回调函数（可选）
 * @param handlers 事件处理器对象（可选）
 * @returns 按钮组配置数组
 */
export const getButtonGroupsByTypeWithContext = (
	buttonGroupType: ButtonGroupType,
	contentType?: string,
	nodeName?: string,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void,
	handlers?: {
		onPackageChange?: (packageId: string, packageName: string) => void;
		onPackageChangeConfirmed?: (packageId: string, packageName: string) => void;
		onQuestionPrevious?: () => void;
		onQuestionJump?: (formState: Record<string, string | number>) => void;
		onQuestionNext?: () => void;
		currentQuestionNumber?: number;
		totalQuestions?: number;
		onScoreAssignment?: () => void;
		onPageFirst?: () => void;
		onPageSecond?: () => void;
		// 分页信息
		paginationInfo?: {
			hasMultiplePages: boolean;
			currentPage: number;
			totalPages: number;
		};
		// 安全公开课相关数据
		configurationData?: any;
		playerInfoData?: any[];
	}
): ButtonGroupConfig[] => {
	switch (buttonGroupType) {
		case "ruleButtonGroups":
			return ruleButtonGroups;
		case "finalPKButtonGroups":
			return finalPKButtonGroups;
		case "questionButtonGroups":
			return questionButtonGroups;
		case "rankingButtonGroups":
			return rankingButtonGroups;
		case "timeRaceAndTieBreakButtonGroups":
			// 使用动态函数，支持争分夺秒计时功能
			return createTimeRaceAndTieBreakButtonGroups(
				contentType,
				nodeName,
				onLog,
				handlers?.onPackageChange,
				handlers?.onPackageChangeConfirmed,
				handlers?.onQuestionPrevious,
				handlers?.onQuestionJump,
				handlers?.onQuestionNext,
				handlers?.currentQuestionNumber,
				handlers?.totalQuestions,
				handlers?.onScoreAssignment,
				handlers?.onPageFirst,
				handlers?.onPageSecond,
				handlers?.paginationInfo
			);
		case "homeButtonGroups":
			return homeButtonGroups;
		case "emptyButtonGroups":
			return emptyButtonGroups;
		case "safetyPublicClassButtonGroups":
			// 使用动态函数，支持安全公开课数据加载
			return createSafetyPublicClassButtonGroupsWithRealData(
				nodeName,
				onLog,
				handlers?.configurationData // 配置数据包含节目显示和选手显示信息
			);
		default:
			return [];
	}
};
