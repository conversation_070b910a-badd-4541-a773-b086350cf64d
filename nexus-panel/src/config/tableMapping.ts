/**
 * 数据库表名映射配置
 * 统一管理所有表名，避免硬编码导致的问题
 */

export interface TableMapping {
  /** 显示名称（用于日志和错误信息） */
  displayName: string;
  /** 数据库中的实际表名 */
  actualName: string;
  /** 表的用途描述 */
  description: string;
}

/**
 * 表名映射配置
 * 当数据库表名发生变化时，只需要修改这里的配置
 */
export const TABLE_MAPPINGS: Record<string, TableMapping> = {
  // 导航相关表
  NAVIGATION: {
    displayName: '环节表',
    actualName: '环节表',
    description: '存储导航菜单和环节信息'
  },

  // 配置相关表
  CONFIGURATION: {
    displayName: '配置信息表',
    actualName: '赛事素材表', // 实际数据库中的表名
    description: '存储赛事配置和素材信息'
  },

  // 题目相关表
  QUESTIONS: {
    displayName: '题目表',
    actualName: '题目表',
    description: '存储题目数据'
  },

  // 选手相关表
  PLAYERS: {
    displayName: '选手表',
    actualName: '选手表',
    description: '存储选手信息'
  },

  // 答题记录表
  ANSWER_RECORDS: {
    displayName: '答题记录表',
    actualName: '答题记录表',
    description: '存储选手答题记录'
  },

  // 评委相关表
  JUDGES: {
    displayName: '评委表',
    actualName: '评委表',
    description: '存储评委信息'
  },

  // 评分记录表
  JUDGE_SCORES: {
    displayName: '评委评分表',
    actualName: '评委评分表',
    description: '存储评委评分记录'
  },

  // 投票记录表
  VOTE_RECORDS: {
    displayName: '投票记录表',
    actualName: '投票记录表',
    description: '存储投票记录'
  },

  // 日志记录表
  LOG_RECORDS: {
    displayName: '日志记录表',
    actualName: '日志记录表',
    description: '存储系统日志'
  },

  // 最终得分表
  FINAL_SCORES: {
    displayName: '最终得分表',
    actualName: '最终得分表',
    description: '存储最终得分'
  }
};

/**
 * 根据逻辑表名获取实际表名
 * @param logicalName 逻辑表名（如 'CONFIGURATION'）
 * @returns 实际表名
 */
export function getActualTableName(logicalName: keyof typeof TABLE_MAPPINGS): string {
  const mapping = TABLE_MAPPINGS[logicalName];
  if (!mapping) {
    throw new Error(`未找到表映射配置: ${logicalName}`);
  }
  return mapping.actualName;
}

/**
 * 根据逻辑表名获取显示名称
 * @param logicalName 逻辑表名
 * @returns 显示名称
 */
export function getDisplayTableName(logicalName: keyof typeof TABLE_MAPPINGS): string {
  const mapping = TABLE_MAPPINGS[logicalName];
  if (!mapping) {
    throw new Error(`未找到表映射配置: ${logicalName}`);
  }
  return mapping.displayName;
}

/**
 * 从表结构列表中查找指定的表
 * @param tables 表结构列表
 * @param logicalName 逻辑表名
 * @returns 找到的表结构，如果未找到则抛出错误
 */
export function findTableByLogicalName(tables: any[], logicalName: keyof typeof TABLE_MAPPINGS) {
  const actualName = getActualTableName(logicalName);
  const displayName = getDisplayTableName(logicalName);
  
  const table = tables.find(t => t.title === actualName);
  
  if (!table) {
    const availableTables = tables.map(t => t.title).join(', ');
    throw new Error(
      `未找到表 "${displayName}" (实际名称: "${actualName}")。` +
      `可用的表: ${availableTables}`
    );
  }
  
  return table;
}

/**
 * 验证所有必需的表是否存在
 * @param tables 表结构列表
 * @param requiredTables 必需的逻辑表名列表
 * @returns 验证结果
 */
export function validateRequiredTables(
  tables: any[], 
  requiredTables: (keyof typeof TABLE_MAPPINGS)[]
): { success: boolean; missingTables: string[]; availableTables: string[] } {
  const missingTables: string[] = [];
  const availableTables = tables.map(t => t.title);
  
  for (const logicalName of requiredTables) {
    try {
      findTableByLogicalName(tables, logicalName);
    } catch (error) {
      missingTables.push(getDisplayTableName(logicalName));
    }
  }
  
  return {
    success: missingTables.length === 0,
    missingTables,
    availableTables
  };
}
