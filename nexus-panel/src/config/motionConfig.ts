/**
 * Motion for React 全局动画配置
 * 
 * 提供统一的动画配置、预设变体和性能优化设置
 * 确保与Adobe React Spectrum设计系统的兼容性
 */

// ==================== 动画持续时间常量 ====================

export const MOTION_DURATIONS = {
  /** 快速动画 - 适用于小型UI元素 */
  fast: 0.15,
  /** 标准动画 - 适用于大多数交互 */
  normal: 0.2,
  /** 中等动画 - 适用于面板展开等 */
  medium: 0.3,
  /** 慢速动画 - 适用于页面级过渡 */
  slow: 0.5,
} as const;

// ==================== 缓动函数配置 ====================

export const MOTION_EASINGS = {
  /** 标准缓动 - 适用于大多数动画 */
  standard: "easeInOut",
  /** 进入缓动 - 适用于元素出现 */
  enter: "easeOut",
  /** 退出缓动 - 适用于元素消失 */
  exit: "easeIn",
  /** 弹性缓动 - 适用于强调动画 */
  bounce: "easeInOut",
} as const;

// ==================== 预设动画变体 ====================

/**
 * 淡入淡出动画变体
 */
export const fadeVariants = {
  hidden: {
    opacity: 0
  },
  visible: {
    opacity: 1,
    transition: {
      duration: MOTION_DURATIONS.normal,
      ease: MOTION_EASINGS.enter
    }
  },
  exit: {
    opacity: 0,
    transition: {
      duration: MOTION_DURATIONS.fast,
      ease: MOTION_EASINGS.exit
    }
  }
};

/**
 * 滑动动画变体 (水平)
 */
export const slideHorizontalVariants = {
  hidden: {
    opacity: 0,
    x: 20
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: MOTION_DURATIONS.normal,
      ease: MOTION_EASINGS.standard
    }
  },
  exit: {
    opacity: 0,
    x: -20,
    transition: {
      duration: MOTION_DURATIONS.fast,
      ease: MOTION_EASINGS.exit
    }
  }
};

/**
 * 滑动动画变体 (垂直)
 */
export const slideVerticalVariants = {
  hidden: {
    opacity: 0,
    y: 10
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: MOTION_DURATIONS.normal,
      ease: MOTION_EASINGS.standard
    }
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: {
      duration: MOTION_DURATIONS.fast,
      ease: MOTION_EASINGS.exit
    }
  }
};

/**
 * 缩放动画变体
 */
export const scaleVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: MOTION_DURATIONS.normal,
      ease: MOTION_EASINGS.standard
    }
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: MOTION_DURATIONS.fast,
      ease: MOTION_EASINGS.exit
    }
  }
};

// ==================== 特殊动画配置 ====================

/**
 * 控制台面板动画配置
 */
export const consolePanelAnimation = {
  /** 展开/折叠动画 */
  expand: {
    duration: MOTION_DURATIONS.medium,
    ease: MOTION_EASINGS.standard
  },
  /** 日志条目动画 */
  logEntry: {
    duration: MOTION_DURATIONS.fast,
    ease: MOTION_EASINGS.enter,
    staggerDelay: 0.05
  }
};

/**
 * 侧边栏按钮组动画配置
 */
export const sidebarAnimation = {
  /** 按钮组切换动画 */
  groupSwitch: {
    duration: MOTION_DURATIONS.normal,
    ease: MOTION_EASINGS.standard
  },
  /** 按钮错开动画 */
  buttonStagger: {
    duration: MOTION_DURATIONS.fast,
    ease: MOTION_EASINGS.enter,
    staggerDelay: 0.05
  }
};

// ==================== 全局Motion配置 ====================

/**
 * 全局MotionConfig配置
 */
export const globalMotionConfig = {
  /** 默认过渡配置 */
  transition: {
    duration: MOTION_DURATIONS.normal,
    ease: MOTION_EASINGS.standard
  },
  /** 减少动画偏好支持 */
  reducedMotion: "user" as const,
  /** 性能优化 */
  features: {
    /** 启用硬件加速 */
    enableHardwareAcceleration: true,
    /** 优化重绘 */
    optimizeRepaints: true
  }
};

// ==================== 动画工具函数 ====================

/**
 * 创建错开动画配置
 * @param delay 基础延迟时间
 * @param increment 递增延迟
 * @returns 错开动画配置
 */
export function createStaggerConfig(delay: number = 0.05, increment: number = 0.05) {
  return {
    staggerChildren: delay,
    delayChildren: increment
  };
}

/**
 * 创建高度动画配置
 * @param fromHeight 起始高度
 * @param toHeight 目标高度
 * @param duration 动画持续时间
 * @returns 高度动画配置
 */
export function createHeightAnimation(
  fromHeight: number | string,
  toHeight: number | string,
  duration: number = MOTION_DURATIONS.medium
) {
  return {
    initial: { height: fromHeight },
    animate: { height: toHeight },
    transition: {
      duration,
      ease: MOTION_EASINGS.standard
    }
  };
}

/**
 * 检查是否应该减少动画
 * @returns 是否减少动画
 */
export function shouldReduceMotion(): boolean {
  if (typeof window === 'undefined') return false;

  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}
