import { useContext } from 'react';
import UserInteractionContext from '../contexts/UserInteractionContext';
import type { UseUserInteractionReturn } from './useUserInteraction';

/**
 * 使用用户交互Context的Hook
 * 
 * 功能特性：
 * - 提供类型安全的Context访问
 * - 自动检查Context是否可用
 * - 返回完整的用户交互状态和方法
 * 
 * 使用方式：
 * ```tsx
 * const { 
 *   hasInteracted, 
 *   canAutoPlayAudio, 
 *   markAsInteracted 
 * } = useUserInteractionContext();
 * 
 * // 检查是否可以自动播放
 * if (canAutoPlayAudio()) {
 *   audio.play();
 * }
 * ```
 * 
 * @returns 用户交互状态和控制方法
 * @throws Error 如果在UserInteractionProvider外部使用
 */
export const useUserInteractionContext = (): UseUserInteractionReturn => {
	const context = useContext(UserInteractionContext);

	if (context === null) {
		throw new Error(
			'useUserInteractionContext必须在UserInteractionProvider内部使用。' +
			'请确保组件被UserInteractionProvider包装。'
		);
	}

	return context;
};
