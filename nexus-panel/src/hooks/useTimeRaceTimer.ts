import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * 计时器状态接口
 */
interface TimerState {
  /** 剩余时间（秒） */
  time: number;
  /** 是否正在运行 */
  isRunning: boolean;
  /** 最大时间（秒） */
  maxTime: number;
}

/**
 * 争分夺秒计时器Hook
 * 
 * 提供专门用于争分夺秒和同分加赛环节的倒计时功能
 * 
 * @param initialTime 初始时间（秒）
 * @param onTimeUp 时间到达时的回调函数
 * @param onLog 日志记录回调函数
 * @returns 计时器状态和控制方法
 */
export function useTimeRaceTimer(
  initialTime: number,
  onTimeUp?: () => void,
  onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void
) {
  // 计时器状态
  const [timer, setTimer] = useState<TimerState>({
    time: initialTime,
    isRunning: false,
    maxTime: initialTime
  });

  // 计时器引用
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 格式化时间为 MM:SS 格式
  const formatTime = useCallback((time: number): string => {
    if (isNaN(time)) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }, []);

  // 开始计时器
  const startTimer = useCallback(() => {
    try {
      // 检查计时器是否已经归零
      if (timer.time <= 0) {
        onLog?.('warning', '计时器时间已到，无法启动', {
          remainingTime: timer.time,
          timestamp: Date.now()
        });
        return;
      }

      setTimer(prev => ({ ...prev, isRunning: true }));

      onLog?.('info', '比赛计时器启动', {
        remainingTime: timer.time,
        maxTime: timer.maxTime,
        timestamp: Date.now()
      });
    } catch (error) {
      onLog?.('error', '启动计时器失败', {
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now()
      });
    }
  }, [timer.time, timer.maxTime, onLog]);

  // 暂停计时器
  const pauseTimer = useCallback(() => {
    setTimer(prev => ({ ...prev, isRunning: false }));

    onLog?.('info', '比赛计时器暂停', {
      remainingTime: timer.time,
      timestamp: Date.now()
    });
  }, [timer.time, onLog]);

  // 重置计时器
  const resetTimer = useCallback(() => {
    setTimer({
      time: initialTime,
      isRunning: false,
      maxTime: initialTime
    });

    onLog?.('info', '比赛计时器重置', {
      resetTime: initialTime,
      timestamp: Date.now()
    });
  }, [initialTime, onLog]);

  // 计时器倒计时逻辑
  useEffect(() => {
    if (timer.isRunning && timer.time > 0) {
      intervalRef.current = setInterval(() => {
        setTimer(prev => {
          const newTime = Math.max(0, prev.time - 1);
          
          // 时间到达时的处理
          if (newTime === 0) {
            onTimeUp?.();
            onLog?.('warning', '比赛时间到', {
              timestamp: Date.now()
            });
            return { ...prev, time: newTime, isRunning: false };
          }
          
          return { ...prev, time: newTime };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [timer.isRunning, timer.time, onTimeUp, onLog]);

  // 初始时间变化时重置计时器
  useEffect(() => {
    setTimer({
      time: initialTime,
      isRunning: false,
      maxTime: initialTime
    });

    onLog?.('info', '计时器时间更新', {
      newTime: initialTime,
      timestamp: Date.now()
    });
  }, [initialTime, onLog]);

  return {
    timer,
    formatTime,
    startTimer,
    pauseTimer,
    resetTimer,
    // 便利属性
    isRunning: timer.isRunning,
    remainingTime: timer.time,
    formattedTime: formatTime(timer.time),
    isTimeUp: timer.time === 0,
    // 计算进度百分比（用于可能的进度条显示）
    progress: timer.maxTime > 0 ? ((timer.maxTime - timer.time) / timer.maxTime) * 100 : 0
  };
}
