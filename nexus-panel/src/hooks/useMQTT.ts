/**
 * MQTT React Hook
 *
 * 提供React组件中使用MQTT服务的便捷接口
 */

import { useEffect, useRef, useState, useCallback } from "react";
import { MQTTService } from "../services/mqtt/MQTTService";
import type {
	MQTTConfig,
	MQTTTopicStructure,
	MQTTMessageHandler,
	MQTTMessageData,
} from "../services/mqtt/types";
import { MQTTConnectionStatus } from "../services/mqtt/types";

interface UseMQTTOptions {
	/** MQTT配置 */
	config: MQTTConfig;
	/** 是否自动连接 */
	autoConnect?: boolean;
	/** 是否启用调试日志 */
	debug?: boolean;
}

interface UseMQTTReturn {
	/** 连接状态 */
	connectionStatus: MQTTConnectionStatus;
	/** 是否已连接 */
	isConnected: boolean;
	/** 连接错误信息 */
	error: string | null;
	/** MQTT服务实例（用于内存管理等高级功能） */
	service: MQTTService | null;
	/** 手动连接 */
	connect: () => Promise<void>;
	/** 断开连接 */
	disconnect: () => void;
	/** 手动重连（重置重连计数） */
	reconnect: () => Promise<void>;
	/** 发布消息 */
	publish: (topicStructure: MQTTTopicStructure, data: MQTTMessageData) => void;
	/** 订阅Topic */
	subscribe: (topic: string, handler: MQTTMessageHandler) => void;
	/** 取消订阅 */
	unsubscribe: (topic: string, handler?: MQTTMessageHandler) => void;
	/** 获取客户端信息 */
	getClientInfo: () => {
		clientId: string;
		brokerUrl: string;
		status: MQTTConnectionStatus;
	} | null;
}

export function useMQTT(options: UseMQTTOptions): UseMQTTReturn {
	const { config, autoConnect = true, debug = false } = options;

	const [connectionStatus, setConnectionStatus] =
		useState<MQTTConnectionStatus>(MQTTConnectionStatus.DISCONNECTED);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);

	const mqttServiceRef = useRef<MQTTService | null>(null);
	const subscribedTopicsRef = useRef<Set<string>>(new Set());

	// 初始化MQTT服务
	useEffect(() => {
		if (!mqttServiceRef.current) {
			mqttServiceRef.current = new MQTTService(config);

			// 注册事件处理器
			mqttServiceRef.current.on("statusChange", (status) => {
				setConnectionStatus(status);
				if (debug) {
					console.log(`MQTT状态变化: ${status}`);
				}
			});

			mqttServiceRef.current.on("error", (err) => {
				setError(err.message);
				if (debug) {
					console.error("MQTT错误:", err);
				}
			});

			mqttServiceRef.current.on("message", (topic, message) => {
				if (debug) {
					console.log(`MQTT消息接收 [${topic}]:`, message);
				}
			});
		}

		return () => {
			if (mqttServiceRef.current) {
				mqttServiceRef.current.disconnect();
				mqttServiceRef.current = null;
			}
		};
	}, [config, debug]);

	// 连接函数
	const connect = useCallback(async (): Promise<void> => {
		if (!mqttServiceRef.current) {
			throw new Error("MQTT服务未初始化");
		}

		if (connectionStatus === MQTTConnectionStatus.CONNECTED) {
			if (debug) {
				console.log("MQTT已连接，跳过连接请求");
			}
			return;
		}

		try {
			setError(null);
			await mqttServiceRef.current.connect();
			setRetryCount(0);

			if (debug) {
				console.log("MQTT连接成功");
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : String(err);
			setError(errorMessage);

			// 不在这里进行重试，让MQTTService的智能重连策略处理
			if (debug) {
				console.log("MQTT连接失败，智能重连策略将自动处理重连");
			}

			throw err;
		}
	}, [connectionStatus, debug]);

	// 自动连接 - 只在初始化时连接，避免与智能重连策略冲突
	useEffect(() => {
		if (
			autoConnect &&
			mqttServiceRef.current &&
			connectionStatus === MQTTConnectionStatus.DISCONNECTED &&
			retryCount === 0 // 只在初始状态时自动连接
		) {
			connect();
		}
	}, [autoConnect, connectionStatus, connect, retryCount]);

	// 断开连接函数
	const disconnect = useCallback((): void => {
		if (mqttServiceRef.current) {
			mqttServiceRef.current.disconnect();
			subscribedTopicsRef.current.clear();
			setError(null);
			setRetryCount(0);

			if (debug) {
				console.log("MQTT连接已断开");
			}
		}
	}, [debug]);

	// 发布消息函数
	const publish = useCallback(
		(topicStructure: MQTTTopicStructure, data: MQTTMessageData): void => {
			if (!mqttServiceRef.current) {
				throw new Error("MQTT服务未初始化");
			}

			if (connectionStatus !== MQTTConnectionStatus.CONNECTED) {
				throw new Error("MQTT未连接，无法发布消息");
			}

			try {
				mqttServiceRef.current.publish(topicStructure, data);

				if (debug) {
					console.log("MQTT消息发布:", { topicStructure, data });
				}
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : String(err);
				setError(errorMessage);
				throw err;
			}
		},
		[connectionStatus, debug],
	);

	// 订阅函数
	const subscribe = useCallback(
		(topic: string, handler: MQTTMessageHandler): void => {
			if (!mqttServiceRef.current) {
				throw new Error("MQTT服务未初始化");
			}

			if (connectionStatus !== MQTTConnectionStatus.CONNECTED) {
				throw new Error("MQTT未连接，无法订阅");
			}

			try {
				mqttServiceRef.current.subscribe(topic, handler);
				subscribedTopicsRef.current.add(topic);

				if (debug) {
					console.log(`MQTT订阅: ${topic}`);
				}
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : String(err);
				setError(errorMessage);
				throw err;
			}
		},
		[connectionStatus, debug],
	);

	// 取消订阅函数
	const unsubscribe = useCallback(
		(topic: string, handler?: MQTTMessageHandler): void => {
			if (!mqttServiceRef.current) {
				return;
			}

			try {
				mqttServiceRef.current.unsubscribe(topic, handler);

				if (!handler) {
					subscribedTopicsRef.current.delete(topic);
				}

				if (debug) {
					console.log(`MQTT取消订阅: ${topic}`);
				}
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : String(err);
				setError(errorMessage);
				if (debug) {
					console.error("取消订阅失败:", err);
				}
			}
		},
		[debug],
	);

	// 获取客户端信息函数
	const getClientInfo = useCallback(() => {
		return mqttServiceRef.current?.getClientInfo() || null;
	}, []);

	// 清理订阅
	useEffect(() => {
		const currentTopics = subscribedTopicsRef.current;
		const currentMqttService = mqttServiceRef.current;

		return () => {
			// 组件卸载时清理所有订阅
			currentTopics.forEach((topic) => {
				if (currentMqttService) {
					currentMqttService.unsubscribe(topic);
				}
			});
			currentTopics.clear();
		};
	}, []);

	// 手动重连函数
	const reconnect = useCallback(async (): Promise<void> => {
		if (!mqttServiceRef.current) {
			throw new Error("MQTT服务未初始化");
		}

		try {
			setError(null);
			setRetryCount(0); // 重置重试计数
			await mqttServiceRef.current.reconnect();

			if (debug) {
				console.log("MQTT手动重连成功");
			}
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : String(err);
			setError(errorMessage);
			throw err;
		}
	}, [debug]);

	return {
		connectionStatus,
		isConnected: connectionStatus === MQTTConnectionStatus.CONNECTED,
		error,
		service: mqttServiceRef.current,
		connect,
		disconnect,
		reconnect, // 添加手动重连方法
		publish,
		subscribe,
		unsubscribe,
		getClientInfo,
	};
}

// 便捷的订阅Hook
export function useMQTTSubscription(
	mqtt: UseMQTTReturn,
	topic: string,
	handler: MQTTMessageHandler,
) {
	useEffect(() => {
		if (mqtt.isConnected) {
			mqtt.subscribe(topic, handler);

			return () => {
				mqtt.unsubscribe(topic, handler);
			};
		}
	}, [mqtt, topic, handler]);
}

// 预定义的订阅模式
export const MQTTSubscriptionPatterns = {
	/** 订阅所有环节控制消息 */
	QUIZ_ALL: "quiz/#",
	/** 订阅所有显示消息 */
	DISPLAY_ALL: "display/#",
	/** 订阅所有系统消息 */
	SYSTEM_ALL: "system/#",
	/** 订阅特定选手的消息 */
	PLAYER: (playerId: number) => `display/player/player-${playerId}/#`,
	/** 订阅大屏显示消息 */
	SCREEN: "display/+/screen/#",
	/** 订阅环节开始消息 */
	SESSION_START: "quiz/session/+/start",
};
