/**
 * 题目导航专用 Hook
 * 
 * 封装题目切换的业务逻辑，包括题目序号管理、边界检查、本地存储等功能
 */

import { useState, useCallback, useEffect, useMemo } from 'react';
import type { ProcessedQuestionItem } from '../services/api/types';

// ==================== 类型定义 ====================

/**
 * 题目导航状态接口
 */
export interface QuestionNavigationState {
	/** 当前题目序号（0表示初始状态："比赛即将开始"） */
	currentQuestionNumber: number;
	/** 当前题目数据 */
	currentQuestion: ProcessedQuestionItem | null;
	/** 是否为初始状态 */
	isInitialState: boolean;
	/** 是否为第一题 */
	isFirstQuestion: boolean;
	/** 是否为最后一题 */
	isLastQuestion: boolean;
	/** 题目总数 */
	totalQuestions: number;
}

/**
 * 日志记录函数类型
 */
export type LogFunction = (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;

/**
 * 题目导航Hook配置选项
 */
export interface UseQuestionNavigationOptions {
	/** 题目数据列表 */
	questions: ProcessedQuestionItem[] | null;
	/** 初始题目序号，默认为0（初始状态） */
	initialQuestionNumber?: number;
	/** 是否启用本地存储，默认为true */
	enableLocalStorage?: boolean;
	/** 本地存储键名前缀，默认为'question-navigation' */
	storageKeyPrefix?: string;
	/** 题目切换回调 */
	onQuestionChange?: (questionNumber: number, question: ProcessedQuestionItem | null) => void;
	/** 边界检查失败回调 */
	onBoundaryError?: (type: 'first' | 'last', currentNumber: number) => void;
	/** 日志记录函数（用于事件处理器） */
	onLog?: LogFunction;
}

/**
 * 题目导航事件处理器接口
 */
export interface QuestionNavigationHandlers {
	/** 处理上一题按钮点击 */
	handlePrevious: () => void;
	/** 处理下一题按钮点击 */
	handleNext: () => void;
	/** 处理题目跳转（接收表单状态） */
	handleJump: (formState: Record<string, string | number>) => void;
}

/**
 * 题目导航Hook返回值
 */
export interface UseQuestionNavigationReturn extends QuestionNavigationState {
	/** 切换到上一题 */
	goToPrevious: () => boolean;
	/** 切换到下一题 */
	goToNext: () => boolean;
	/** 跳转到指定题目 */
	goToQuestion: (questionNumber: number) => boolean;
	/** 重置到初始状态 */
	resetToInitial: () => void;
	/** 检查题目序号是否有效 */
	isValidQuestionNumber: (questionNumber: number) => boolean;
	/** 获取题目序号的有效范围 */
	getValidRange: () => { min: number; max: number };
	/** 事件处理器（包含日志记录和验证逻辑） */
	handlers: QuestionNavigationHandlers;
}

// ==================== 本地存储工具 ====================

/**
 * 生成本地存储键名
 */
const generateStorageKey = (prefix: string, sectionName?: string): string => {
	return sectionName ? `${prefix}-${sectionName}` : prefix;
};

/**
 * 从本地存储加载题目序号
 */
const loadQuestionNumberFromStorage = (storageKey: string, defaultValue: number): number => {
	try {
		const stored = localStorage.getItem(storageKey);
		if (stored !== null) {
			const parsed = parseInt(stored, 10);
			return isNaN(parsed) ? defaultValue : parsed;
		}
	} catch (error) {
		console.warn('[题目导航] 从本地存储加载失败:', error);
	}
	return defaultValue;
};

/**
 * 保存题目序号到本地存储
 */
const saveQuestionNumberToStorage = (storageKey: string, questionNumber: number): void => {
	try {
		localStorage.setItem(storageKey, questionNumber.toString());
	} catch (error) {
		console.warn('[题目导航] 保存到本地存储失败:', error);
	}
};

// ==================== 主要 Hook ====================

/**
 * 题目导航Hook
 * 
 * @param options - Hook配置选项
 * @returns 题目导航状态和控制方法
 */
export function useQuestionNavigation(options: UseQuestionNavigationOptions): UseQuestionNavigationReturn {
	const {
		questions,
		initialQuestionNumber = 0,
		enableLocalStorage = true,
		storageKeyPrefix = 'question-navigation',
		onQuestionChange,
		onBoundaryError,
		onLog,
	} = options;

	// 生成存储键名（可以根据环节名称区分）
	const storageKey = useMemo(() => {
		// 如果有题目数据，使用第一个题目的环节名称作为区分
		const sectionName = questions && questions.length > 0 ? questions[0].section : undefined;
		return generateStorageKey(storageKeyPrefix, sectionName);
	}, [storageKeyPrefix, questions]);

	// 初始化当前题目序号（从本地存储加载或使用默认值）
	const [currentQuestionNumber, setCurrentQuestionNumber] = useState<number>(() => {
		if (enableLocalStorage && questions && questions.length > 0) {
			return loadQuestionNumberFromStorage(storageKey, initialQuestionNumber);
		}
		return initialQuestionNumber;
	});

	// 计算派生状态
	const derivedState = useMemo((): Omit<QuestionNavigationState, 'currentQuestionNumber'> => {
		const totalQuestions = questions ? questions.length : 0;
		const isInitialState = currentQuestionNumber === 0;
		const isFirstQuestion = currentQuestionNumber === 1;
		const isLastQuestion = currentQuestionNumber === totalQuestions;

		// 查找当前题目数据
		let currentQuestion: ProcessedQuestionItem | null = null;
		if (!isInitialState && questions) {
			currentQuestion = questions.find(q => q.questionNumber === currentQuestionNumber) || null;
		}

		return {
			currentQuestion,
			isInitialState,
			isFirstQuestion,
			isLastQuestion,
			totalQuestions,
		};
	}, [currentQuestionNumber, questions]);

	// 检查题目序号是否有效
	const isValidQuestionNumber = useCallback((questionNumber: number): boolean => {
		if (questionNumber === 0) return true; // 初始状态总是有效的
		if (!questions || questions.length === 0) return false;
		return questionNumber >= 1 && questionNumber <= questions.length;
	}, [questions]);

	// 获取有效范围
	const getValidRange = useCallback(() => {
		const totalQuestions = questions ? questions.length : 0;
		return { min: 0, max: totalQuestions };
	}, [questions]);

	// 更新题目序号的通用方法
	const updateQuestionNumber = useCallback((newQuestionNumber: number): boolean => {
		if (!isValidQuestionNumber(newQuestionNumber)) {
			return false;
		}

		setCurrentQuestionNumber(newQuestionNumber);

		// 保存到本地存储
		if (enableLocalStorage) {
			saveQuestionNumberToStorage(storageKey, newQuestionNumber);
		}

		// 触发回调
		const newQuestion = newQuestionNumber === 0 ? null :
			(questions?.find(q => q.questionNumber === newQuestionNumber) || null);
		onQuestionChange?.(newQuestionNumber, newQuestion);

		return true;
	}, [isValidQuestionNumber, enableLocalStorage, storageKey, onQuestionChange, questions]);

	// 切换到上一题
	const goToPrevious = useCallback((): boolean => {
		const newNumber = Math.max(0, currentQuestionNumber - 1);

		if (newNumber === currentQuestionNumber) {
			// 已经是边界，触发错误回调
			onBoundaryError?.('first', currentQuestionNumber);
			return false;
		}

		return updateQuestionNumber(newNumber);
	}, [currentQuestionNumber, updateQuestionNumber, onBoundaryError]);

	// 切换到下一题
	const goToNext = useCallback((): boolean => {
		const totalQuestions = questions ? questions.length : 0;
		const newNumber = Math.min(totalQuestions, currentQuestionNumber + 1);

		if (newNumber === currentQuestionNumber) {
			// 已经是边界，触发错误回调
			onBoundaryError?.('last', currentQuestionNumber);
			return false;
		}

		return updateQuestionNumber(newNumber);
	}, [currentQuestionNumber, questions, updateQuestionNumber, onBoundaryError]);

	// 跳转到指定题目
	const goToQuestion = useCallback((questionNumber: number): boolean => {
		return updateQuestionNumber(questionNumber);
	}, [updateQuestionNumber]);

	// 重置到初始状态
	const resetToInitial = useCallback((): void => {
		updateQuestionNumber(0);
	}, [updateQuestionNumber]);

	// ==================== 事件处理器 ====================

	// 处理上一题按钮点击（包含验证和日志记录）
	const handlePrevious = useCallback(() => {
		// 预检查：验证题目数据可用性
		if (!questions || questions.length === 0) {
			onLog?.('warning', '无法切换到上一题：题目数据不可用', {
				hasQuestionData: !!questions,
				questionCount: questions?.length || 0,
				timestamp: Date.now(),
				action: 'question_previous_data_unavailable'
			});
			return;
		}

		// 预检查：验证边界条件
		if (derivedState.isInitialState || derivedState.isFirstQuestion) {
			onLog?.('warning', '无法切换到上一题：已在边界位置', {
				currentQuestionNumber,
				isInitialState: derivedState.isInitialState,
				isFirstQuestion: derivedState.isFirstQuestion,
				timestamp: Date.now(),
				action: 'question_previous_boundary_check'
			});
			return;
		}

		const success = goToPrevious();
		if (success) {
			onLog?.('info', '切换到上一题', {
				currentQuestionNumber,
				previousQuestionNumber: currentQuestionNumber + 1,
				totalQuestions: derivedState.totalQuestions,
				timestamp: Date.now(),
				action: 'question_previous_success'
			});
		}
	}, [questions, derivedState, currentQuestionNumber, goToPrevious, onLog]);

	// 处理下一题按钮点击（包含验证和日志记录）
	const handleNext = useCallback(() => {
		// 预检查：验证题目数据可用性
		if (!questions || questions.length === 0) {
			onLog?.('warning', '无法切换到下一题：题目数据不可用', {
				hasQuestionData: !!questions,
				questionCount: questions?.length || 0,
				timestamp: Date.now(),
				action: 'question_next_data_unavailable'
			});
			return;
		}

		// 预检查：验证边界条件
		if (derivedState.isLastQuestion) {
			onLog?.('warning', '无法切换到下一题：已在最后一题', {
				currentQuestionNumber,
				totalQuestions: derivedState.totalQuestions,
				isLastQuestion: derivedState.isLastQuestion,
				timestamp: Date.now(),
				action: 'question_next_boundary_check'
			});
			return;
		}

		const success = goToNext();
		if (success) {
			onLog?.('info', '切换到下一题', {
				currentQuestionNumber,
				previousQuestionNumber: currentQuestionNumber - 1,
				totalQuestions: derivedState.totalQuestions,
				timestamp: Date.now(),
				action: 'question_next_success'
			});
		}
	}, [questions, derivedState, currentQuestionNumber, goToNext, onLog]);

	// 处理题目跳转（包含验证和日志记录）
	const handleJump = useCallback((formState: Record<string, string | number>) => {
		// 预检查：验证题目数据可用性
		if (!questions || questions.length === 0) {
			onLog?.('warning', '无法跳转题目：题目数据不可用', {
				hasQuestionData: !!questions,
				questionCount: questions?.length || 0,
				input: formState.questionNumber,
				timestamp: Date.now(),
				action: 'question_jump_data_unavailable'
			});
			return;
		}

		const questionNumber = Number(formState.questionNumber);
		if (isNaN(questionNumber)) {
			onLog?.('warning', '无效的题目序号：输入不是数字', {
				input: formState.questionNumber,
				inputType: typeof formState.questionNumber,
				timestamp: Date.now(),
				action: 'question_jump_invalid_input'
			});
			return;
		}

		// 增强的范围验证
		const validRange = getValidRange();
		if (questionNumber < validRange.min || questionNumber > validRange.max) {
			onLog?.('warning', '题目序号超出有效范围', {
				input: questionNumber,
				validRange,
				totalQuestions: derivedState.totalQuestions,
				timestamp: Date.now(),
				action: 'question_jump_out_of_range'
			});
			return;
		}

		const success = goToQuestion(questionNumber);
		if (success) {
			onLog?.('info', `跳转到第${questionNumber}题`, {
				questionNumber,
				previousQuestionNumber: currentQuestionNumber,
				totalQuestions: derivedState.totalQuestions,
				timestamp: Date.now(),
				action: 'question_jump_success'
			});
		} else {
			onLog?.('error', `跳转失败：无法跳转到第${questionNumber}题`, {
				questionNumber,
				validRange,
				currentQuestionNumber,
				timestamp: Date.now(),
				action: 'question_jump_failed'
			});
		}
	}, [questions, derivedState, currentQuestionNumber, getValidRange, goToQuestion, onLog]);

	// 组合事件处理器
	const handlers = useMemo((): QuestionNavigationHandlers => ({
		handlePrevious,
		handleNext,
		handleJump,
	}), [handlePrevious, handleNext, handleJump]);

	// 当题目数据变化时，验证当前题目序号的有效性
	useEffect(() => {
		if (questions && questions.length > 0) {
			// 如果当前序号无效，重置到初始状态
			if (!isValidQuestionNumber(currentQuestionNumber)) {
				updateQuestionNumber(0);
			}
		}
	}, [questions, currentQuestionNumber, isValidQuestionNumber, updateQuestionNumber]);

	return {
		currentQuestionNumber,
		...derivedState,
		goToPrevious,
		goToNext,
		goToQuestion,
		resetToInitial,
		isValidQuestionNumber,
		getValidRange,
		handlers,
	};
}
