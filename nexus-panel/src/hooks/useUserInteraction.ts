import { useState, useEffect, useCallback } from 'react';

/**
 * 用户交互状态接口
 */
export interface UserInteractionState {
	/** 用户是否已经与页面进行过交互 */
	hasInteracted: boolean;
	/** 首次交互的时间戳 */
	firstInteractionTime: number | null;
	/** 最后一次交互的时间戳 */
	lastInteractionTime: number | null;
	/** 交互次数 */
	interactionCount: number;
}

/**
 * 用户交互Hook返回值接口
 */
export interface UseUserInteractionReturn extends UserInteractionState {
	/** 手动标记用户已交互（用于特殊情况） */
	markAsInteracted: () => void;
	/** 重置交互状态 */
	resetInteractionState: () => void;
	/** 检查是否可以自动播放音频 */
	canAutoPlayAudio: () => boolean;
}

/**
 * 本地存储键名
 */
const STORAGE_KEY = 'nexus-panel-user-interaction';

/**
 * 默认交互状态
 */
const DEFAULT_STATE: UserInteractionState = {
	hasInteracted: false,
	firstInteractionTime: null,
	lastInteractionTime: null,
	interactionCount: 0,
};

/**
 * 从本地存储加载交互状态
 */
const loadInteractionState = (): UserInteractionState => {
	try {
		const stored = localStorage.getItem(STORAGE_KEY);
		if (stored) {
			const parsed = JSON.parse(stored);
			// 验证数据结构
			if (typeof parsed === 'object' && 
				typeof parsed.hasInteracted === 'boolean' &&
				typeof parsed.interactionCount === 'number') {
				return { ...DEFAULT_STATE, ...parsed };
			}
		}
	} catch (error) {
		console.warn('[用户交互] 加载本地存储失败:', error);
	}
	return DEFAULT_STATE;
};

/**
 * 保存交互状态到本地存储
 */
const saveInteractionState = (state: UserInteractionState): void => {
	try {
		localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
	} catch (error) {
		console.warn('[用户交互] 保存本地存储失败:', error);
	}
};

/**
 * 用户交互状态管理Hook
 * 
 * 功能特性：
 * - 自动检测用户交互事件（点击、触摸、键盘）
 * - 持久化交互状态到本地存储
 * - 提供音频自动播放权限检查
 * - 支持手动标记和重置交互状态
 * - 跟踪交互统计信息
 * 
 * 用于解决浏览器自动播放策略限制，确保音频播放功能正常工作
 * 
 * @returns 用户交互状态和控制方法
 */
export const useUserInteraction = (): UseUserInteractionReturn => {
	// 初始化状态（从本地存储加载）
	const [state, setState] = useState<UserInteractionState>(() => loadInteractionState());

	/**
	 * 更新交互状态
	 */
	const updateInteractionState = useCallback(() => {
		const now = Date.now();
		setState(prevState => {
			const newState: UserInteractionState = {
				hasInteracted: true,
				firstInteractionTime: prevState.firstInteractionTime || now,
				lastInteractionTime: now,
				interactionCount: prevState.interactionCount + 1,
			};
			
			// 保存到本地存储
			saveInteractionState(newState);
			
			console.log('[用户交互] 检测到用户交互:', {
				interactionCount: newState.interactionCount,
				timestamp: now,
			});
			
			return newState;
		});
	}, []);

	/**
	 * 手动标记用户已交互
	 */
	const markAsInteracted = useCallback(() => {
		console.log('[用户交互] 手动标记用户已交互');
		updateInteractionState();
	}, [updateInteractionState]);

	/**
	 * 重置交互状态
	 */
	const resetInteractionState = useCallback(() => {
		console.log('[用户交互] 重置交互状态');
		setState(DEFAULT_STATE);
		saveInteractionState(DEFAULT_STATE);
	}, []);

	/**
	 * 检查是否可以自动播放音频
	 */
	const canAutoPlayAudio = useCallback((): boolean => {
		// 基本交互检查
		if (!state.hasInteracted) {
			console.log('[用户交互] 无法自动播放：用户尚未与页面交互');
			return false;
		}

		// 检查交互时间（可选：确保交互不是太久之前）
		const maxInteractionAge = 30 * 60 * 1000; // 30分钟
		if (state.lastInteractionTime && 
			Date.now() - state.lastInteractionTime > maxInteractionAge) {
			console.log('[用户交互] 无法自动播放：最后交互时间过久');
			return false;
		}

		console.log('[用户交互] 可以自动播放音频');
		return true;
	}, [state.hasInteracted, state.lastInteractionTime]);

	/**
	 * 设置交互事件监听器
	 */
	useEffect(() => {
		// 需要监听的交互事件类型
		const interactionEvents = [
			'click',
			'touchstart',
			'keydown',
			'mousedown',
		] as const;

		/**
		 * 交互事件处理器
		 */
		const handleInteraction = (event: Event) => {
			// 只在首次交互时更新状态，避免过度更新
			if (!state.hasInteracted) {
				console.log('[用户交互] 首次检测到用户交互:', event.type);
				updateInteractionState();
			}
		};

		// 添加事件监听器
		interactionEvents.forEach(eventType => {
			document.addEventListener(eventType, handleInteraction, { 
				passive: true,
				capture: true 
			});
		});

		// 清理函数
		return () => {
			interactionEvents.forEach(eventType => {
				document.removeEventListener(eventType, handleInteraction, true);
			});
		};
	}, [state.hasInteracted, updateInteractionState]);

	return {
		...state,
		markAsInteracted,
		resetInteractionState,
		canAutoPlayAudio,
	};
};
