/**
 * App状态管理专用 Hook
 * 
 * 统一管理App级别的状态重置、验证和事件处理逻辑
 * 减少App.tsx中的重复代码，提高可维护性
 */

import { useCallback } from 'react';
import type { Key } from '@adobe/react-spectrum';
import type { useRaceApi } from './useRaceApi';
import type { UseQuestionNavigationReturn } from './useQuestionNavigation';
import type { ProcessedRulesIntroductionItem } from '../services/api/types';

// ==================== 类型定义 ====================

/**
 * 日志记录函数类型
 */
export type LogFunction = (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;

/**
 * 状态设置器类型
 */
export interface StateSetters {
	/** 设置选中的项目 */
	setSelectedProject: (project: string | null) => void;
	/** 设置选中的导航键 */
	setSelectedNavigationKey: (key: string | null) => void;
	/** 设置当前显示类型 */
	setCurrentDisplayType: (type: 'leader' | 'player' | 'award' | null) => void;
	/** 设置选中的规则介绍项 */
	setSelectedRulesIntroductionItem: (item: ProcessedRulesIntroductionItem | null) => void;
	/** 设置规则当前页码 */
	setRulesCurrentPage: (page: number) => void;
	/** 设置是否显示答案 */
	setShowAnswer: (show: boolean) => void;
	/** 设置是否显示解析 */
	setShowExplanation: (show: boolean) => void;
}

/**
 * App状态管理Hook配置选项
 */
export interface UseAppStateManagerOptions {
	/** 赛事API Hook返回值 */
	raceApi: ReturnType<typeof useRaceApi>;
	/** 题目导航Hook返回值 */
	questionNavigation: UseQuestionNavigationReturn;
	/** 状态设置器 */
	stateSetters: StateSetters;
	/** 日志记录函数 */
	onLog?: LogFunction;
	/** 内存统计更新回调 */
	onMemoryStatsUpdate?: () => void;
	/** MQTT集成处理器（可选） */
	mqttIntegration?: {
		handleProjectChange: (projectId: string | null) => void;
	};
}

/**
 * App状态管理事件处理器接口
 */
export interface AppStateManagerHandlers {
	/** 处理导航选择变化 */
	handleNavigationSelectionChange: (selectedKey: string | null) => void;
	/** 处理刷新操作 */
	handleRefresh: () => void;
	/** 处理项目选择变化 */
	handleProjectSelectionChange: (key: Key | null) => void;
}

/**
 * App状态管理Hook返回值
 */
export interface UseAppStateManagerReturn {
	/** 事件处理器 */
	handlers: AppStateManagerHandlers;
	/** 重置导航相关状态 */
	resetNavigationState: () => void;
	/** 重置项目相关状态 */
	resetProjectState: () => void;
	/** 验证题目数据可用性 */
	validateQuestionData: () => boolean;
}

// ==================== 主要 Hook ====================

/**
 * App状态管理Hook
 * 
 * @param options - Hook配置选项
 * @returns App状态管理器和事件处理器
 */
export function useAppStateManager(options: UseAppStateManagerOptions): UseAppStateManagerReturn {
	const {
		raceApi,
		questionNavigation,
		stateSetters,
		onLog,
		mqttIntegration,
	} = options;

	// ==================== 状态重置方法 ====================

	// 重置导航相关状态
	const resetNavigationState = useCallback(() => {
		stateSetters.setCurrentDisplayType(null);
		stateSetters.setSelectedRulesIntroductionItem(null);
		stateSetters.setRulesCurrentPage(1);
		questionNavigation.resetToInitial();
		stateSetters.setShowAnswer(true);
		stateSetters.setShowExplanation(false);
		raceApi.setCurrentStage(null);
	}, [stateSetters, questionNavigation, raceApi]);

	// 重置项目相关状态
	const resetProjectState = useCallback(() => {
		stateSetters.setSelectedProject(null);
		stateSetters.setSelectedNavigationKey(null);
		stateSetters.setCurrentDisplayType(null);
		raceApi.resetAllProjectData();
	}, [stateSetters, raceApi]);

	// ==================== 验证方法 ====================

	// 验证题目数据可用性
	const validateQuestionData = useCallback((): boolean => {
		return !!(raceApi.questionData && raceApi.questionData.length > 0);
	}, [raceApi.questionData]);

	// ==================== 事件处理器 ====================

	// 处理导航选择变化
	const handleNavigationSelectionChange = useCallback((selectedKey: string | null) => {
		stateSetters.setSelectedNavigationKey(selectedKey);

		// 重置导航相关状态
		resetNavigationState();

		// 导航选择已变化 - 调试日志已移除

		// TODO: 这里需要处理具体的导航逻辑（规则介绍、题目数据获取等）
		// 这部分逻辑比较复杂，暂时保留在App.tsx中
	}, [stateSetters, resetNavigationState, onLog]);

	// 处理刷新操作
	const handleRefresh = useCallback(() => {
		resetProjectState();

		// 重新获取赛事数据
		raceApi.refetch();

		// 记录刷新操作
		onLog?.('info', '页面已刷新，正在重新加载数据（包含所有项目数据重置）', {
			timestamp: Date.now(),
			action: 'manual_refresh',
			resetStates: {
				selectedProject: 'null',
				selectedNavigationKey: 'null',
				currentDisplayType: 'null',
				allProjectData: 'reset',
				raceData: 'refetching'
			}
		});
	}, [resetProjectState, raceApi, onLog]);

	// 处理项目选择变化
	const handleProjectSelectionChange = useCallback(async (key: Key | null) => {
		const projectId = key as string | null;

		// 项目选择变化处理开始 - 调试日志已移除

		stateSetters.setSelectedProject(projectId);

		// 重置导航选择状态
		stateSetters.setSelectedNavigationKey(null);

		if (projectId) {
			// 开始处理项目ID - 调试日志已移除

			// 先预加载表结构数据，避免后续重复请求
			try {
				// 开始调用ApiPreloader - 调试日志已移除

				const { ApiPreloader } = await import('../services/api/preloader');
				await ApiPreloader.preloadProjectData(projectId);

				// ApiPreloader调用完成 - 调试日志已移除
			} catch (preloadError) {
				console.error('❌ useAppStateManager ApiPreloader.preloadProjectData 调用失败', {
					projectId,
					preloadError,
					timestamp: Date.now(),
					action: 'api_preloader_error'
				});

				onLog?.('warning', '项目数据预加载失败，继续正常流程', {
					projectId,
					error: preloadError instanceof Error ? preloadError.message : String(preloadError),
					timestamp: Date.now(),
					action: 'project_preload_warning'
				});
			}

			// 使用合并数据获取方法，一次性获取所有项目数据
			console.log('🎯 useAppStateManager 准备调用 fetchAllProjectData', {
				projectId,
				hasRaceApi: !!raceApi,
				hasFetchAllProjectData: !!raceApi?.fetchAllProjectData,
				timestamp: Date.now(),
				action: 'about_to_call_fetch_all_project_data'
			});

			onLog?.('info', '🎯 useAppStateManager 准备调用 fetchAllProjectData', {
				projectId,
				timestamp: Date.now(),
				action: 'about_to_call_fetch_all_project_data'
			});

			raceApi.fetchAllProjectData(projectId).then(() => {
				console.log('✅ useAppStateManager fetchAllProjectData 调用成功', {
					projectId,
					timestamp: Date.now(),
					action: 'fetch_all_project_data_success_in_manager'
				});

				onLog?.('success', '✅ useAppStateManager fetchAllProjectData 调用成功', {
					projectId,
					timestamp: Date.now(),
					action: 'fetch_all_project_data_success_in_manager'
				});
			}).catch((error) => {
				console.error('❌ useAppStateManager fetchAllProjectData 调用失败', {
					error,
					projectId,
					timestamp: Date.now(),
					action: 'fetch_all_project_data_error_in_manager'
				});

				onLog?.('error', '❌ useAppStateManager fetchAllProjectData 调用失败', {
					error: error.message,
					projectId,
					timestamp: Date.now(),
					action: 'fetch_all_project_data_error_in_manager'
				});
			});
		} else {
			// 如果取消选择，重置所有项目数据
			raceApi.resetAllProjectData();
			stateSetters.setCurrentDisplayType(null);
		}

		// 使用 MQTT 集成 Hook 发送项目消息
		mqttIntegration?.handleProjectChange(projectId);

		// 项目选择已变化 - 调试日志已移除
	}, [stateSetters, raceApi, onLog, mqttIntegration]);



	// 组合事件处理器
	const handlers: AppStateManagerHandlers = {
		handleNavigationSelectionChange,
		handleRefresh,
		handleProjectSelectionChange,
	};

	return {
		handlers,
		resetNavigationState,
		resetProjectState,
		validateQuestionData,
	};
}
