/**
 * 通用 API 请求状态管理 Hook
 * 
 * 提供统一的 API 请求状态管理，包括 loading、error、data 状态
 * 支持自动执行、手动执行、重试等功能
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import type { UseApiOptions, UseApiReturn, ApiError } from '../services/api/types';
import { ApiStatus } from '../services/api/types';
import { ApiErrorHandler } from '../services/api/errors';

/**
 * 通用 API Hook
 * 
 * @param apiFunction - 要执行的 API 函数
 * @param options - Hook 配置选项
 * @returns API 状态和控制方法
 */
export function useApi<T = unknown>(
	apiFunction: () => Promise<T>,
	options: UseApiOptions<T> = {}
): UseApiReturn<T> {
	const {
		immediate = false,
		onSuccess,
		onError,
		onStart,
		onFinally,
	} = options;

	// 状态管理
	const [data, setData] = useState<T | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<ApiError | null>(null);
	const [status, setStatus] = useState<ApiStatus>(ApiStatus.IDLE);



	// 引用管理
	const isMountedRef = useRef(true);
	const abortControllerRef = useRef<AbortController | null>(null);
	const hasExecutedRef = useRef(false); // 防止 StrictMode 重复执行

	// 稳定化回调函数引用
	const callbacksRef = useRef({ onSuccess, onError, onStart, onFinally });
	callbacksRef.current = { onSuccess, onError, onStart, onFinally };

	// 清理函数
	useEffect(() => {
		// 组件挂载时重置状态（处理 StrictMode 重复挂载）
		isMountedRef.current = true;
		hasExecutedRef.current = false;

		return () => {
			isMountedRef.current = false;
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, []);

	// 执行 API 请求
	const execute = useCallback(async (): Promise<void> => {
		// 如果组件已卸载，不执行请求
		if (!isMountedRef.current) {
			return;
		}

		// 取消之前的请求
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}

		// 创建新的 AbortController
		abortControllerRef.current = new AbortController();

		try {
			// 设置加载状态
			setLoading(true);
			setError(null);
			setStatus(ApiStatus.LOADING);

			// 调用开始回调
			callbacksRef.current.onStart?.();

			// 执行 API 请求
			const result = await apiFunction();

			// 检查组件是否仍然挂载
			if (!isMountedRef.current) {
				return;
			}

			// 设置成功状态
			setData(result);
			setStatus(ApiStatus.SUCCESS);

			// 调用成功回调
			callbacksRef.current.onSuccess?.(result);

		} catch (err) {
			// 检查组件是否仍然挂载
			if (!isMountedRef.current) {
				return;
			}

			// 处理错误
			const apiError = ApiErrorHandler.createApiError(err);
			setError(apiError);
			setStatus(ApiStatus.ERROR);

			// 调用错误回调
			callbacksRef.current.onError?.(apiError);

			// 记录错误（仅在开发环境）
			if (process.env.NODE_ENV === 'development') {
				ApiErrorHandler.logError(apiError, 'useApi.execute');
			}

		} finally {
			// 检查组件是否仍然挂载，如果已卸载则跳过状态更新但仍执行清理
			if (isMountedRef.current) {
				// 清理加载状态
				setLoading(false);

				// 调用完成回调
				callbacksRef.current.onFinally?.();
			}

			// 无论组件是否挂载都需要清理 AbortController 以防止内存泄漏
			abortControllerRef.current = null;
		}
	}, [apiFunction]); // 只依赖 apiFunction，回调通过 ref 访问

	// 重置状态
	const reset = useCallback((): void => {
		// 取消正在进行的请求
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
			abortControllerRef.current = null;
		}

		// 重置所有状态
		setData(null);
		setLoading(false);
		setError(null);
		setStatus(ApiStatus.IDLE);
	}, []);

	// 重新执行请求（等同于 execute）
	const refetch = useCallback((): Promise<void> => {
		return execute();
	}, [execute]);

	// 自动执行（如果启用）
	useEffect(() => {
		if (immediate && !hasExecutedRef.current) {
			hasExecutedRef.current = true;
			execute();
		}
	}, [immediate, execute]); // 包含 execute 依赖

	return {
		data,
		loading,
		error,
		status,
		execute,
		reset,
		refetch,
	};
}

// ==================== 便捷 Hook 变体 ====================

/**
 * 自动执行的 API Hook
 * 组件挂载时自动执行 API 请求
 */
export function useApiImmediate<T = unknown>(
	apiFunction: () => Promise<T>,
	options: Omit<UseApiOptions<T>, 'immediate'> = {}
): UseApiReturn<T> {
	return useApi(apiFunction, { ...options, immediate: true });
}

/**
 * 手动执行的 API Hook
 * 需要手动调用 execute 方法执行请求
 */
export function useApiManual<T = unknown>(
	apiFunction: () => Promise<T>,
	options: Omit<UseApiOptions<T>, 'immediate'> = {}
): UseApiReturn<T> {
	return useApi(apiFunction, { ...options, immediate: false });
}

/**
 * 带重试功能的 API Hook
 */
export function useApiWithRetry<T = unknown>(
	apiFunction: () => Promise<T>,
	maxRetries: number = 3,
	retryDelay: number = 1000,
	options: UseApiOptions<T> = {}
): UseApiReturn<T> & { retry: () => Promise<void>; retryCount: number } {
	const [retryCount, setRetryCount] = useState(0);
	const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// 包装 API 函数以支持重试
	const apiWithRetry = useCallback(async (): Promise<T> => {
		let lastError: unknown;

		for (let attempt = 0; attempt <= maxRetries; attempt++) {
			try {
				const result = await apiFunction();
				setRetryCount(0); // 成功后重置重试计数
				return result;
			} catch (error) {
				lastError = error;

				// 如果不是最后一次尝试，等待后重试
				if (attempt < maxRetries) {
					setRetryCount(attempt + 1);
					await new Promise(resolve => {
						retryTimeoutRef.current = setTimeout(resolve, retryDelay * Math.pow(2, attempt));
					});
				}
			}
		}

		// 所有重试都失败，抛出最后的错误
		throw lastError;
	}, [apiFunction, maxRetries, retryDelay]);

	const apiResult = useApi(apiWithRetry, options);

	// 手动重试方法
	const retry = useCallback(async (): Promise<void> => {
		setRetryCount(0);
		return apiResult.execute();
	}, [apiResult]);

	// 清理定时器
	useEffect(() => {
		return () => {
			if (retryTimeoutRef.current) {
				clearTimeout(retryTimeoutRef.current);
			}
		};
	}, []);

	return {
		...apiResult,
		retry,
		retryCount,
	};
}
