/**
 * 争分夺秒排名数据管理Hook
 * 
 * 封装争分夺秒和同分加赛环节的数据获取、轮询和状态管理逻辑
 * 提供简洁的接口供组件使用，遵循关注点分离原则
 * 
 * 修复问题：显示完列表后马上回到"暂无争分夺秒排名数据"
 * 
 * 主要修复：
 * 1. 移除组件卸载时的数据清理逻辑
 * 2. 优化依赖项管理，避免不必要的重置
 * 3. 改进错误恢复机制
 * 4. 添加数据持久化逻辑
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRaceApi } from './useRaceApi';
import { globalTimerManager } from '../utils/globalTimerManager';
import type { RankingData, PaginatedRankingData } from '../services/api/types';
import type { LogFunction } from './useRaceApi';

/**
 * Hook配置选项
 */
export interface UseTimeRaceRankingOptions {
  /** 环节名称 */
  sectionName: '争分夺秒' | '同分加赛';
  /** 基础ID */
  baseId: string;
  /** 轮询间隔（毫秒），默认5000ms */
  pollingInterval?: number;
  /** 每页显示数量，默认8 */
  pageSize?: number;
  /** 初始页码，默认1 */
  initialPage?: number;
  /** 日志记录回调函数 */
  onLog?: LogFunction;
}

/**
 * Hook返回值
 */
export interface UseTimeRaceRankingReturn {
  /** 排名数据 */
  rankingData: RankingData | PaginatedRankingData | null;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: Error | null;
  /** 是否正在轮询 */
  isPolling: boolean;
  /** 当前页码 */
  currentPage: number;
  /** 最后更新时间 */
  lastUpdateTime: number;
  /** 手动刷新数据 */
  refresh: () => Promise<void>;
  /** 切换页面 */
  changePage: (page: number) => void;
  /** 开始轮询 */
  startPolling: () => void;
  /** 停止轮询 */
  stopPolling: () => void;
}

/**
 * 争分夺秒排名数据管理Hook
 */
export function useTimeRaceRanking(options: UseTimeRaceRankingOptions): UseTimeRaceRankingReturn {
  const {
    sectionName,
    baseId,
    pollingInterval = 5000,
    initialPage = 1,
    onLog
  } = options;

  // 使用useRaceApi Hook管理数据
  const {
    sectionRankingData,
    sectionRankingLoading,
    sectionRankingError,
    fetchSectionRankingData,
    startSectionRankingPolling,
    stopSectionRankingPolling,
    switchToSection,
    currentSectionName,
    playerListData,
    playerListLoading,
    playerListError,
    fetchPlayerListData,
  } = useRaceApi();

  // 内部状态管理
  const [isPollingActive, setIsPollingActive] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now());
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [manualError, setManualError] = useState<Error | null>(null);

  // 防止重复初始化的标志
  const hasInitialized = useRef(false);
  const isComponentMounted = useRef(true);

  // 合并错误状态
  const error = manualError || sectionRankingError;
  const loading = sectionRankingLoading || playerListLoading;

  // 检查是否为分页数据（暂时未使用）
  // const isPaginatedData = sectionRankingData && 'paginationInfo' in sectionRankingData;

  // 手动刷新数据
  const refresh = useCallback(async () => {
    // 强制日志 - 确认refresh函数被调用
    console.log(`[useTimeRaceRanking] refresh函数被调用`, {
      baseId,
      sectionName,
      timestamp: Date.now(),
      action: 'refresh_function_called'
    });

    const now = Date.now();

    // 防抖处理：如果距离上次更新不到2秒，则跳过
    if (now - lastUpdateTime < 2000) {
      onLog?.('info', `${sectionName}排名数据刷新过于频繁，跳过本次请求`, {
        sectionName,
        timeSinceLastUpdate: now - lastUpdateTime,
        timestamp: now,
        action: 'refresh_debounced'
      });
      return;
    }

    // 检查是否正在加载中
    if (loading) {
      onLog?.('info', `${sectionName}排名数据正在加载中，跳过手动刷新请求`, {
        sectionName,
        baseId,
        timestamp: now,
        action: 'manual_refresh_skip_loading'
      });
      return;
    }

    try {
      setManualError(null);
      setLastUpdateTime(now);

      onLog?.('info', `手动刷新${sectionName}排名数据`, {
        sectionName,
        baseId,
        timestamp: now,
        action: 'manual_refresh_start'
      });

      // 如果没有选手列表数据，先获取选手列表
      if (!playerListData || playerListError) {
        console.log(`[useTimeRaceRanking] 即将调用fetchPlayerListData`, {
          baseId,
          hasPlayerListData: !!playerListData,
          hasPlayerListError: !!playerListError,
          timestamp: Date.now()
        });
        try {
          await fetchPlayerListData(baseId);
          console.log(`[useTimeRaceRanking] fetchPlayerListData调用成功`, {
            baseId,
            timestamp: Date.now()
          });
        } catch (playerListError) {
          console.log(`[useTimeRaceRanking] fetchPlayerListData调用失败，但继续执行fetchSectionRankingData`, {
            baseId,
            error: playerListError instanceof Error ? playerListError.message : String(playerListError),
            timestamp: Date.now()
          });
          // 不要抛出异常，继续执行fetchSectionRankingData
        }
      }

      // 获取排名数据
      console.log(`[useTimeRaceRanking] 即将调用fetchSectionRankingData`, {
        baseId,
        sectionName,
        timestamp: Date.now()
      });
      await fetchSectionRankingData(baseId, sectionName);

      onLog?.('success', `${sectionName}排名数据刷新成功`, {
        sectionName,
        baseId,
        timestamp: Date.now(),
        action: 'manual_refresh_success'
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setManualError(new Error(errorMessage));

      onLog?.('error', `手动刷新${sectionName}排名数据失败`, {
        error: errorMessage,
        sectionName,
        baseId,
        timestamp: Date.now(),
        action: 'manual_refresh_error'
      });
    }
  }, [
    sectionName,
    baseId,
    lastUpdateTime,
    loading,
    playerListData,
    playerListError,
    fetchPlayerListData,
    fetchSectionRankingData,
    onLog
  ]);

  // 切换页面
  const changePage = useCallback((page: number) => {
    setCurrentPage(page);
    onLog?.('info', `${sectionName}排名切换到第${page}页`, {
      sectionName,
      page,
      timestamp: Date.now(),
      action: 'page_change'
    });
  }, [sectionName, onLog]);

  // 开始轮询
  const startPolling = useCallback(() => {
    if (!isPollingActive && isComponentMounted.current) {
      setIsPollingActive(true);
      startSectionRankingPolling(baseId, sectionName, pollingInterval);
      onLog?.('info', `开始${sectionName}排名数据轮询`, {
        sectionName,
        pollingInterval,
        timestamp: Date.now(),
        action: 'start_polling'
      });
    }
  }, [isPollingActive, startSectionRankingPolling, baseId, sectionName, pollingInterval, onLog]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (isPollingActive) {
      setIsPollingActive(false);
      stopSectionRankingPolling();
      onLog?.('info', `停止${sectionName}排名数据轮询`, {
        sectionName,
        timestamp: Date.now(),
        action: 'stop_polling'
      });
    }
  }, [isPollingActive, stopSectionRankingPolling, sectionName, onLog]);

  // 组件初始化：复用已有数据，避免重复请求
  useEffect(() => {
    if (baseId && sectionName && baseId !== 'default' && !hasInitialized.current && isComponentMounted.current) {
      hasInitialized.current = true;

      onLog?.('info', `${sectionName}Hook初始化，检查数据复用策略`, {
        sectionName,
        baseId,
        hasPlayerData: !!(playerListData && playerListData.length > 0),
        hasRankingData: !!sectionRankingData,
        playerListLoading,
        sectionRankingLoading,
        strategy: 'data_reuse_first',
        timestamp: Date.now(),
        action: 'hook_initialize_with_reuse_check'
      });

      // 优先复用已有的选手列表数据
      if (playerListData && playerListData.length > 0 && !playerListError) {
        onLog?.('success', `✅ 复用已有选手列表数据，避免重复请求`, {
          sectionName,
          baseId,
          playerCount: playerListData.length,
          timestamp: Date.now(),
          action: 'reuse_existing_player_data'
        });

        // 如果还没有排名数据且未在加载中，则获取排名数据
        if (!sectionRankingData && !sectionRankingLoading) {
          // 短暂延迟，让UI有时间显示选手列表
          setTimeout(() => {
            if (isComponentMounted.current && !sectionRankingData && !sectionRankingLoading) {
              onLog?.('info', `基于已有选手数据，异步获取${sectionName}排名数据`, {
                sectionName,
                baseId,
                timestamp: Date.now(),
                action: 'fetch_ranking_with_existing_players'
              });
              fetchSectionRankingData(baseId, sectionName);
            }
          }, 300);
        }
      } else if (!playerListLoading && (!playerListData || playerListError)) {
        // 没有有效的选手列表数据（为空或有错误）且未在加载中，直接获取排名数据
        if (playerListError) {
          onLog?.('info', `选手列表数据有错误，直接获取${sectionName}排名数据`, {
            sectionName,
            baseId,
            playerListError: playerListError.message,
            timestamp: Date.now(),
            action: 'skip_player_list_due_to_error'
          });
        } else {
          onLog?.('info', `没有可复用的选手数据，直接获取${sectionName}排名数据`, {
            sectionName,
            baseId,
            timestamp: Date.now(),
            action: 'fetch_ranking_no_player_data'
          });
        }

        // 直接获取排名数据，不依赖选手列表
        if (!sectionRankingData && !sectionRankingLoading) {
          fetchSectionRankingData(baseId, sectionName);
        }
      } else if (playerListLoading) {
        // 选手数据正在加载中，等待加载完成
        onLog?.('info', `选手数据正在加载中，等待完成后处理排名数据`, {
          sectionName,
          baseId,
          playerListLoading,
          timestamp: Date.now(),
          action: 'wait_for_player_data_loading'
        });
      } else {
        // 其他情况，直接获取排名数据
        onLog?.('info', `其他情况，直接获取${sectionName}排名数据`, {
          sectionName,
          baseId,
          hasPlayerData: !!playerListData,
          playerDataLength: playerListData?.length || 0,
          hasPlayerError: !!playerListError,
          timestamp: Date.now(),
          action: 'fetch_ranking_fallback'
        });

        if (!sectionRankingData && !sectionRankingLoading) {
          fetchSectionRankingData(baseId, sectionName);
        }
      }
    }
  }, [baseId, sectionName, fetchSectionRankingData, fetchPlayerListData, playerListData, playerListLoading, playerListError, sectionRankingData, sectionRankingLoading, onLog]);

  // 重置初始化标志当baseId或sectionName变化时
  useEffect(() => {
    hasInitialized.current = false;
  }, [baseId, sectionName]);

  // 当sectionName变化时，自动切换到新的环节并获取数据
  useEffect(() => {
    if (sectionName && currentSectionName !== sectionName) {
      switchToSection(sectionName);

      // 环节切换后，如果有有效的baseId，立即获取新环节的数据
      if (baseId && baseId !== 'default') {
        // 短暂延迟，确保switchToSection状态更新完成
        setTimeout(() => {
          onLog?.('info', `环节切换到${sectionName}，自动获取数据`, {
            sectionName,
            baseId,
            timestamp: Date.now(),
            action: 'auto_fetch_on_section_switch'
          });
          fetchSectionRankingData(baseId, sectionName);
        }, 100);
      }
    }
  }, [sectionName, currentSectionName, switchToSection, baseId, fetchSectionRankingData, onLog]);

  // 选手列表获取成功后，异步获取答题记录数据（仅在初始化未处理时）
  useEffect(() => {
    if (playerListData &&
      playerListData.length > 0 &&
      baseId &&
      sectionName &&
      baseId !== 'default' &&
      !sectionRankingData &&
      !sectionRankingLoading &&
      hasInitialized.current &&
      isComponentMounted.current) {

      onLog?.('info', `选手列表已就绪，开始异步获取${sectionName}答题记录数据`, {
        sectionName,
        baseId,
        playerCount: playerListData.length,
        timestamp: Date.now(),
        action: 'async_fetch_answer_records_after_players'
      });

      // 短暂延迟，让UI有时间显示选手列表
      const timer = setTimeout(() => {
        if (isComponentMounted.current) {
          fetchSectionRankingData(baseId, sectionName);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [playerListData, baseId, sectionName, fetchSectionRankingData, sectionRankingData, sectionRankingLoading, onLog]);

  // 计时器状态监听：当计时器启动时自动开始轮询
  useEffect(() => {
    const handleTimerStateChange = () => {
      if (!isComponentMounted.current) return;

      const timerState = globalTimerManager.getState();

      // 当计时器启动且轮询未激活时，自动开始轮询
      if (timerState.isRunning && !isPollingActive) {
        startPolling();
        onLog?.('info', `计时器启动，自动开始${sectionName}排名数据轮询`, {
          sectionName,
          pollingInterval,
          timerState: timerState,
          timestamp: Date.now(),
          action: 'auto_start_polling_on_timer'
        });
      }
    };

    // 添加监听器
    globalTimerManager.addListener(handleTimerStateChange);

    // 组件挂载时检查计时器当前状态
    const currentTimerState = globalTimerManager.getState();
    if (currentTimerState.isRunning && !isPollingActive && isComponentMounted.current) {
      handleTimerStateChange();
    }

    // 清理函数：移除监听器
    return () => {
      globalTimerManager.removeListener(handleTimerStateChange);
    };
  }, [isPollingActive, startPolling, sectionName, pollingInterval, onLog]);

  // 错误恢复机制：自动重试（但不清理数据）
  useEffect(() => {
    if (error && isPollingActive && isComponentMounted.current) {
      const retryTimer = setTimeout(() => {
        if (isComponentMounted.current) {
          onLog?.('info', `${sectionName}排名数据获取失败，正在自动重试...`, {
            sectionName,
            error: error.message,
            timestamp: Date.now(),
            action: 'auto_retry'
          });
          refresh();
        }
      }, 10000); // 10秒后自动重试

      return () => clearTimeout(retryTimer);
    }
  }, [error, isPollingActive, sectionName, onLog, refresh]);

  // 组件卸载时只停止轮询，不清理数据
  useEffect(() => {
    return () => {
      isComponentMounted.current = false;
      if (isPollingActive) {
        stopSectionRankingPolling();
      }
      // 移除数据清理逻辑，保持数据持久化
      onLog?.('info', `${sectionName}Hook卸载，停止轮询但保持数据`, {
        sectionName,
        timestamp: Date.now(),
        action: 'hook_unmount_preserve_data'
      });
    };
  }, [isPollingActive, stopSectionRankingPolling, sectionName, onLog]);

  // 数据获取成功时清除手动错误状态
  useEffect(() => {
    if (sectionRankingData && manualError) {
      setManualError(null);
      onLog?.('info', `${sectionName}数据获取成功，清除手动错误状态`, {
        sectionName,
        timestamp: Date.now(),
        action: 'clear_manual_error_on_success'
      });
    }
  }, [sectionRankingData, manualError, sectionName, onLog]);

  // 性能优化：只在真正的API数据更新时更新时间戳
  useEffect(() => {
    if (sectionRankingData && sectionRankingData.fetchTimestamp && isComponentMounted.current) {
      // 只有当fetchTimestamp存在且与当前显示时间不同时，才更新显示时间
      if (sectionRankingData.fetchTimestamp !== lastUpdateTime) {
        setLastUpdateTime(sectionRankingData.fetchTimestamp);
        onLog?.('info', '检测到API数据更新，更新显示时间戳', {
          sectionName,
          baseId,
          newTimestamp: sectionRankingData.fetchTimestamp,
          oldTimestamp: lastUpdateTime,
          timestamp: Date.now(),
          action: 'update_display_timestamp'
        });
      }
    }
  }, [sectionRankingData?.fetchTimestamp, lastUpdateTime, onLog, sectionName, baseId]);

  return {
    rankingData: sectionRankingData,
    loading,
    error,
    isPolling: isPollingActive,
    currentPage,
    lastUpdateTime,
    refresh,
    changePage,
    startPolling,
    stopPolling
  };
}
