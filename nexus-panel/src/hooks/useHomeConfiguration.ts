/**
 * 首页配置数据专用 Hook
 * 
 * 专门处理首页配置信息的获取、处理和状态管理
 */

import { useCallback, useMemo, useState, useEffect } from 'react';
import type { GroupedConfigurationData, ProcessedConfigurationItem, ApiError } from '../services/api/types';
import type { ButtonGroupConfig, TraditionalButtonGroupConfig } from '../config/buttonGroupConfigurations';
import { ButtonStyleType } from '../components/SidebarButtonStyles';

// ==================== 类型定义 ====================

/**
 * 日志记录函数类型
 */
export type LogFunction = (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;

/**
 * 首页配置 Hook 配置选项
 */
export interface UseHomeConfigurationOptions {
	/** 配置数据 */
	configurationData: GroupedConfigurationData | null;
	/** 配置数据加载状态 */
	configurationLoading: boolean;
	/** 配置数据错误信息 */
	configurationError: ApiError | null;
	/** 日志记录回调函数 */
	onLog?: LogFunction;
	/** 当前显示类型状态管理（用于事件处理器） */
	currentDisplayType?: 'leader' | 'player' | 'award' | null;
	/** 设置当前显示类型 */
	setCurrentDisplayType?: (type: 'leader' | 'player' | 'award' | null) => void;
	/** 选择器重置控制状态管理 */
	pickerResetControl?: { timestamp: number; selectedType: 'leader' | 'player' | 'award' | null };
	/** 设置选择器重置控制 */
	setPickerResetControl?: (control: { timestamp: number; selectedType: 'leader' | 'player' | 'award' | null }) => void;
	/** 添加日志的回调（用于延迟日志记录） */
	addConsoleLog?: LogFunction;
}

/**
 * 首页配置事件处理器接口
 */
export interface HomeConfigurationHandlers {
	/** 处理配置选择变化 */
	handleConfigurationSelectionChange: (type: 'leader' | 'player' | 'award', key: string) => void;
	/** 处理显示内容 */
	handleShowContent: (type: 'leader' | 'player' | 'award') => void;
	/** 处理带验证的显示内容 */
	handleShowContentWithValidation: (type: 'leader' | 'player' | 'award', formState: Record<string, string | number>) => void;
	/** 处理重置到主屏幕 */
	handleResetToMainScreen: () => void;
}

/**
 * 首页配置 Hook 返回值
 */
export interface UseHomeConfigurationReturn {
	/** 动态生成的按钮组配置 */
	dynamicButtonGroups: ButtonGroupConfig[];
	/** 当前选中的配置项 */
	selectedItems: {
		leader: ProcessedConfigurationItem | null;
		player: ProcessedConfigurationItem | null;
		award: ProcessedConfigurationItem | null;
	};
	/** 更新选中项 */
	updateSelectedItem: (type: 'leader' | 'player' | 'award', item: ProcessedConfigurationItem | null) => void;
	/** 是否有配置数据 */
	hasConfigurationData: boolean;
	/** 配置数据统计 */
	configurationStats: {
		leaderCount: number;
		playerCount: number;
		awardCount: number;
		totalCount: number;
	};
	/** 事件处理器（包含验证和日志记录逻辑） */
	handlers: HomeConfigurationHandlers;
}

// ==================== 辅助函数 ====================

/**
 * 创建按钮组配置的辅助函数
 */
function createButtonGroupConfig(config: Partial<TraditionalButtonGroupConfig> & { title: string }): TraditionalButtonGroupConfig {
	return {
		type: 'traditional',
		tooltipContent: "控制显示功能",
		buttons: [],
		formFields: [],
		additionalButtons: [],
		renderOrder: ["formFields", "buttons", "additionalButtons"],
		showBreakBeforeButtons: false,
		showBreakBeforeAdditionalButtons: false,
		...config,
	};
}

/**
 * 将配置数据转换为选择器选项
 */
function convertToPickerOptions(items: ProcessedConfigurationItem[]): Array<{ key: string; label: string }> {
	return items.map(item => ({
		key: item.id.toString(),
		label: item.title,
	}));
}

/**
 * 生成领导显示按钮组配置
 */
function generateLeaderButtonGroup(
	leaderItems: ProcessedConfigurationItem[],
	onSelectionChange: (key: string) => void
): ButtonGroupConfig {
	return createButtonGroupConfig({
		title: "领导显示",
		tooltipContent: "控制领导显示功能",
		formFields: [
			{
				label: "选择领导",
				name: "leaderSelection",
				type: "picker",
				placeholder: "请选择领导",
				selectedKey: undefined,
				options: convertToPickerOptions(leaderItems),
				onSelectionChange,
			},
		],
		buttons: [
			{ text: "显示领导", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	});
}

/**
 * 生成选手显示按钮组配置
 */
function generatePlayerButtonGroup(
	playerItems: ProcessedConfigurationItem[],
	onSelectionChange: (key: string) => void
): ButtonGroupConfig {
	return createButtonGroupConfig({
		title: "选手显示",
		tooltipContent: "控制选手显示功能",
		formFields: [
			{
				label: "选择选手",
				name: "playerSelection",
				type: "picker",
				placeholder: "请选择选手",
				selectedKey: undefined,
				options: convertToPickerOptions(playerItems),
				onSelectionChange,
			},
		],
		buttons: [
			{ text: "显示选手", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	});
}

/**
 * 生成奖项显示按钮组配置
 */
function generateAwardButtonGroup(
	awardItems: ProcessedConfigurationItem[],
	onSelectionChange: (key: string) => void
): ButtonGroupConfig {
	return createButtonGroupConfig({
		title: "奖项显示",
		tooltipContent: "控制奖项显示功能",
		formFields: [
			{
				label: "选择奖项",
				name: "awardSelection",
				type: "picker",
				placeholder: "请选择奖项",
				selectedKey: undefined,
				options: convertToPickerOptions(awardItems),
				onSelectionChange,
			},
		],
		buttons: [
			{ text: "显示奖项", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	});
}

// ==================== 主要 Hook ====================

/**
 * 首页配置数据 Hook
 * 
 * @param options - Hook 配置选项
 * @returns 首页配置数据状态和控制方法
 */
export function useHomeConfiguration(options: UseHomeConfigurationOptions): UseHomeConfigurationReturn {
	const {
		configurationData,
		onLog,
		currentDisplayType,
		setCurrentDisplayType,
		setPickerResetControl,
		addConsoleLog,
	} = options;

	// 选中项状态管理
	const [selectedItems, setSelectedItems] = useState<{
		leader: ProcessedConfigurationItem | null;
		player: ProcessedConfigurationItem | null;
		award: ProcessedConfigurationItem | null;
	}>({
		leader: null,
		player: null,
		award: null,
	});

	// 更新选中项
	const updateSelectedItem = useCallback((
		type: 'leader' | 'player' | 'award',
		item: ProcessedConfigurationItem | null
	) => {
		setSelectedItems(prev => ({
			...prev,
			[type]: item,
		}));

		onLog?.('info', `${type}选择已更新`, {
			type,
			itemId: item?.id,
			itemTitle: item?.title,
			timestamp: Date.now(),
			action: 'update_selected_item'
		});
	}, [onLog]);

	// 选择变更处理器
	const handleLeaderSelectionChange = useCallback((key: string) => {
		if (!configurationData) return;
		const item = configurationData.leaderDisplay.find(item => item.id.toString() === key);
		updateSelectedItem('leader', item || null);
	}, [configurationData, updateSelectedItem]);

	const handlePlayerSelectionChange = useCallback((key: string) => {
		if (!configurationData) return;
		const item = configurationData.playerDisplay.find(item => item.id.toString() === key);
		updateSelectedItem('player', item || null);
	}, [configurationData, updateSelectedItem]);

	const handleAwardSelectionChange = useCallback((key: string) => {
		if (!configurationData) return;
		const item = configurationData.awardDisplay.find(item => item.id.toString() === key);
		updateSelectedItem('award', item || null);
	}, [configurationData, updateSelectedItem]);

	// 动态生成按钮组配置
	const dynamicButtonGroups = useMemo((): ButtonGroupConfig[] => {
		if (!configurationData) {
			return [];
		}

		const buttonGroups: ButtonGroupConfig[] = [];

		// 生成选手显示按钮组
		if (configurationData.playerDisplay.length > 0) {
			buttonGroups.push(generatePlayerButtonGroup(
				configurationData.playerDisplay,
				handlePlayerSelectionChange
			));
		}

		// 生成领导显示按钮组
		if (configurationData.leaderDisplay.length > 0) {
			buttonGroups.push(generateLeaderButtonGroup(
				configurationData.leaderDisplay,
				handleLeaderSelectionChange
			));
		}

		// 生成奖项显示按钮组
		if (configurationData.awardDisplay.length > 0) {
			buttonGroups.push(generateAwardButtonGroup(
				configurationData.awardDisplay,
				handleAwardSelectionChange
			));
		}

		return buttonGroups;
	}, [
		configurationData,
		handleLeaderSelectionChange,
		handlePlayerSelectionChange,
		handleAwardSelectionChange,
	]);

	// 计算配置数据统计
	const configurationStats = useMemo(() => {
		if (!configurationData) {
			return {
				leaderCount: 0,
				playerCount: 0,
				awardCount: 0,
				totalCount: 0,
			};
		}

		const leaderCount = configurationData.leaderDisplay.length;
		const playerCount = configurationData.playerDisplay.length;
		const awardCount = configurationData.awardDisplay.length;

		return {
			leaderCount,
			playerCount,
			awardCount,
			totalCount: leaderCount + playerCount + awardCount,
		};
	}, [configurationData]);

	// 是否有配置数据
	const hasConfigurationData = useMemo(() => {
		return configurationData !== null && configurationStats.totalCount > 0;
	}, [configurationData, configurationStats.totalCount]);

	// 配置数据变化时重置选中项
	useEffect(() => {
		if (!configurationData) {
			setSelectedItems({
				leader: null,
				player: null,
				award: null,
			});
		}
	}, [configurationData]);

	// ==================== 事件处理器 ====================

	// 处理配置选择变化
	const handleConfigurationSelectionChange = useCallback((
		type: 'leader' | 'player' | 'award',
		key: string
	) => {
		// 实现互斥选择：记录当前选择的类型，其他组件将根据此信息决定是否重置
		const timestamp = Date.now();

		// 更新重置控制状态，记录当前选择的类型
		setPickerResetControl?.({
			timestamp,
			selectedType: type
		});

		// 更新当前显示类型
		setCurrentDisplayType?.(type);

		// 更新选中的配置项
		if (configurationData) {
			let selectedItem = null;
			switch (type) {
				case 'leader':
					selectedItem = configurationData.leaderDisplay.find(item => item.id.toString() === key);
					break;
				case 'player':
					selectedItem = configurationData.playerDisplay.find(item => item.id.toString() === key);
					break;
				case 'award':
					selectedItem = configurationData.awardDisplay.find(item => item.id.toString() === key);
					break;
			}

			if (selectedItem) {
				updateSelectedItem(type, selectedItem);
				// 使用 setTimeout 避免在渲染过程中更新状态
				setTimeout(() => {
					addConsoleLog?.('info', `${type}配置选择已更新，其他选择器已重置`, {
						type,
						itemId: selectedItem.id,
						itemTitle: selectedItem.title,
						timestamp: Date.now(),
						action: 'configuration_selection_change_with_reset'
					});
				}, 0);
			}
		}

		// 记录操作日志
		onLog?.('info', `配置选择已变化: ${type}`, {
			type,
			key,
			timestamp,
			action: 'configuration_selection_change'
		});
	}, [configurationData, updateSelectedItem, setPickerResetControl, setCurrentDisplayType, addConsoleLog, onLog]);

	// 处理重置到主屏幕
	const handleResetToMainScreen = useCallback(() => {
		// 重置当前显示类型
		setCurrentDisplayType?.(null);

		// 记录操作日志
		onLog?.('info', '重置到主屏幕', {
			previousDisplayType: currentDisplayType,
			timestamp: Date.now(),
			action: 'reset_to_main_screen'
		});
	}, [currentDisplayType, setCurrentDisplayType, onLog]);

	// 处理"显示XX"按钮点击
	const handleShowContent = useCallback((type: 'leader' | 'player' | 'award') => {
		// 检查是否有对应的选中项
		const selectedItem = selectedItems[type];
		if (selectedItem) {
			// 设置当前显示类型，触发显示选中的内容
			setCurrentDisplayType?.(type);

			// 记录操作日志
			onLog?.('info', `显示${type === 'leader' ? '领导' : type === 'player' ? '选手' : '奖项'}内容`, {
				displayType: type,
				itemId: selectedItem.id,
				itemTitle: selectedItem.title,
				timestamp: Date.now(),
				action: 'show_content'
			});
		} else {
			// 没有选中项，记录警告日志
			onLog?.('warning', `无法显示${type === 'leader' ? '领导' : type === 'player' ? '选手' : '奖项'}内容：未选择任何项`, {
				displayType: type,
				timestamp: Date.now(),
				action: 'show_content_no_selection'
			});
		}
	}, [selectedItems, setCurrentDisplayType, onLog]);

	// 处理"显示XX"按钮点击（带表单状态验证）
	const handleShowContentWithValidation = useCallback((
		type: 'leader' | 'player' | 'award',
		formState: Record<string, string | number>
	) => {
		// 根据类型确定对应的表单字段名
		const fieldName = type === 'leader' ? 'leaderSelection' :
			type === 'player' ? 'playerSelection' : 'awardSelection';

		// 检查表单状态中对应字段是否有有效选择
		const currentSelection = formState[fieldName] as string;

		if (!currentSelection || currentSelection === '') {
			// 表单字段没有选择，记录警告日志
			onLog?.('warning', `请先在下拉框中选择要显示的${type === 'leader' ? '领导' : type === 'player' ? '选手' : '奖项'}`, {
				displayType: type,
				fieldName,
				formState,
				timestamp: Date.now(),
				action: 'show_content_validation_failed'
			});
			// 注意：这里不显示 AlertDialog，因为验证逻辑将在 SidebarButtonGroup 中处理
			return;
		}

		// 表单字段有选择，调用原有的显示逻辑
		handleShowContent(type);
	}, [handleShowContent, onLog]);

	// 组合事件处理器
	const handlers = useMemo((): HomeConfigurationHandlers => ({
		handleConfigurationSelectionChange,
		handleShowContent,
		handleShowContentWithValidation,
		handleResetToMainScreen,
	}), [handleConfigurationSelectionChange, handleShowContent, handleShowContentWithValidation, handleResetToMainScreen]);

	return {
		dynamicButtonGroups,
		selectedItems,
		updateSelectedItem,
		hasConfigurationData,
		configurationStats,
		handlers,
	};
}

export default useHomeConfiguration;
