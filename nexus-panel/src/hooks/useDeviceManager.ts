/**
 * 设备管理 Hook
 * 
 * 封装设备状态管理、选中状态和模式切换逻辑
 */

import { useState, useCallback, useMemo, useEffect } from 'react';

// ==================== 类型定义 ====================

/**
 * 设备连接状态
 */
export const DeviceConnectionStatus = {
	ONLINE: 'online',
	OFFLINE: 'offline'
} as const;

export type DeviceConnectionStatus = typeof DeviceConnectionStatus[keyof typeof DeviceConnectionStatus];

/**
 * 设备提交状态
 */
export const DeviceSubmissionStatus = {
	SUBMITTED: 'submitted',
	NOT_SUBMITTED: 'not_submitted'
} as const;

export type DeviceSubmissionStatus = typeof DeviceSubmissionStatus[keyof typeof DeviceSubmissionStatus];

/**
 * 设备故障状态
 */
export const DeviceFaultStatus = {
	NORMAL: 'normal',
	FAULT: 'fault'
} as const;

export type DeviceFaultStatus = typeof DeviceFaultStatus[keyof typeof DeviceFaultStatus];

/**
 * 设备状态接口
 */
export interface DeviceState {
	/** 设备ID */
	id: number;
	/** 连接状态 */
	connection: DeviceConnectionStatus;
	/** 提交状态 */
	submission: DeviceSubmissionStatus;
	/** 故障状态 */
	fault: DeviceFaultStatus;
	/** 是否在定向指令模式下被选中 */
	selected: boolean;
}

/**
 * 设备管理模式
 */
export type DeviceMode = 'global' | 'targeted';

/**
 * 设备管理器配置选项
 */
export interface UseDeviceManagerOptions {
	/** 设备数量 */
	deviceCount: number;
	/** 当前模式 */
	mode: DeviceMode;
	/** 日志记录回调 */
	onLog?: (level: 'info' | 'warning' | 'error', message: string, details?: unknown) => void;
}

/**
 * 设备管理器返回值
 */
export interface UseDeviceManagerReturn {
	/** 设备状态列表 */
	devices: DeviceState[];
	/** 选中的设备ID列表 */
	selectedDeviceIds: number[];
	/** 更新设备连接状态 */
	updateDeviceConnection: (deviceId: number, status: DeviceConnectionStatus) => void;
	/** 更新设备提交状态 */
	updateDeviceSubmission: (deviceId: number, status: DeviceSubmissionStatus) => void;
	/** 更新设备故障状态 */
	updateDeviceFault: (deviceId: number, status: DeviceFaultStatus) => void;
	/** 切换设备选中状态（定向指令模式） */
	toggleDeviceSelection: (deviceId: number) => void;
	/** 清除所有选中状态 */
	clearAllSelections: () => void;
	/** 处理模式切换 */
	handleModeChange: (newMode: DeviceMode) => void;
	/** 获取设备的显示颜色 */
	getDeviceColor: (device: DeviceState) => string;
	/** 获取设备的描边颜色 */
	getDeviceBorderColor: (device: DeviceState) => string | null;
}

// ==================== Hook 实现 ====================

/**
 * 设备管理 Hook
 */
export function useDeviceManager(options: UseDeviceManagerOptions): UseDeviceManagerReturn {
	const { deviceCount, mode, onLog } = options;

	// 初始化设备状态
	const [devices, setDevices] = useState<DeviceState[]>(() =>
		Array.from({ length: deviceCount }, (_, index) => ({
			id: index + 1,
			connection: DeviceConnectionStatus.ONLINE,
			submission: DeviceSubmissionStatus.NOT_SUBMITTED,
			fault: DeviceFaultStatus.NORMAL,
			selected: false,
		}))
	);

	// 当设备数量变化时重新初始化设备列表
	useEffect(() => {
		setDevices(prevDevices => {
			const newDevices = Array.from({ length: deviceCount }, (_, index) => {
				const deviceId = index + 1;
				const existingDevice = prevDevices.find(d => d.id === deviceId);

				return existingDevice || {
					id: deviceId,
					connection: DeviceConnectionStatus.ONLINE,
					submission: DeviceSubmissionStatus.NOT_SUBMITTED,
					fault: DeviceFaultStatus.NORMAL,
					selected: false,
				};
			});

			onLog?.('info', `设备数量更新为 ${deviceCount} 个`, {
				previousCount: prevDevices.length,
				newCount: deviceCount,
				timestamp: Date.now(),
				action: 'device_count_update'
			});

			return newDevices;
		});
	}, [deviceCount, onLog]);

	// 计算选中的设备ID列表
	const selectedDeviceIds = useMemo(() =>
		devices.filter(device => device.selected).map(device => device.id),
		[devices]
	);

	// 更新设备连接状态
	const updateDeviceConnection = useCallback((deviceId: number, status: DeviceConnectionStatus) => {
		setDevices(prevDevices =>
			prevDevices.map(device =>
				device.id === deviceId
					? { ...device, connection: status }
					: device
			)
		);

		onLog?.('info', `设备 ${deviceId} 连接状态更新为 ${status}`, {
			deviceId,
			status,
			timestamp: Date.now(),
			action: 'device_connection_update'
		});
	}, [onLog]);

	// 更新设备提交状态
	const updateDeviceSubmission = useCallback((deviceId: number, status: DeviceSubmissionStatus) => {
		setDevices(prevDevices =>
			prevDevices.map(device =>
				device.id === deviceId
					? { ...device, submission: status }
					: device
			)
		);

		onLog?.('info', `设备 ${deviceId} 提交状态更新为 ${status}`, {
			deviceId,
			status,
			timestamp: Date.now(),
			action: 'device_submission_update'
		});
	}, [onLog]);

	// 更新设备故障状态
	const updateDeviceFault = useCallback((deviceId: number, status: DeviceFaultStatus) => {
		setDevices(prevDevices =>
			prevDevices.map(device =>
				device.id === deviceId
					? { ...device, fault: status }
					: device
			)
		);

		onLog?.('info', `设备 ${deviceId} 故障状态更新为 ${status}`, {
			deviceId,
			status,
			timestamp: Date.now(),
			action: 'device_fault_update'
		});
	}, [onLog]);

	// 切换设备选中状态
	const toggleDeviceSelection = useCallback((deviceId: number) => {
		if (mode !== 'targeted') {
			onLog?.('warning', '只有在定向指令模式下才能选中设备', {
				deviceId,
				currentMode: mode,
				timestamp: Date.now(),
				action: 'device_selection_mode_error'
			});
			return;
		}

		setDevices(prevDevices =>
			prevDevices.map(device =>
				device.id === deviceId
					? { ...device, selected: !device.selected }
					: device
			)
		);

		const device = devices.find(d => d.id === deviceId);
		const newSelectedState = !device?.selected;

		onLog?.('info', `设备 ${deviceId} ${newSelectedState ? '已选中' : '已取消选中'}`, {
			deviceId,
			selected: newSelectedState,
			timestamp: Date.now(),
			action: 'device_selection_toggle'
		});
	}, [mode, devices, onLog]);

	// 清除所有选中状态
	const clearAllSelections = useCallback(() => {
		setDevices(prevDevices =>
			prevDevices.map(device => ({ ...device, selected: false }))
		);

		onLog?.('info', '已清除所有设备选中状态', {
			timestamp: Date.now(),
			action: 'device_selection_clear_all'
		});
	}, [onLog]);

	// 处理模式切换
	const handleModeChange = useCallback((newMode: DeviceMode) => {
		if (newMode === 'global') {
			// 切换到全局指令模式时清除所有选中状态
			clearAllSelections();
		}

		onLog?.('info', `设备模式切换为 ${newMode === 'global' ? '全局指令' : '定向指令'}`, {
			previousMode: mode,
			newMode,
			timestamp: Date.now(),
			action: 'device_mode_change'
		});
	}, [mode, clearAllSelections, onLog]);

	// 获取设备显示颜色
	const getDeviceColor = useCallback((device: DeviceState): string => {
		// 定向指令模式下，所有设备显示为灰色，选中的设备显示为蓝色
		if (mode === 'targeted') {
			return device.selected ? '#007BFF' : '#6C757D';
		}

		// 全局指令模式下，根据连接状态显示颜色
		return device.connection === DeviceConnectionStatus.ONLINE ? '#007BFF' : '#6C757D';
	}, [mode]);

	// 获取设备描边颜色
	const getDeviceBorderColor = useCallback((device: DeviceState): string | null => {
		// 故障状态优先显示红色描边
		if (device.fault === DeviceFaultStatus.FAULT) {
			return '#DC3545';
		}

		// 已提交状态显示绿色描边
		if (device.submission === DeviceSubmissionStatus.SUBMITTED) {
			return '#28A745';
		}

		return null;
	}, []);

	return {
		devices,
		selectedDeviceIds,
		updateDeviceConnection,
		updateDeviceSubmission,
		updateDeviceFault,
		toggleDeviceSelection,
		clearAllSelections,
		handleModeChange,
		getDeviceColor,
		getDeviceBorderColor,
	};
}
