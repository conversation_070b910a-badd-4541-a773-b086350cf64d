/**
 * useRaceApi Hook 工厂函数模块
 * 
 * 包含各种工厂函数，用于创建重置函数、数据获取函数等
 */

import { ApiErrorHandler } from '../../services/api/errors';
import type { ApiError, LogFunction } from './types';

/**
 * 创建工厂函数的参数类型
 */
interface FactoryCallbacksRef {
    current: {
        onLog?: LogFunction;
    };
}

/**
 * 创建工厂函数集合
 */
export function createFactories(callbacksRef: FactoryCallbacksRef) {
    // 通用重置函数工厂
    const createResetFunction = <T>(
        dataName: string,
        setData: (data: T | null) => void,
        setLoading: (loading: boolean) => void,
        setError: (error: ApiError | null) => void
    ) => {
        return () => {
            setData(null);
            setLoading(false);
            setError(null);

            callbacksRef.current.onLog?.('info', `${dataName}数据已重置`, {
                timestamp: Date.now(),
                action: `reset_${dataName.toLowerCase().replace(/\s+/g, '_')}_data`
            });
        };
    };

    // 扩展重置函数工厂 - 支持额外的重置操作
    const createExtendedResetFunction = <T>(
        dataName: string,
        setData: (data: T | null) => void,
        setLoading: (loading: boolean) => void,
        setError: (error: ApiError | null) => void,
        additionalResetActions?: () => void
    ) => {
        return () => {
            setData(null);
            setLoading(false);
            setError(null);

            // 执行额外的重置操作
            additionalResetActions?.();

            callbacksRef.current.onLog?.('info', `${dataName}数据已重置`, {
                timestamp: Date.now(),
                action: `reset_${dataName.toLowerCase().replace(/\s+/g, '_')}_data`,
                hasAdditionalActions: !!additionalResetActions
            });
        };
    };

    // 通用错误处理函数
    const handleApiError = (
        error: unknown,
        operation: string,
        baseId?: string,
        additionalInfo?: Record<string, unknown>
    ) => {
        const apiError = ApiErrorHandler.createApiError(error);

        const errorLog = {
            error: apiError.message,
            operation,
            timestamp: Date.now(),
            action: `${operation.toLowerCase().replace(/\s+/g, '_')}_error`,
            ...(baseId && { baseId }),
            ...additionalInfo
        };

        callbacksRef.current.onLog?.('error', `${operation}失败`, errorLog);

        return apiError;
    };

    // 通用数据获取函数工厂
    const createDataFetcher = <T>(
        dataName: string,
        setData: (data: T) => void,
        setLoading: (loading: boolean) => void,
        setError: (error: ApiError | null) => void,
        fetchFunction: (baseId: string) => Promise<T>
    ) => {
        return async (baseId: string): Promise<void> => {
            try {
                setLoading(true);
                setError(null);

                callbacksRef.current.onLog?.('info', `正在获取${dataName}数据...`, {
                    baseId,
                    timestamp: Date.now(),
                    action: `fetch_${dataName.toLowerCase().replace(/\s+/g, '_')}_start`
                });

                const result = await fetchFunction(baseId);
                setData(result);

                callbacksRef.current.onLog?.('success', `${dataName}数据获取成功`, {
                    baseId,
                    timestamp: Date.now(),
                    action: `fetch_${dataName.toLowerCase().replace(/\s+/g, '_')}_success`
                });
            } catch (error) {
                const apiError = handleApiError(error, `获取${dataName}数据`, baseId);
                setError(apiError);
                throw apiError;
            } finally {
                setLoading(false);
            }
        };
    };

    // 基于表结构的数据获取函数工厂
    const createTableBasedDataFetcher = <T>(
        dataName: string,
        tableName: string,
        setData: (data: T) => void,
        setLoading: (loading: boolean) => void,
        setError: (error: ApiError | null) => void,
        fetchFunction: (tableId: string) => Promise<T>,
        getTableStructureWithCache: (baseId: string) => Promise<{ list: Array<{ id: string; title: string }> }>
    ) => {
        return async (baseId: string): Promise<void> => {
            try {
                setLoading(true);
                setError(null);

                // 调试：记录函数开始执行
                console.log(`[createTableBasedDataFetcher] 🎯 开始执行 ${dataName} 数据获取`, {
                    dataName,
                    tableName,
                    baseId,
                    fetchFunction: fetchFunction?.name || 'anonymous',
                    timestamp: Date.now()
                });

                callbacksRef.current.onLog?.('info', `正在获取${dataName}数据...`, {
                    baseId,
                    tableName,
                    timestamp: Date.now(),
                    action: `fetch_${dataName.toLowerCase().replace(/\s+/g, '_')}_start`
                });

                // 使用缓存获取表结构
                console.log(`[createTableBasedDataFetcher] 🔍 获取表结构`, {
                    dataName,
                    baseId,
                    timestamp: Date.now()
                });
                const tableStructureData = await getTableStructureWithCache(baseId);
                const tables = tableStructureData.list;

                // 查找指定的表
                console.log(`[createTableBasedDataFetcher] 🔍 查找目标表`, {
                    dataName,
                    tableName,
                    availableTables: tables.map(t => t.title),
                    allTablesDetail: tables.map(t => ({ id: t.id, title: t.title })),
                    timestamp: Date.now()
                });
                const targetTable = tables.find((table) => table.title === tableName);
                if (!targetTable) {
                    console.error(`[createTableBasedDataFetcher] ❌ 未找到目标表`, {
                        dataName,
                        tableName,
                        availableTables: tables.map(t => t.title),
                        allTablesDetail: tables.map(t => ({ id: t.id, title: t.title })),
                        searchedTableName: tableName,
                        exactMatches: tables.filter(t => t.title === tableName),
                        partialMatches: tables.filter(t => t.title.includes('配置') || t.title.includes('信息')),
                        timestamp: Date.now()
                    });
                    throw new Error(`未找到${tableName}`);
                }

                console.log(`[createTableBasedDataFetcher] ✅ 找到目标表，准备调用fetchFunction`, {
                    dataName,
                    tableName,
                    targetTableId: targetTable.id,
                    fetchFunction: fetchFunction?.name || 'anonymous',
                    timestamp: Date.now()
                });

                // 获取数据
                const result = await fetchFunction(targetTable.id);
                setData(result);

                callbacksRef.current.onLog?.('success', `${dataName}数据获取成功`, {
                    baseId,
                    tableName,
                    timestamp: Date.now(),
                    action: `fetch_${dataName.toLowerCase().replace(/\s+/g, '_')}_success`
                });
            } catch (error) {
                const apiError = handleApiError(error, `获取${dataName}数据`, baseId, { tableName });
                setError(apiError);
                throw apiError;
            } finally {
                setLoading(false);
            }
        };
    };

    return {
        createResetFunction,
        createExtendedResetFunction,
        handleApiError,
        createDataFetcher,
        createTableBasedDataFetcher
    };
}

export type FactoryFunctions = ReturnType<typeof createFactories>;