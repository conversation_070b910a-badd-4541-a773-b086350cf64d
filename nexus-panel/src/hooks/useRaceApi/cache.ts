/**
 * useRaceApi Hook 缓存管理模块
 * 
 * 专门处理表结构缓存相关功能
 */

import { useCallback, useState } from 'react';
import type { TableStructureResponse, CacheStats, CachePerformanceStats, LogFunction } from './types';

/**
 * 创建缓存管理的参数类型
 */
interface CacheCallbacksRef {
    current: {
        onLog?: LogFunction;
    };
}

/**
 * 创建缓存管理功能 - 自定义 Hook
 */
export function useCacheManager(callbacksRef: CacheCallbacksRef) {
    // 表结构缓存状态管理
    const [tableStructureCache, setTableStructureCache] = useState<Map<string, TableStructureResponse>>(new Map());
    const [cacheStats, setCacheStats] = useState<CacheStats>({
        hits: 0,
        misses: 0,
        totalRequests: 0,
        lastReportTime: Date.now()
    });

    // 当前项目的表结构数据（用于组件间共享）
    const [currentTableStructure, setCurrentTableStructure] = useState<TableStructureResponse | null>(null);

    // 缓存数据验证函数
    const validateCacheData = useCallback((baseId: string, data: TableStructureResponse): boolean => {
        try {
            // 基本结构验证
            if (!data || typeof data !== 'object') {
                callbacksRef.current.onLog?.('warning', '缓存数据结构无效', {
                    baseId,
                    dataType: typeof data,
                    timestamp: Date.now(),
                    action: 'cache_validation_failed'
                });
                return false;
            }

            // 验证必需字段
            if (!data.list || !Array.isArray(data.list)) {
                callbacksRef.current.onLog?.('warning', '缓存数据缺少必需的list字段', {
                    baseId,
                    hasListField: !!data.list,
                    listType: typeof data.list,
                    timestamp: Date.now(),
                    action: 'cache_validation_failed'
                });
                return false;
            }

            // 验证表数据的基本结构
            const hasRequiredTables = data.list.some(table =>
                table.title === "环节表" || table.title === "配置信息表"
            );

            if (!hasRequiredTables) {
                callbacksRef.current.onLog?.('warning', '缓存数据缺少必需的表结构', {
                    baseId,
                    availableTables: data.list.map(t => t.title),
                    timestamp: Date.now(),
                    action: 'cache_validation_failed'
                });
                return false;
            }

            return true;
        } catch (error) {
            callbacksRef.current.onLog?.('error', '缓存数据验证过程中发生错误', {
                baseId,
                error: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                action: 'cache_validation_error'
            });
            return false;
        }
    }, [callbacksRef]);

    // 表结构缓存辅助函数 - 简化版本，直接使用全局缓存
    const getTableStructureWithCache = useCallback(async (baseId: string): Promise<TableStructureResponse> => {
        // 更新缓存统计
        setCacheStats(prev => ({
            ...prev,
            totalRequests: prev.totalRequests + 1
        }));

        callbacksRef.current.onLog?.('info', '🔄 使用全局表结构缓存获取数据', {
            baseId,
            timestamp: Date.now(),
            action: 'use_global_table_structure_cache'
        });

        try {
            // 直接使用全局缓存，避免双重缓存复杂性
            const { GlobalTableStructureCache } = await import('../../services/api/tableStructureCache');
            const result = await GlobalTableStructureCache.getWithCache(
                baseId,
                async (id) => {
                    const { getTableStructure } = await import('../../services/api');
                    const response = await getTableStructure(id);
                    return response.data;
                }
            );

            // 更新统计（命中）
            setCacheStats(prev => ({
                ...prev,
                hits: prev.hits + 1
            }));

            // 更新当前项目的表结构数据（用于组件间共享）
            setCurrentTableStructure(result);

            callbacksRef.current.onLog?.('success', '✅ 全局缓存获取表结构数据成功', {
                baseId,
                tablesCount: result.list.length,
                availableTables: result.list.map(t => t.title),
                optimization: 'global_cache_only',
                timestamp: Date.now(),
                action: 'global_table_structure_cache_success'
            });

            return result;
        } catch (error) {
            // 更新统计（未命中/错误）
            setCacheStats(prev => ({
                ...prev,
                misses: prev.misses + 1
            }));

            // 错误处理
            const errorMessage = error instanceof Error ? error.message : String(error);
            callbacksRef.current.onLog?.('error', '全局缓存获取表结构数据失败', {
                baseId,
                error: errorMessage,
                timestamp: Date.now(),
                action: 'global_table_structure_cache_error'
            });

            throw error;
        }
    }, [callbacksRef]);

    // 缓存性能监控函数
    const getCachePerformanceStats = useCallback((): CachePerformanceStats => {
        const hitRate = cacheStats.totalRequests > 0 ? (cacheStats.hits / cacheStats.totalRequests) * 100 : 0;
        const missRate = cacheStats.totalRequests > 0 ? (cacheStats.misses / cacheStats.totalRequests) * 100 : 0;

        return {
            cacheSize: tableStructureCache.size,
            totalRequests: cacheStats.totalRequests,
            hits: cacheStats.hits,
            misses: cacheStats.misses,
            hitRate: Number(hitRate.toFixed(1)),
            missRate: Number(missRate.toFixed(1)),
            efficiency: (hitRate >= 50 ? 'good' : hitRate >= 25 ? 'fair' : 'poor') as 'good' | 'fair' | 'poor',
            memoryUsage: {
                estimatedKB: tableStructureCache.size * 2, // 粗略估算每个缓存项约2KB
                maxRecommendedSize: 10 // 建议最大缓存10个项目
            }
        };
    }, [tableStructureCache.size, cacheStats]);

    // 缓存清理函数
    const clearTableStructureCache = useCallback((baseId?: string) => {
        const performanceStats = getCachePerformanceStats();

        if (baseId) {
            // 清理特定项目的缓存
            setTableStructureCache(prev => {
                const newCache = new Map(prev);
                newCache.delete(baseId);
                return newCache;
            });

            callbacksRef.current.onLog?.('info', '🧹 已清理特定项目的表结构缓存', {
                baseId,
                remainingCacheSize: tableStructureCache.size - (tableStructureCache.has(baseId) ? 1 : 0),
                performanceBeforeClear: performanceStats,
                timestamp: Date.now(),
                action: 'table_structure_cache_clear_specific'
            });
        } else {
            // 清理所有缓存
            setTableStructureCache(new Map());
            setCacheStats({
                hits: 0,
                misses: 0,
                totalRequests: 0,
                lastReportTime: Date.now()
            });

            callbacksRef.current.onLog?.('info', '🧹 已清理所有表结构缓存', {
                previousCacheSize: tableStructureCache.size,
                finalPerformanceStats: performanceStats,
                timestamp: Date.now(),
                action: 'table_structure_cache_clear_all'
            });
        }
    }, [tableStructureCache, getCachePerformanceStats, callbacksRef]);

    return {
        // 状态
        tableStructureCache,
        cacheStats,
        currentTableStructure,
        setCurrentTableStructure,

        // 函数
        validateCacheData,
        getTableStructureWithCache,
        getCachePerformanceStats,
        clearTableStructureCache
    };
}

export type CacheManager = ReturnType<typeof useCacheManager>;