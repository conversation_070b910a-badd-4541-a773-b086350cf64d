/**
 * useRaceApi Hook 模块入口文件
 */

// 导出主要的 Hook
export { useRaceApi, useRaceApiImmediate, useRaceApiManual, useRaceApiWithHealthCheck } from './useRaceApi';

// 导出类型定义
export type {
    UseRaceApiOptions,
    UseRaceApiReturn,
    RaceApiHandlers,
    LogFunction,
    ProcessedRaceItem,
    ApiError,
    NavigationNode,
    GroupedConfigurationData,
    ProcessedRulesIntroductionItem,
    ProcessedQuestionItem,
    RankingData,
    RankingProgress,
    TableStructureResponse,
    CacheStats,
    CachePerformanceStats,
    PlayerInfoApiItem
} from './types';

// 导出工厂函数（如果需要在其他地方使用）
export { createFactories } from './factories';
export type { FactoryFunctions } from './factories';

// 导出辅助函数（如果需要在其他地方使用）
export { createHelpers } from './helpers';
export type { HelperFunctions } from './helpers';

// 导出缓存管理（如果需要在其他地方使用）
export { useCacheManager } from './cache';
export type { CacheManager } from './cache';