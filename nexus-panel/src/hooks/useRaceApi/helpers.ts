/**
 * useRaceApi Hook 辅助函数模块
 * 
 * 包含各种辅助函数，用于数据处理、验证等
 */

import type { NavigationNode, ProcessedRaceItem, LogFunction } from './types';

/**
 * 创建辅助函数的参数类型
 */
interface HelpersCallbacksRef {
    current: {
        onLog?: LogFunction;
    };
}

/**
 * 验证项目数据的辅助函数
 */
export function validateProjectData(
    baseId: string,
    races: ProcessedRaceItem[],
    callbacksRef: HelpersCallbacksRef
): ProcessedRaceItem | undefined {
    const selectedRace = races.find(race => race.id === baseId);
    if (selectedRace) {
        callbacksRef.current.onLog?.('info', '找到对应的赛事数据', {
            baseId,
            raceName: selectedRace.name,
            raceDbId: selectedRace.dbId,
            raceVisible: selectedRace.visible,
            timestamp: Date.now(),
            action: 'race_validation_success'
        });
    } else {
        callbacksRef.current.onLog?.('warning', '未找到对应的赛事数据', {
            baseId,
            availableRaces: races.map(race => ({ id: race.id, name: race.name })),
            timestamp: Date.now(),
            action: 'race_validation_failed'
        });
    }
    return selectedRace;
}

/**
 * 环节数据项的类型定义
 */
interface SectionItem {
    Id: number;
    "环节名称": string;
    "环节图标": string;
    "显示顺序": number;
    "环节类型": string;
    "内容类型"?: string;
    "初始阶段"?: string;
}

/**
 * 处理导航数据的辅助函数
 */
export function processNavigationData(sectionItems: SectionItem[]): NavigationNode[] {
    // 手动转换环节数据为导航节点
    const navigationNodes: NavigationNode[] = sectionItems.map((item) => ({
        id: `section-${item.Id}`,
        name: item["环节名称"],
        icon: item["环节图标"],
        order: item["显示顺序"],
        type: item["环节类型"],
        contentType: item["内容类型"],
        initialStage: item["初始阶段"],
        children: []
    }));

    // 构建树形结构
    const rootNodes: NavigationNode[] = [];
    const childNodes: NavigationNode[] = [];

    navigationNodes.forEach(node => {
        if (node.type === "父节点") {
            rootNodes.push(node);
        } else {
            childNodes.push(node);
        }
    });

    // 建立父子关系
    childNodes.forEach(child => {
        const parentNode = rootNodes.find(parent => {
            if (child.type === "规则介绍") {
                return parent.name === "规则介绍";
            }
            if (child.type === "环节切换") {
                return parent.name === "环节切换";
            }
            return false;
        });

        if (parentNode && parentNode.children) {
            child.parentId = parentNode.id;
            parentNode.children.push(child);
        }
    });

    // 排序
    rootNodes.sort((a, b) => a.order - b.order);
    rootNodes.forEach(parent => {
        if (parent.children) {
            parent.children.sort((a: NavigationNode, b: NavigationNode) => a.order - b.order);
        }
    });

    // 添加固定的首页节点
    const homeNode: NavigationNode = {
        id: 'home',
        name: '首页',
        icon: '首页',
        order: 0,
        type: '独立节点'
    };

    return [homeNode, ...rootNodes];
}

/**
 * 查找导航节点的通用函数
 */
export function findNodeInTree(nodes: NavigationNode[], targetId: string): NavigationNode | null {
    for (const node of nodes) {
        if (node.id === targetId) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeInTree(node.children, targetId);
            if (found) {
                return found;
            }
        }
    }
    return null;
}

/**
 * 创建辅助函数集合（用于需要依赖注入的场景）
 */
export function createHelpers(
    callbacksRef: HelpersCallbacksRef,
    races: ProcessedRaceItem[]
) {
    return {
        validateProjectData: (baseId: string) => validateProjectData(baseId, races, callbacksRef),
        processNavigationData,
        findNodeInTree
    };
}

export type HelperFunctions = ReturnType<typeof createHelpers>;