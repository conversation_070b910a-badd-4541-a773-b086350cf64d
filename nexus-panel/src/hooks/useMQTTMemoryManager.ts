/**
 * MQTT 内存管理器 React Hook
 * 
 * 提供 React 组件级别的 MQTT 内存管理集成，包括：
 * - 内存管理器的生命周期管理
 * - 内存统计状态的实时更新
 * - 手动清理操作的封装
 * - 清理历史的状态管理
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { MQTTMemoryManager } from '../services/mqtt';
import type { MQTTMemoryConfig, MQTTMemoryStats, MQTTCleanupResult } from '../services/mqtt';
import { MQTTService } from '../services/mqtt/MQTTService';

/**
 * MQTT 内存管理器 Hook 配置接口
 */
export interface UseMQTTMemoryManagerOptions {
	/** 内存管理器配置 */
	config?: MQTTMemoryConfig;
	/** 是否启用调试日志，默认 false */
	debug?: boolean;
}

/**
 * MQTT 内存管理器 Hook 返回值接口
 */
export interface UseMQTTMemoryManagerReturn {
	/** 内存管理器实例 */
	memoryManager: MQTTMemoryManager | null;
	/** 当前内存统计 */
	memoryStats: MQTTMemoryStats | null;
	/** 清理历史 */
	cleanupHistory: MQTTCleanupResult[];
	/** 是否正在清理 */
	isCleaningUp: boolean;
	/** 最后一次错误 */
	error: string | null;
	/** 手动执行清理 */
	manualCleanup: () => Promise<void>;
	/** 启动内存管理器 */
	start: (mqttService: MQTTService) => void;
	/** 停止内存管理器 */
	stop: () => void;
	/** 设置日志计数回调 */
	setLogCountCallback: (callback: () => number) => void;
	/** 设置日志清理回调 */
	setLogCleanupCallback: (callback: (count: number) => void) => void;
	/** 清除错误状态 */
	clearError: () => void;
	/** 手动更新统计 */
	updateStats: () => void;
}

/**
 * MQTT 内存管理器 Hook
 */
export function useMQTTMemoryManager(
	options: UseMQTTMemoryManagerOptions = {}
): UseMQTTMemoryManagerReturn {
	const {
		config = {},
		debug = false,
	} = options;

	// 状态管理
	const [memoryManager, setMemoryManager] = useState<MQTTMemoryManager | null>(null);
	const [memoryStats, setMemoryStats] = useState<MQTTMemoryStats | null>(null);
	const [cleanupHistory, setCleanupHistory] = useState<MQTTCleanupResult[]>([]);
	const [isCleaningUp, setIsCleaningUp] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 引用管理
	const mqttServiceRef = useRef<MQTTService | null>(null);
	const memoryManagerRef = useRef<MQTTMemoryManager | null>(null);

	// 调试日志
	const log = useCallback((message: string, data?: unknown) => {
		if (debug) {
			console.log(`[useMQTTMemoryManager] ${message}`, data || '');
		}
	}, [debug]);

	// 更新内存统计
	const updateMemoryStats = useCallback(() => {
		const currentManager = memoryManagerRef.current;
		if (currentManager) {
			try {
				const stats = currentManager.getMemoryStats();
				setMemoryStats(stats);

				const history = currentManager.getCleanupHistory();
				setCleanupHistory(history);

				log('内存统计已更新', stats);
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : String(err);
				setError(`更新内存统计失败: ${errorMessage}`);
				console.error('更新内存统计失败:', err);
			}
		}
	}, [log]);



	// 启动内存管理器
	const start = useCallback((mqttService: MQTTService) => {
		try {
			// 停止现有的管理器
			const currentManager = memoryManagerRef.current;
			if (currentManager) {
				currentManager.stop();
			}

			// 创建新的内存管理器
			const newManager = new MQTTMemoryManager({
				...config,
				debug,
			});

			// 启动管理器
			newManager.start(mqttService);

			// 更新引用和状态
			memoryManagerRef.current = newManager;
			setMemoryManager(newManager);
			mqttServiceRef.current = mqttService;
			setError(null);

			// 立即更新一次统计
			setTimeout(updateMemoryStats, 100);

			log('内存管理器已启动');
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : String(err);
			setError(`启动内存管理器失败: ${errorMessage}`);
			console.error('启动内存管理器失败:', err);
		}
	}, [config, debug, updateMemoryStats, log]);

	// 停止内存管理器
	const stop = useCallback(() => {
		try {
			const currentManager = memoryManagerRef.current;
			if (currentManager) {
				currentManager.stop();
				memoryManagerRef.current = null;
				setMemoryManager(null);
			}

			mqttServiceRef.current = null;

			// 清理状态
			setMemoryStats(null);
			setCleanupHistory([]);
			setIsCleaningUp(false);
			setError(null);

			log('内存管理器已停止');
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : String(err);
			setError(`停止内存管理器失败: ${errorMessage}`);
			console.error('停止内存管理器失败:', err);
		}
	}, [log]);

	// 手动清理
	const manualCleanup = useCallback(async () => {
		const currentManager = memoryManagerRef.current;
		if (!currentManager) {
			setError('内存管理器未初始化');
			return;
		}

		setIsCleaningUp(true);
		setError(null);

		try {
			log('开始手动清理');
			const result = await currentManager.manualCleanup();

			// 更新清理历史
			setCleanupHistory(prev => [...prev, result].slice(-10)); // 保留最近10次

			// 立即更新统计
			updateMemoryStats();

			log('手动清理完成', result);
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : String(err);
			setError(`手动清理失败: ${errorMessage}`);
			console.error('手动清理失败:', err);
		} finally {
			setIsCleaningUp(false);
		}
	}, [updateMemoryStats, log]);

	// 设置日志计数回调
	const setLogCountCallback = useCallback((callback: () => number) => {
		const currentManager = memoryManagerRef.current;
		if (currentManager) {
			currentManager.setLogCountCallback(callback);
			log('日志计数回调已设置');
		}
	}, [log]);

	// 设置日志清理回调
	const setLogCleanupCallback = useCallback((callback: (count: number) => void) => {
		const currentManager = memoryManagerRef.current;
		if (currentManager) {
			currentManager.setLogCleanupCallback(callback);
			log('日志清理回调已设置');
		}
	}, [log]);

	// 清除错误状态
	const clearError = useCallback(() => {
		setError(null);
	}, []);

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			// 直接清理，避免依赖循环
			const currentManager = memoryManagerRef.current;
			if (currentManager) {
				currentManager.stop();
				memoryManagerRef.current = null;
			}
		};
	}, []);

	// 自动启动逻辑 - 移除，避免复杂的依赖关系
	// 改为在 App.tsx 中手动控制启动时机

	return {
		memoryManager,
		memoryStats,
		cleanupHistory,
		isCleaningUp,
		error,
		manualCleanup,
		start,
		stop,
		setLogCountCallback,
		setLogCleanupCallback,
		clearError,
		updateStats: updateMemoryStats,
	};
}
