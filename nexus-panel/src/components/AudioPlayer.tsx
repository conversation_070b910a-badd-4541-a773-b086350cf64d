// React 核心依赖导入
import React, { useState, useRef, useEffect, useCallback } from "react";
// Adobe React Spectrum UI 组件库导入
import { Flex, ActionButton, Text, View } from "@adobe/react-spectrum";
// 导入图标
import Play from "@spectrum-icons/workflow/Play";
import Pause from "@spectrum-icons/workflow/Pause";
import RotateCCWBold from "@spectrum-icons/workflow/RotateCCWBold";
import VolumeThree from "@spectrum-icons/workflow/VolumeThree";
import VolumeMute from "@spectrum-icons/workflow/VolumeMute";
// 导入样式
import "./AudioPlayer.css";

/**
 * AudioPlayer 组件的 Props 接口
 */
export interface AudioPlayerProps {
	/** 音频源路径 - 支持本地文件路径（如 "/audio/sample.mp3"）或外部 HTTPS URL（如 "https://example.com/audio.mp3"） */
	audioSrc: string;
	/** 播放事件回调 */
	onPlay?: () => void;
	/** 暂停事件回调 */
	onPause?: () => void;
	/** 播放结束回调 */
	onEnded?: () => void;
	/** 时间更新回调 */
	onTimeUpdate?: (currentTime: number, duration: number) => void;
	/** 是否禁用 */
	isDisabled?: boolean;
	/** 是否加载中 */
	isLoading?: boolean;
	/** 是否启用自动播放 - 在音频源变化时自动开始播放（需要用户已与页面交互） */
	autoPlay?: boolean;
	/** 自动播放失败回调 - 当自动播放被浏览器阻止时触发 */
	onAutoPlayFailed?: (error: Error) => void;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum 样式 */
	UNSAFE_style?: React.CSSProperties;
}

/**
 * AudioPlayer - 音频播放组件
 *
 * 功能特性：
 * - 播放/暂停控制：点击切换播放状态
 * - 重播功能：从头开始播放音频
 * - 智能自动播放：在用户交互后自动播放新音频（可配置）
 * - 时间显示：显示当前时间/总时长（mm:ss格式）
 * - 进度条：可拖拽调节播放进度，实时显示播放进度
 * - 音量控制：点击切换静音/取消静音状态
 * - 使用HTML5 Audio API实现音频播放功能
 * - 支持本地文件和外部 HTTPS URL 音频源
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局和无障碍访问
 * - 自动播放策略：符合浏览器安全策略，需要用户交互后才能自动播放
 *
 * 支持的音频源格式：
 * - 本地文件："/audio/sample.mp3"、"./assets/audio.wav"
 * - 外部 HTTPS URL："https://example.com/audio.mp3"
 * - 支持的音频格式：MP3、WAV、OGG、AAC（取决于浏览器支持）
 *
 * 自动播放说明：
 * - 当autoPlay=true且用户已与页面交互时，音频源变化会触发自动播放
 * - 如果自动播放失败（浏览器阻止），会触发onAutoPlayFailed回调
 * - 自动播放失败时会显示播放提示，引导用户手动播放
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function AudioPlayer({
	audioSrc,
	onPlay,
	onPause,
	onEnded,
	onTimeUpdate,
	isDisabled = false,
	isLoading = false,
	autoPlay = false,
	onAutoPlayFailed,
	className,
	UNSAFE_style,
}: AudioPlayerProps) {
	// 播放状态管理
	const [isPlaying, setIsPlaying] = useState(false);
	const [isMuted, setIsMuted] = useState(false);

	// 时间相关状态
	const [currentTime, setCurrentTime] = useState(0);
	const [duration, setDuration] = useState(0);
	const [isLoadingAudio, setIsLoadingAudio] = useState(false);
	const [hasAudioError, setHasAudioError] = useState(false);
	const [audioErrorMessage, setAudioErrorMessage] = useState("");

	// 自动播放状态跟踪
	const [hasAutoPlayed, setHasAutoPlayed] = useState(false);



	// 拖拽相关状态
	const [isDragging, setIsDragging] = useState(false);
	const [dragCurrentTime, setDragCurrentTime] = useState(0); // 拖拽过程中的实时时间

	// Audio元素引用
	const audioRef = useRef<HTMLAudioElement>(null);
	const progressBarRef = useRef<HTMLDivElement>(null);

	/**
	 * 验证音频源 URL 格式
	 * @param src - 音频源路径或URL
	 * @returns 是否为有效的音频源
	 */
	const isValidAudioSrc = useCallback((src: string): boolean => {
		if (!src || src.trim() === "") return false;

		// 支持本地路径（相对路径或绝对路径）
		if (src.startsWith("/") || src.startsWith("./") || src.startsWith("../")) {
			return true;
		}

		// 支持 HTTPS URL
		if (src.startsWith("https://")) {
			try {
				new URL(src);
				return true;
			} catch {
				return false;
			}
		}

		// 支持 data: URL（base64 编码音频）
		if (src.startsWith("data:audio/")) {
			return true;
		}

		// 其他情况视为本地文件路径
		return true;
	}, []);

	/**
	 * 验证音频时长是否有效
	 * @param duration - 音频时长
	 * @returns 是否为有效的时长
	 */
	const isValidDuration = useCallback((duration: number): boolean => {
		return !isNaN(duration) && isFinite(duration) && duration > 0;
	}, []);

	/**
	 * 格式化时间为 mm:ss 格式
	 * @param time - 时间（秒）
	 * @returns 格式化的时间字符串
	 */
	const formatTime = useCallback((time: number): string => {
		if (isNaN(time)) return "00:00";
		const minutes = Math.floor(time / 60);
		const seconds = Math.floor(time % 60);
		return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
	}, []);

	/**
	 * 播放/暂停切换处理
	 */
	const handlePlayPause = useCallback(() => {
		if (!audioRef.current || isDisabled) return;

		if (isPlaying) {
			audioRef.current.pause();
			onPause?.();
		} else {
			audioRef.current.play();
			onPlay?.();
		}
	}, [isPlaying, isDisabled, onPlay, onPause]);

	/**
	 * 重播处理
	 */
	const handleReplay = useCallback(() => {
		if (!audioRef.current || isDisabled) return;

		audioRef.current.currentTime = 0;
		audioRef.current.play();
		onPlay?.();
	}, [isDisabled, onPlay]);

	/**
	 * 尝试自动播放音频
	 */
	const attemptAutoPlay = useCallback(async () => {
		if (!audioRef.current || !autoPlay || isDisabled || hasAutoPlayed) {
			return;
		}

		try {
			console.log('[音频播放器] 尝试自动播放音频:', audioSrc);

			await audioRef.current.play();
			setHasAutoPlayed(true); // 标记已经自动播放过
			onPlay?.();

			console.log('[音频播放器] 自动播放成功');
		} catch (error) {
			console.warn('[音频播放器] 自动播放失败:', error);

			// 触发自动播放失败回调
			if (onAutoPlayFailed && error instanceof Error) {
				onAutoPlayFailed(error);
			}
		}
	}, [audioRef, autoPlay, isDisabled, hasAutoPlayed, audioSrc, onPlay, onAutoPlayFailed, setHasAutoPlayed]);



	/**
	 * 音量切换处理
	 */
	const handleVolumeToggle = useCallback(() => {
		if (!audioRef.current || isDisabled) return;

		audioRef.current.muted = !isMuted;
		setIsMuted(!isMuted);
	}, [isMuted, isDisabled]);

	/**
	 * 进度条点击处理
	 */
	const handleProgressClick = useCallback(
		(event: React.MouseEvent<HTMLDivElement>) => {
			if (
				!audioRef.current ||
				!progressBarRef.current ||
				isDisabled ||
				!isValidDuration(duration) ||
				isDragging ||
				hasAudioError
			)
				return;

			const rect = progressBarRef.current.getBoundingClientRect();
			const clickX = event.clientX - rect.left;
			const percentage = clickX / rect.width;
			const newTime = percentage * duration;

			audioRef.current.currentTime = Math.max(0, Math.min(newTime, duration));
		},
		[duration, isDisabled, isDragging, isValidDuration, hasAudioError],
	);

	/**
	 * 拖拽开始处理（鼠标）
	 */
	const handleDragStart = useCallback(
		(event: React.MouseEvent<HTMLDivElement>) => {
			if (
				!audioRef.current ||
				!progressBarRef.current ||
				isDisabled ||
				!isValidDuration(duration) ||
				hasAudioError
			)
				return;

			event.preventDefault();
			event.stopPropagation(); // 阻止事件冒泡，避免触发进度条点击
			setIsDragging(true);
			setDragCurrentTime(currentTime); // 初始化拖拽时间
		},
		[isDisabled, duration, currentTime, isValidDuration, hasAudioError],
	);

	/**
	 * 拖拽移动处理（鼠标）
	 */
	const handleDragMove = useCallback(
		(event: MouseEvent) => {
			if (
				!audioRef.current ||
				!progressBarRef.current ||
				!isDragging ||
				!isValidDuration(duration) ||
				hasAudioError
			)
				return;

			const rect = progressBarRef.current.getBoundingClientRect();
			// 使用绝对位置而不是增量计算
			const clickX = event.clientX - rect.left;
			const percentage = Math.max(0, Math.min(clickX / rect.width, 1));
			const newTime = percentage * duration;

			// 更新拖拽时的实时时间状态
			setDragCurrentTime(newTime);
			audioRef.current.currentTime = newTime;
		},
		[isDragging, duration, isValidDuration, hasAudioError],
	);

	/**
	 * 拖拽结束处理（鼠标）
	 */
	const handleDragEnd = useCallback(() => {
		setIsDragging(false);
		setDragCurrentTime(0); // 重置拖拽时间状态
	}, []);

	/**
	 * 触摸拖拽开始处理
	 */
	const handleTouchStart = useCallback(
		(event: React.TouchEvent<HTMLDivElement>) => {
			if (
				!audioRef.current ||
				!progressBarRef.current ||
				isDisabled ||
				!isValidDuration(duration) ||
				hasAudioError
			)
				return;

			event.preventDefault();
			event.stopPropagation(); // 阻止事件冒泡
			setIsDragging(true);
			setDragCurrentTime(currentTime); // 初始化拖拽时间
		},
		[isDisabled, duration, currentTime, isValidDuration, hasAudioError],
	);

	/**
	 * 触摸拖拽移动处理
	 */
	const handleTouchMove = useCallback(
		(event: TouchEvent) => {
			if (
				!audioRef.current ||
				!progressBarRef.current ||
				!isDragging ||
				!isValidDuration(duration) ||
				hasAudioError
			)
				return;

			event.preventDefault();
			const touch = event.touches[0];
			const rect = progressBarRef.current.getBoundingClientRect();
			// 使用绝对位置而不是增量计算
			const clickX = touch.clientX - rect.left;
			const percentage = Math.max(0, Math.min(clickX / rect.width, 1));
			const newTime = percentage * duration;

			// 更新拖拽时的实时时间状态
			setDragCurrentTime(newTime);
			audioRef.current.currentTime = newTime;
		},
		[isDragging, duration, isValidDuration, hasAudioError],
	);

	/**
	 * 触摸拖拽结束处理
	 */
	const handleTouchEnd = useCallback(() => {
		setIsDragging(false);
		setDragCurrentTime(0); // 重置拖拽时间状态
	}, []);

	// 音频事件监听设置
	useEffect(() => {
		const audio = audioRef.current;
		if (!audio) return;

		const handleLoadStart = () => {
			setIsLoadingAudio(true);
			setHasAudioError(false);
			setAudioErrorMessage("");
		};

		const handleLoadedMetadata = () => {
			if (isValidDuration(audio.duration)) {
				setDuration(audio.duration);
				setIsLoadingAudio(false);
				setHasAudioError(false);
			}
		};

		// 添加 canplay 事件作为备用
		const handleCanPlay = () => {
			if (isValidDuration(audio.duration) && duration === 0) {
				setDuration(audio.duration);
				setIsLoadingAudio(false);
				setHasAudioError(false);
			}
		};

		// 添加 durationchange 事件监听
		const handleDurationChange = () => {
			if (isValidDuration(audio.duration)) {
				setDuration(audio.duration);
				setHasAudioError(false);
			}
		};

		const handleTimeUpdate = () => {
			setCurrentTime(audio.currentTime);
			onTimeUpdate?.(audio.currentTime, audio.duration);
		};

		const handlePlay = () => setIsPlaying(true);
		const handlePause = () => setIsPlaying(false);
		const handleEnded = () => {
			setIsPlaying(false);
			onEnded?.();
		};

		const handleError = (event: Event) => {
			setIsLoadingAudio(false);
			setIsPlaying(false);
			setHasAudioError(true);

			// 获取错误详情
			const target = event.target as HTMLAudioElement;
			const error = target.error;
			let errorMessage = "音频加载失败";

			if (error) {
				switch (error.code) {
					case error.MEDIA_ERR_ABORTED:
						errorMessage = "音频加载被中止";
						break;
					case error.MEDIA_ERR_NETWORK:
						errorMessage = "网络错误，无法加载音频";
						break;
					case error.MEDIA_ERR_DECODE:
						errorMessage = "音频解码失败";
						break;
					case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
						errorMessage = "不支持的音频格式";
						break;
					default:
						errorMessage = "未知音频错误";
				}
			}

			setAudioErrorMessage(errorMessage);
		};

		// 添加事件监听器
		audio.addEventListener("loadstart", handleLoadStart);
		audio.addEventListener("loadedmetadata", handleLoadedMetadata);
		audio.addEventListener("canplay", handleCanPlay);
		audio.addEventListener("durationchange", handleDurationChange);
		audio.addEventListener("timeupdate", handleTimeUpdate);
		audio.addEventListener("play", handlePlay);
		audio.addEventListener("pause", handlePause);
		audio.addEventListener("ended", handleEnded);
		audio.addEventListener("error", handleError);

		return () => {
			// 清理事件监听器
			audio.removeEventListener("loadstart", handleLoadStart);
			audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
			audio.removeEventListener("canplay", handleCanPlay);
			audio.removeEventListener("durationchange", handleDurationChange);
			audio.removeEventListener("timeupdate", handleTimeUpdate);
			audio.removeEventListener("play", handlePlay);
			audio.removeEventListener("pause", handlePause);
			audio.removeEventListener("ended", handleEnded);
			audio.removeEventListener("error", handleError);
		};
	}, [audioSrc, onTimeUpdate, onEnded, duration, isValidDuration]);

	// 音频源变化时重置状态
	useEffect(() => {
		setIsPlaying(false);
		setCurrentTime(0);
		setDuration(0);
		setIsLoadingAudio(false);
		setIsDragging(false);
		setDragCurrentTime(0);
		setHasAudioError(false);
		setAudioErrorMessage("");
		setHasAutoPlayed(false); // 重置自动播放状态

		// 开发模式下验证音频源格式（静默验证）
		if (process.env.NODE_ENV === "development") {
			isValidAudioSrc(audioSrc); // 静默验证，不输出日志
		}
	}, [audioSrc, isValidAudioSrc]);

	// 音频加载完成后尝试自动播放
	useEffect(() => {
		if (!hasAudioError && !isLoadingAudio && duration > 0 && autoPlay && !hasAutoPlayed) {
			// 延迟一小段时间确保音频完全准备好
			const timer = setTimeout(() => {
				attemptAutoPlay();
			}, 100);

			return () => clearTimeout(timer);
		}
	}, [hasAudioError, isLoadingAudio, duration, autoPlay, hasAutoPlayed, attemptAutoPlay]);

	// 处理拖拽事件监听器
	useEffect(() => {
		if (isDragging) {
			document.addEventListener("mousemove", handleDragMove);
			document.addEventListener("mouseup", handleDragEnd);
			document.addEventListener("touchmove", handleTouchMove);
			document.addEventListener("touchend", handleTouchEnd);
		}

		return () => {
			document.removeEventListener("mousemove", handleDragMove);
			document.removeEventListener("mouseup", handleDragEnd);
			document.removeEventListener("touchmove", handleTouchMove);
			document.removeEventListener("touchend", handleTouchEnd);
		};
	}, [
		isDragging,
		handleDragMove,
		handleDragEnd,
		handleTouchMove,
		handleTouchEnd,
	]);

	// 计算进度百分比 - 拖拽时使用拖拽时间，否则使用当前播放时间
	const displayTime = isDragging ? dragCurrentTime : currentTime;
	const progressPercentage = isValidDuration(duration)
		? (displayTime / duration) * 100
		: 0;

	// 检查是否可以进行拖拽操作
	const isDragDisabled =
		isDisabled || !isValidDuration(duration) || hasAudioError || isLoadingAudio;

	// 构建CSS类名
	const cssClasses = [
		"audio-player",
		isDisabled ? "disabled" : "",
		isLoading || isLoadingAudio ? "loading" : "",
		hasAudioError ? "error" : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View UNSAFE_className={cssClasses} UNSAFE_style={UNSAFE_style}>
			{/* 隐藏的HTML5 Audio元素 */}
			<audio
				ref={audioRef}
				src={audioSrc}
				preload="metadata"
				style={{ display: "none" }}
			/>



			{/* 音频播放器控件 */}
			<Flex
				alignItems="center"
				gap="size-75"
				UNSAFE_className="audio-player-controls"
			>
				{/* 播放/暂停按钮 */}
				<ActionButton
					isQuiet
					isDisabled={isDisabled || isLoading || isLoadingAudio}
					onPress={handlePlayPause}
					aria-label={isPlaying ? "暂停" : "播放"}
					UNSAFE_className="audio-player-play-pause"
				>
					{isPlaying ? <Pause /> : <Play />}
				</ActionButton>

				{/* 重播按钮 */}
				<ActionButton
					isQuiet
					isDisabled={isDisabled || isLoading || isLoadingAudio}
					onPress={handleReplay}
					aria-label="重播"
					UNSAFE_className="audio-player-replay"
				>
					<RotateCCWBold />
				</ActionButton>

				{/* 时间显示 */}
				<Text UNSAFE_className="audio-player-time">
					{hasAudioError
						? audioErrorMessage
						: isLoadingAudio
							? "加载中..."
							: `${formatTime(currentTime)} / ${formatTime(duration)}`}
				</Text>

				{/* 进度条 */}
				<View flex="1" UNSAFE_className="audio-player-progress-container">
					<div
						ref={progressBarRef}
						className={`audio-player-progress-bar ${isDragging ? "dragging" : ""} ${isDragDisabled ? "disabled" : ""}`}
						onClick={isDragDisabled ? undefined : handleProgressClick}
						role="slider"
						aria-label="音频进度"
						aria-valuemin={0}
						aria-valuemax={100}
						aria-valuenow={Math.round(progressPercentage)}
						tabIndex={isDragDisabled ? -1 : 0}
						style={{
							cursor: isDragDisabled
								? "not-allowed"
								: isDragging
									? "grabbing"
									: "pointer",
						}}
					>
						<div
							className={`audio-player-progress-fill ${isDragging ? "dragging" : ""}`}
							style={{ width: `${progressPercentage}%` }}
						/>
						{/* 拖拽指示器 */}
						{!isDragDisabled && (
							<div
								className={`audio-player-drag-indicator ${isDragging ? "dragging" : ""}`}
								style={{
									left: `${progressPercentage}%`,
									cursor: isDragging ? "grabbing" : "grab",
								}}
								onMouseDown={handleDragStart}
								onTouchStart={handleTouchStart}
							/>
						)}
					</div>
				</View>

				{/* 音量按钮 */}
				<ActionButton
					isQuiet
					isDisabled={isDisabled || isLoading || isLoadingAudio}
					onPress={handleVolumeToggle}
					aria-label={isMuted ? "取消静音" : "静音"}
					UNSAFE_className="audio-player-volume"
				>
					{isMuted ? <VolumeMute /> : <VolumeThree />}
				</ActionButton>
			</Flex>
		</View>
	);
}

export default AudioPlayer;
