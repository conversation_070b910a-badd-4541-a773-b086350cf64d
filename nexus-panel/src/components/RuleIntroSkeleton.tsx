// React 核心依赖导入
// Adobe React Spectrum UI 组件库导入
import { View, ProgressCircle, Flex } from "@adobe/react-spectrum";

/**
 * RuleIntroSkeleton 组件的 Props 接口 - Next.js 风格优化
 */
export interface RuleIntroSkeletonProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
}

/**
 * RuleIntroSkeleton - 规则介绍页面的骨架屏加载状态组件 (Next.js 风格优化)
 *
 * 功能特性：
 * - A1 区域：规则标题占位符
 * - B1 区域：规则内容占位符（多行文本）
 * - C1 & C2 区域：导航按钮占位符（上一页/下一页）
 * - Next.js 风格设计：柔和灰色调色板、统一8px圆角、2s平滑动画
 * - 支持交互效果、状态指示、淡入淡出过渡
 * - 深色主题适配和无障碍访问优化
 * - 性能优化：硬件加速、减少重绘
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function RuleIntroSkeleton({
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "loading",
	fadeEffect = null,
}: RuleIntroSkeletonProps) {
	// 构建CSS类名
	const cssClasses = [
		"rule-intro-skeleton",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "loading" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* A1 区域：规则标题占位符 */}
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="100%"
				height="size-600"
				marginBottom="size-400"
				UNSAFE_className="skeleton-item"
			/>

			{/* B1 区域：规则内容占位符 */}
			<View marginBottom="size-100" backgroundColor="gray-300" borderRadius="small">
				{status === "loading" ? (
					/* 加载状态：显示加载指示器 */
					<Flex direction="column" alignItems="center" justifyContent="center" height="size-3000">
						<ProgressCircle
							aria-label="正在加载规则介绍内容..."
							isIndeterminate
							size="L"
							UNSAFE_style={{ marginBottom: '16px' }}
						/>
						<View UNSAFE_style={{
							color: 'var(--spectrum-global-color-gray-600)',
							fontSize: '14px',
							textAlign: 'center'
						}}>
							正在加载规则介绍内容...
						</View>
					</Flex>
				) : (
					/* 非加载状态：显示静态占位符 */
					<View
						backgroundColor="gray-300"
						borderRadius="small"
						width="100%"
						height="size-3000"
						UNSAFE_className="skeleton-item"
					/>
				)}
			</View>
		</View>
	);
}

export default RuleIntroSkeleton;
