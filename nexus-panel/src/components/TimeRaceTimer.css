/**
 * 争分夺秒计时器样式
 */

.timer-display {
  font-size: 2rem !important;
  font-weight: bold !important;
  font-family:
    'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
  letter-spacing: 0.1em !important;
  min-width: 120px !important;
  text-align: center !important;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  border: 2px solid #0056b3 !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
  width: 100% !important;
  margin-bottom: 12px !important;
}

.timer-display:disabled {
  opacity: 1 !important;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
}

/* 时间不足时的警告样式 */
.timer-display.warning {
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%) !important;
  border-color: #e55a2b !important;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4) !important;
  animation: pulse-warning 1s infinite;
}

.timer-display.danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  border-color: #c82333 !important;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4) !important;
  animation: pulse-danger 0.5s infinite;
}

/* 脉冲动画 */
@keyframes pulse-warning {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes pulse-danger {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 计时器按钮组特殊样式 */
.time-race-timer-group {
  border: 2px solid var(--spectrum-global-color-blue-400);
  border-radius: 8px;
  padding: 16px;
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-blue-50) 0%,
    var(--spectrum-global-color-blue-100) 100%
  );
}

.time-race-timer-group .spectrum-Heading {
  color: var(--spectrum-global-color-blue-700);
  font-weight: 600;
}

/* 确保计时器按钮组中的控制按钮正常显示 */
.time-race-timer-group .spectrum-ActionButton {
  margin: 4px;
}

/* 计时器显示区域的容器样式 */
.timer-display-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* 控制按钮行样式 */
.timer-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
  width: 100%;
}
