// React 核心依赖导入
import { useState, useEffect, useCallback, useRef } from "react";
// Adobe React Spectrum UI 组件库导入
import { View, Flex, Text } from "@adobe/react-spectrum";
// 导入样式
import "./UltimatePKContent.css";
// 导入共享类型定义
import { PKStage, PKSide } from "../types/ultimatePK.ts";

/**
 * 计时器状态接口
 */
interface TimerState {
	time: number;
	isRunning: boolean;
	maxTime: number;
}

/**
 * 阶段配置接口
 */
interface StageConfig {
	maxTime: number;
	reminderTime: number;
	audioFile: string;
}

/**
 * UltimatePKContent 组件的 Props 接口
 */
export interface UltimatePKContentProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 日志记录回调 */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
}

/**
 * 阶段配置映射
 */
const STAGE_CONFIGS: Record<PKStage, StageConfig> = {
	[PKStage.VIEWPOINT]: {
		maxTime: 90,
		reminderTime: 75,
		audioFile: '/audio/1.mp3'
	},
	[PKStage.INQUIRY]: {
		maxTime: 90,
		reminderTime: 75,
		audioFile: '/audio/1.mp3'
	},
	[PKStage.FREE_DEBATE]: {
		maxTime: 45,
		reminderTime: 30,
		audioFile: '/audio/1.mp3'
	},
	[PKStage.SUMMARY]: {
		maxTime: 120,
		reminderTime: 90,
		audioFile: '/audio/2.mp3'
	}
};

/**
 * 音频管理器接口
 */
interface AudioManager {
	isPlaying: boolean;
	playShortBell: () => Promise<void>;
	playLongBell: () => Promise<void>;
	preloadAudio: () => void;
}

/**
 * 计时器管理Hook
 */
function useUltimatePKTimer(currentStage: PKStage, audioManager: AudioManager, onLog?: UltimatePKContentProps['onLog']) {
	const [positiveTimer, setPositiveTimer] = useState<TimerState>({
		time: STAGE_CONFIGS[currentStage].maxTime,
		isRunning: false,
		maxTime: STAGE_CONFIGS[currentStage].maxTime
	});

	const [negativeTimer, setNegativeTimer] = useState<TimerState>({
		time: STAGE_CONFIGS[currentStage].maxTime,
		isRunning: false,
		maxTime: STAGE_CONFIGS[currentStage].maxTime
	});

	const [activeTimer, setActiveTimer] = useState<PKSide | null>(null);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);
	const reminderTriggeredRef = useRef<Set<string>>(new Set());

	// 格式化时间为 MM:SS 格式
	const formatTime = useCallback((time: number): string => {
		if (isNaN(time)) return "00:00";
		const minutes = Math.floor(time / 60);
		const seconds = Math.floor(time % 60);
		return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
	}, []);

	// 启动计时器
	const startTimer = useCallback((side: PKSide) => {
		try {
			// 检查计时器是否已经归零
			const currentTimer = side === PKSide.POSITIVE ? positiveTimer : negativeTimer;
			if (currentTimer.time <= 0) {
				onLog?.('warning', `${side === PKSide.POSITIVE ? '正方' : '反方'}计时器时间已到，无法启动`, {
					side,
					stage: currentStage,
					timestamp: Date.now()
				});
				return;
			}

			setActiveTimer(side);

			if (side === PKSide.POSITIVE) {
				setPositiveTimer(prev => ({ ...prev, isRunning: true }));
			} else {
				setNegativeTimer(prev => ({ ...prev, isRunning: true }));
			}

			onLog?.('info', `${side === PKSide.POSITIVE ? '正方' : '反方'}计时器启动`, {
				side,
				stage: currentStage,
				timestamp: Date.now()
			});
		} catch (error) {
			onLog?.('error', `启动计时器失败`, {
				side,
				error: error instanceof Error ? error.message : String(error),
				timestamp: Date.now()
			});
		}
	}, [currentStage, onLog, positiveTimer, negativeTimer]);

	// 暂停计时器
	const pauseTimer = useCallback((side: PKSide) => {
		if (side === PKSide.POSITIVE) {
			setPositiveTimer(prev => ({ ...prev, isRunning: false }));
		} else {
			setNegativeTimer(prev => ({ ...prev, isRunning: false }));
		}

		if (activeTimer === side) {
			setActiveTimer(null);
		}

		onLog?.('info', `${side === PKSide.POSITIVE ? '正方' : '反方'}计时器暂停`, {
			side,
			stage: currentStage,
			timestamp: Date.now()
		});
	}, [activeTimer, currentStage, onLog]);

	// 重置计时器
	const resetTimer = useCallback((side: PKSide) => {
		const maxTime = STAGE_CONFIGS[currentStage].maxTime;

		if (side === PKSide.POSITIVE) {
			setPositiveTimer({
				time: maxTime,
				isRunning: false,
				maxTime
			});
		} else {
			setNegativeTimer({
				time: maxTime,
				isRunning: false,
				maxTime
			});
		}

		if (activeTimer === side) {
			setActiveTimer(null);
		}

		onLog?.('info', `${side === PKSide.POSITIVE ? '正方' : '反方'}计时器重置`, {
			side,
			stage: currentStage,
			maxTime,
			timestamp: Date.now()
		});
	}, [activeTimer, currentStage, onLog]);

	// 切换计时器（用于自由辩论阶段）
	const switchTimer = useCallback(() => {
		if (activeTimer === PKSide.POSITIVE) {
			pauseTimer(PKSide.POSITIVE);
			startTimer(PKSide.NEGATIVE);
		} else if (activeTimer === PKSide.NEGATIVE) {
			pauseTimer(PKSide.NEGATIVE);
			startTimer(PKSide.POSITIVE);
		}

		onLog?.('info', '计时器切换', {
			from: activeTimer,
			to: activeTimer === PKSide.POSITIVE ? PKSide.NEGATIVE : PKSide.POSITIVE,
			stage: currentStage,
			timestamp: Date.now()
		});
	}, [activeTimer, currentStage, onLog, pauseTimer, startTimer]);

	// 计时器主循环
	useEffect(() => {
		if (positiveTimer.isRunning || negativeTimer.isRunning) {
			intervalRef.current = setInterval(() => {
				if (positiveTimer.isRunning) {
					setPositiveTimer(prev => {
						const newTime = Math.max(0, prev.time - 1);
						const stageConfig = STAGE_CONFIGS[currentStage];

						// 铃声提醒逻辑
						const reminderKey = `positive-${stageConfig.reminderTime}`;
						if (newTime === stageConfig.reminderTime && !reminderTriggeredRef.current.has(reminderKey)) {
							reminderTriggeredRef.current.add(reminderKey);
							audioManager.playShortBell();
							onLog?.('info', '正方计时器短铃提醒', {
								stage: currentStage,
								remainingTime: newTime,
								timestamp: Date.now()
							});
						} else if (newTime === 0) {
							audioManager.playLongBell();
							onLog?.('warning', '正方计时器时间到', {
								stage: currentStage,
								timestamp: Date.now()
							});
							return { ...prev, time: newTime, isRunning: false };
						}
						return { ...prev, time: newTime };
					});
				}

				if (negativeTimer.isRunning) {
					setNegativeTimer(prev => {
						const newTime = Math.max(0, prev.time - 1);
						const stageConfig = STAGE_CONFIGS[currentStage];

						// 铃声提醒逻辑
						const reminderKey = `negative-${stageConfig.reminderTime}`;
						if (newTime === stageConfig.reminderTime && !reminderTriggeredRef.current.has(reminderKey)) {
							reminderTriggeredRef.current.add(reminderKey);
							audioManager.playShortBell();
							onLog?.('info', '反方计时器短铃提醒', {
								stage: currentStage,
								remainingTime: newTime,
								timestamp: Date.now()
							});
						} else if (newTime === 0) {
							audioManager.playLongBell();
							onLog?.('warning', '反方计时器时间到', {
								stage: currentStage,
								timestamp: Date.now()
							});
							return { ...prev, time: newTime, isRunning: false };
						}
						return { ...prev, time: newTime };
					});
				}
			}, 1000);
		} else {
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
				intervalRef.current = null;
			}
		}

		return () => {
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
				intervalRef.current = null;
			}
		};
	}, [positiveTimer.isRunning, negativeTimer.isRunning, currentStage, onLog, audioManager]);

	// 阶段变化时重置计时器
	useEffect(() => {
		const maxTime = STAGE_CONFIGS[currentStage].maxTime;

		setPositiveTimer({
			time: maxTime,
			isRunning: false,
			maxTime
		});

		setNegativeTimer({
			time: maxTime,
			isRunning: false,
			maxTime
		});

		setActiveTimer(null);

		// 重置铃声提醒状态
		reminderTriggeredRef.current.clear();

		onLog?.('info', '阶段变化，计时器已重置', {
			stage: currentStage,
			maxTime,
			timestamp: Date.now()
		});
	}, [currentStage, onLog]);

	return {
		positiveTimer,
		negativeTimer,
		activeTimer,
		formatTime,
		startTimer,
		pauseTimer,
		resetTimer,
		switchTimer
	};
}

/**
 * 阶段管理Hook
 */
function useUltimatePKStage(onLog?: UltimatePKContentProps['onLog']) {
	const [currentStage, setCurrentStage] = useState<PKStage>(PKStage.VIEWPOINT);
	const [currentSpeaker, setCurrentSpeaker] = useState<string>('正方');

	// 设置阶段
	const setStage = useCallback((stage: PKStage) => {
		setCurrentStage(stage);

		// 根据阶段设置默认发言人
		switch (stage) {
			case PKStage.VIEWPOINT:
				setCurrentSpeaker('正方');
				break;
			case PKStage.INQUIRY:
				setCurrentSpeaker('正方');
				break;
			case PKStage.FREE_DEBATE:
				setCurrentSpeaker('正方');
				break;
			case PKStage.SUMMARY:
				setCurrentSpeaker('反方');
				break;
		}

		onLog?.('info', `阶段切换到: ${stage}`, {
			stage,
			speaker: currentSpeaker,
			timestamp: Date.now()
		});

		// 触发按钮组更新事件
		setTimeout(() => {
			window.dispatchEvent(new CustomEvent('ultimatePKStageChanged', {
				detail: {
					stage,
					isFreeDabateStage: stage === PKStage.FREE_DEBATE
				}
			}));
		}, 0);
	}, [currentSpeaker, onLog]);

	// 获取阶段配置
	const getStageConfig = useCallback((stage: PKStage): StageConfig => {
		return STAGE_CONFIGS[stage];
	}, []);

	return {
		currentStage,
		currentSpeaker,
		setStage,
		setCurrentSpeaker,
		getStageConfig
	};
}

/**
 * 音频管理Hook
 */
function useUltimatePKAudio(onLog?: UltimatePKContentProps['onLog']) {
	const shortBellRef = useRef<HTMLAudioElement | null>(null);
	const longBellRef = useRef<HTMLAudioElement | null>(null);
	const [isPlaying, setIsPlaying] = useState(false);

	// 预加载音频
	const preloadAudio = useCallback(() => {
		try {
			shortBellRef.current = new Audio('/audio/1.mp3');
			longBellRef.current = new Audio('/audio/2.mp3');

			shortBellRef.current.preload = 'auto';
			longBellRef.current.preload = 'auto';

			onLog?.('info', '音频文件预加载完成', {
				shortBell: '/audio/1.mp3',
				longBell: '/audio/2.mp3',
				timestamp: Date.now()
			});
		} catch (error) {
			onLog?.('error', '音频文件预加载失败', {
				error: error instanceof Error ? error.message : String(error),
				timestamp: Date.now()
			});
		}
	}, [onLog]);

	// 播放短铃声
	const playShortBell = useCallback(async () => {
		if (!shortBellRef.current || isPlaying) return;

		try {
			setIsPlaying(true);
			await shortBellRef.current.play();
			onLog?.('info', '短铃声播放', { timestamp: Date.now() });
		} catch (error) {
			onLog?.('error', '短铃声播放失败', {
				error: error instanceof Error ? error.message : String(error),
				timestamp: Date.now()
			});
		} finally {
			setIsPlaying(false);
		}
	}, [isPlaying, onLog]);

	// 播放长铃声
	const playLongBell = useCallback(async () => {
		if (!longBellRef.current || isPlaying) return;

		try {
			setIsPlaying(true);
			await longBellRef.current.play();
			onLog?.('info', '长铃声播放', { timestamp: Date.now() });
		} catch (error) {
			onLog?.('error', '长铃声播放失败', {
				error: error instanceof Error ? error.message : String(error),
				timestamp: Date.now()
			});
		} finally {
			setIsPlaying(false);
		}
	}, [isPlaying, onLog]);

	// 组件挂载时预加载音频
	useEffect(() => {
		preloadAudio();

		// 清理函数
		return () => {
			if (shortBellRef.current) {
				shortBellRef.current.pause();
				shortBellRef.current = null;
			}
			if (longBellRef.current) {
				longBellRef.current.pause();
				longBellRef.current = null;
			}
		};
	}, [preloadAudio]);

	return {
		isPlaying,
		playShortBell,
		playLongBell,
		preloadAudio
	};
}

export default function UltimatePKContent({
	className,
	height = "auto",
	onLog
}: UltimatePKContentProps) {
	// 使用自定义Hooks
	const stageManager = useUltimatePKStage(onLog);
	const audioManager = useUltimatePKAudio(onLog);
	const timerManager = useUltimatePKTimer(stageManager.currentStage, audioManager, onLog);

	// 暴露控制方法给全局，以便按钮组调用
	useEffect(() => {
		// 将控制方法挂载到window对象上，供按钮组使用
		window.ultimatePKControls = {
			// 阶段切换
			setStage: stageManager.setStage,
			// 正方控制
			startPositiveTimer: () => timerManager.startTimer(PKSide.POSITIVE),
			pausePositiveTimer: () => timerManager.pauseTimer(PKSide.POSITIVE),
			resetPositiveTimer: () => timerManager.resetTimer(PKSide.POSITIVE),
			// 反方控制
			startNegativeTimer: () => timerManager.startTimer(PKSide.NEGATIVE),
			pauseNegativeTimer: () => timerManager.pauseTimer(PKSide.NEGATIVE),
			resetNegativeTimer: () => timerManager.resetTimer(PKSide.NEGATIVE),
			// 全局控制
			pauseAllTimers: () => {
				timerManager.pauseTimer(PKSide.POSITIVE);
				timerManager.pauseTimer(PKSide.NEGATIVE);
			},
			switchTimer: timerManager.switchTimer,
			playShortBell: audioManager.playShortBell,
			playLongBell: audioManager.playLongBell,
			// 状态查询
			getCurrentStage: () => stageManager.currentStage,
			isFreeDabateStage: () => stageManager.currentStage === PKStage.FREE_DEBATE,
			// 按钮禁用状态查询
			shouldDisablePositiveControls: () => false,
			shouldDisableNegativeControls: () => false,
			// 触发按钮组重新渲染的方法
			triggerButtonGroupUpdate: () => {
				// 触发一个自定义事件来通知按钮组更新
				setTimeout(() => {
					window.dispatchEvent(new CustomEvent('ultimatePKStageChanged', {
						detail: {
							stage: stageManager.currentStage,
							isFreeDabateStage: stageManager.currentStage === PKStage.FREE_DEBATE
						}
					}));
				}, 0);
			}
		};

		// 当阶段变化时，触发按钮组更新（延迟执行避免循环）
		// 注意：这里不直接调用，而是在setStage方法中触发事件

		// 清理函数
		return () => {
			delete window.ultimatePKControls;
		};
	}, [stageManager, timerManager, audioManager, stageManager.currentStage]);

	// 构建CSS类名
	const cssClasses = [
		"ultimate-pk-content",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* A1 区域：当前阶段标题 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				width="100%"
				height="size-800"
				marginBottom="size-400"
				UNSAFE_style={{
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<Text UNSAFE_style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--spectrum-global-color-gray-800)' }}>
					{stageManager.currentStage}
				</Text>
			</View>

			{/* C1 & C2 区域：正方和反方倒计时显示 */}
			<Flex
				justifyContent="space-between"
				alignItems="center"
				marginBottom="size-300"
				gap="size-300"
			>
				{/* C1 区域：正方倒计时显示 */}
				<View
					backgroundColor="gray-50"
					borderRadius="small"
					width="45%"
					height="size-2000"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						boxShadow: `inset 0 0 0 5px ${timerManager.positiveTimer.time === 0
							? 'var(--spectrum-global-color-static-red-600)'
							: timerManager.positiveTimer.time <= 30
								? 'var(--spectrum-global-color-static-orange-600)'
								: timerManager.positiveTimer.isRunning
									? 'var(--spectrum-global-color-static-celery-700)'
									: 'var(--spectrum-global-color-gray-500)'
							}`
					}}
				>
					<Text UNSAFE_style={{
						fontSize: '60px',
						fontWeight: 'bold',
						fontFamily: 'monospace',
						color: 'var(--spectrum-global-color-gray-800)'
					}}>
						{timerManager.formatTime(timerManager.positiveTimer.time)}
					</Text>
				</View>

				{/* C2 区域：反方倒计时显示 */}
				<View
					backgroundColor="gray-50"
					borderRadius="small"
					width="45%"
					height="size-2000"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						boxShadow: `inset 0 0 0 5px ${timerManager.negativeTimer.time === 0
							? 'var(--spectrum-global-color-static-red-600)'
							: timerManager.negativeTimer.time <= 30
								? 'var(--spectrum-global-color-static-orange-600)'
								: timerManager.negativeTimer.isRunning
									? 'var(--spectrum-global-color-static-celery-700)'
									: 'var(--spectrum-global-color-gray-500)'
							}`
					}}
				>
					<Text UNSAFE_style={{
						fontSize: '60px',
						fontWeight: 'bold',
						fontFamily: 'monospace',
						color: 'var(--spectrum-global-color-gray-800)'
					}}>
						{timerManager.formatTime(timerManager.negativeTimer.time)}
					</Text>
				</View>
			</Flex >

			{/* B1 & B2 区域：正方和反方名称显示 */}
			< Flex justifyContent="space-between" alignItems="center" gap="size-300" >
				{/* B1 区域：正方名称 */}
				< View
					backgroundColor="gray-200"
					borderRadius="small"
					width="45%"
					height="size-600"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
					}
					}
				>
					<Text UNSAFE_style={{ fontSize: '14px', fontWeight: 'bold', color: 'var(--spectrum-global-color-gray-800)' }}>
						正方
					</Text>
				</View >

				{/* B2 区域：反方名称 */}
				< View
					backgroundColor="gray-200"
					borderRadius="small"
					width="45%"
					height="size-600"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
					}}
				>
					<Text UNSAFE_style={{ fontSize: '14px', fontWeight: 'bold', color: 'var(--spectrum-global-color-gray-800)' }}>
						反方
					</Text>
				</View >
			</Flex >
		</View >
	);
}
