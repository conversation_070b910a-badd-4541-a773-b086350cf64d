/**
 * AudioPlayer 组件样式
 *
 * 设计原则：
 * - 遵循 Adobe React Spectrum 设计规范
 * - 支持深色主题适配
 * - 响应式布局设计
 * - 无障碍访问优化
 * - 平滑动画过渡效果
 */

/* 主容器样式 */
.audio-player {
  width: 100%;
  max-width: 400px; /* 缩小50%: 600px -> 300px */
  padding: 6px 8px; /* 缩小50%: 12px 16px -> 6px 8px */
  background-color: #1d1d1d;
  border-radius: 8px; /* 缩小50%: 20px -> 10px */
  transition: all 0.2s ease;
}

.audio-player:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15); /* 缩小50%: 4px 8px -> 2px 4px */
}

/* 禁用状态 */
.audio-player.disabled {
  opacity: 0.6;
  pointer-events: none;
  background-color: #1d1d1d;
}

/* 加载状态 */
.audio-player.loading {
  opacity: 0.8;
}

.audio-player.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 控件容器样式 */
.audio-player-controls {
  position: relative;
  width: 100%;
  min-height: 20px; /* 缩小50%: 40px -> 20px */
}

/* 按钮样式优化 */
.audio-player-play-pause,
.audio-player-replay,
.audio-player-volume {
  min-width: 20px; /* 缩小50%: 40px -> 20px */
  min-height: 20px; /* 缩小50%: 40px -> 20px */
  border-radius: 5px; /* 缩小50%: 10px -> 5px */
  transition: all 0.2s ease;
}

.audio-player-play-pause:hover,
.audio-player-replay:hover,
.audio-player-volume:hover {
  background-color: var(--spectrum-global-color-gray-200);
  transform: scale(1.05);
}

.audio-player-play-pause:active,
.audio-player-replay:active,
.audio-player-volume:active {
  transform: scale(0.95);
}

/* 播放/暂停按钮特殊样式 */
.audio-player-play-pause {
  background-color: #006eef !important;
  color: white;
}

.audio-player-play-pause:hover {
  background-color: var(--spectrum-global-color-blue-700);
}

/* 时间显示样式 */
.audio-player-time {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 10px; /* 调整为更可读的大小: 7px -> 10px */
  font-weight: 500;
  color: var(--spectrum-global-color-gray-800);
  min-width: 50px; /* 相应调整宽度: 40px -> 50px */
  text-align: center;
  white-space: nowrap;
}

/* 进度条容器样式 */
.audio-player-progress-container {
  position: relative;
  margin: 0 0px; /* 缩小50%: 8px -> 4px */
  min-width: 60px; /* 缩小50%: 120px -> 60px */
  padding: 6px 8px; /* 增加垂直padding为指示器预留更多空间 */
}

/* 进度条样式 */
.audio-player-progress-bar {
  position: relative;
  width: 100%;
  height: 5px; /* 缩小50%: 8px -> 4px */
  background-color: var(--spectrum-global-color-gray-300);
  border-radius: 4px; /* 缩小50%: 4px -> 2px */
  cursor: pointer;
  transition: all 0.2s ease;
  /* 移除 overflow: hidden 以显示拖拽指示器 */
}

/* 进度条禁用状态 */
.audio-player-progress-bar.disabled {
  background-color: var(--spectrum-global-color-gray-200);
  cursor: not-allowed;
  opacity: 0.6;
}

.audio-player-progress-bar.disabled:hover {
  height: 4px; /* 禁用时不放大 */
  transform: none;
  box-shadow: none;
}

.audio-player-progress-bar:hover {
  height: 8px; /* 悬停时增加高度 */
  /* 高度从5px增加到8px，需要向上偏移(8-5)/2 = 1.5px来保持居中 */
  transform: translateY(-1.5px);
}

/* 进度条填充样式 */
.audio-player-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #006eef !important;
  border-radius: 2px; /* 缩小50%: 4px -> 2px */
  transition: width 0.1s ease;
  min-width: 1px; /* 缩小50%: 2px -> 1px */
}

.audio-player-progress-bar:hover .audio-player-progress-fill {
  box-shadow: 0 0 4px rgba(0, 110, 239, 0.4); /* 缩小50%: 8px -> 4px */
}

/* 进度条拖拽指示器 - 新的独立指示器 */
.audio-player-drag-indicator {
  position: absolute;
  top: 50%;
  width: 12px; /* 增加实际大小以便点击 */
  height: 12px;
  background-color: #b0b0b0;
  /* border: 2px solid #006EEF; */
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 1; /* 始终可见，但稍微透明 */
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  cursor: grab;
  /* 移除 padding 和 margin，使用更大的实际尺寸来增加点击区域 */
}

/* 悬停状态显示指示器 */
.audio-player-progress-bar:hover .audio-player-drag-indicator {
  opacity: 1; /* 悬停时完全不透明 */
  /* 当进度条悬停变高时，保持指示器在视觉中心 */
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 拖拽状态样式 */
.audio-player-drag-indicator.dragging {
  opacity: 1;
  width: 14px; /* 拖拽时稍微放大 */
  height: 14px;
  background-color: #006eef;
  border: 3px solid #ffffff;
  box-shadow: 0 4px 8px rgba(0, 110, 239, 0.4);
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(1.1); /* 减少缩放避免过大 */
}

/* 进度条拖拽状态 */
.audio-player-progress-bar.dragging {
  cursor: grabbing;
}

.audio-player-progress-fill.dragging {
  transition: none; /* 拖拽时禁用过渡动画 */
}

/* 保留原有的 ::after 指示器作为备用 */
.audio-player-progress-fill::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -3px; /* 缩小50%: -6px -> -3px */
  width: 6px; /* 缩小50%: 12px -> 6px */
  height: 6px; /* 缩小50%: 12px -> 6px */
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(255, 255, 255, 0.1); /* 缩小50%: 2px -> 1px */
  border-radius: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 缩小50%: 2px 4px -> 1px 2px */
  display: none; /* 隐藏原有指示器，使用新的独立指示器 */
}

.audio-player-progress-bar:hover .audio-player-progress-fill::after {
  opacity: 0; /* 禁用原有指示器 */
}

/* 音量按钮样式 */
.audio-player-volume {
  position: relative;
}

.audio-player-volume[aria-label*='静音'] {
  color: var(--spectrum-global-color-red-600);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .audio-player {
    padding: 4px 6px; /* 缩小50%: 8px 12px -> 4px 6px */
  }

  .audio-player-controls {
    gap: 4px; /* 缩小50%: 8px -> 4px */
  }

  .audio-player-play-pause,
  .audio-player-replay,
  .audio-player-volume {
    min-width: 18px; /* 缩小50%: 36px -> 18px */
    min-height: 18px; /* 缩小50%: 36px -> 18px */
  }

  .audio-player-time {
    font-size: 8px; /* 调整为更可读的大小: 6px -> 8px */
    min-width: 45px; /* 相应调整宽度: 35px -> 45px */
  }

  .audio-player-progress-container {
    margin: 0 2px; /* 缩小50%: 4px -> 2px */
    min-width: 40px; /* 缩小50%: 80px -> 40px */
  }

  /* 移动端拖拽指示器调整 */
  .audio-player-drag-indicator {
    width: 16px; /* 移动端更大便于触摸 */
    height: 16px;
    /* 移除 padding 和 margin，使用更大的实际尺寸 */
  }

  .audio-player-drag-indicator.dragging {
    width: 18px;
    height: 18px;
    transform: translate(-50%, -50%) scale(1.05); /* 移动端减少缩放 */
  }
}

/* 焦点状态优化 */
.audio-player-play-pause:focus-visible,
.audio-player-replay:focus-visible,
.audio-player-volume:focus-visible {
  outline: 1px solid var(--spectrum-global-color-blue-600); /* 缩小50%: 2px -> 1px */
  outline-offset: 1px; /* 缩小50%: 2px -> 1px */
}

/* 错误状态样式 */
.audio-player.error {
  border-color: var(--spectrum-global-color-red-600);
  background-color: var(--spectrum-global-color-red-100);
}

.audio-player.error .audio-player-time {
  color: var(--spectrum-global-color-red-800);
}

/* 成功状态样式 */
.audio-player.success .audio-player-progress-fill {
  background: linear-gradient(
    90deg,
    var(--spectrum-global-color-green-500),
    var(--spectrum-global-color-green-600)
  );
}

/* 自动播放提示样式 */
.audio-player-play-prompt {
  animation: fadeInSlideDown 0.3s ease-out;
  border: 1px solid var(--spectrum-global-color-blue-300);
}

@keyframes fadeInSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.audio-player-play-prompt:hover {
  background-color: var(--spectrum-global-color-blue-200);
  transition: background-color 0.2s ease;
}

/* 自动播放提示中的播放按钮样式 */
.audio-player-play-prompt .spectrum-ActionButton {
  transition: all 0.2s ease;
}

.audio-player-play-prompt .spectrum-ActionButton:hover {
  background-color: var(--spectrum-global-color-blue-700) !important;
  transform: scale(1.05);
}
