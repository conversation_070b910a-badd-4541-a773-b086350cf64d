// React 核心依赖导入
import React, { useMemo, useCallback } from "react";
// Adobe React Spectrum UI 组件库导入
import { View, Grid, Text, Flex, ActionButton, ButtonGroup } from "@adobe/react-spectrum";
// 导入类型
import type { RankingData, PaginatedRankingData, ApiError } from "../services/api/types";
import type { LogFunction } from "../hooks/useRaceApi";

/**
 * TimeRaceRankingContent 组件的 Props 接口
 */
export interface TimeRaceRankingContentProps {
	/** 环节名称 */
	sectionName: '争分夺秒' | '同分加赛';
	/** 排名数据（支持分页数据） */
	rankingData: RankingData | PaginatedRankingData | null;
	/** 加载状态 */
	loading?: boolean;
	/** 错误信息 */
	error?: Error | ApiError | null;
	/** 是否正在轮询 */
	isPolling?: boolean;
	/** 最后更新时间 */
	lastUpdateTime?: number;
	/** 刷新回调 */
	onRefresh?: () => void;
	/** 自定义 CSS 类名 */
	className?: string;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 日志记录回调函数 */
	onLog?: LogFunction;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
}

/**
 * TimeRaceRankingContent - 争分夺秒和同分加赛动态排名内容组件
 *
 * 功能特性：
 * - 支持"争分夺秒"和"同分加赛"环节的实时排名显示
 * - 支持分页显示，默认每页8名选手
 * - 复用现有排名系统的UI设计和数据处理逻辑
 * - 使用 Adobe React Spectrum 组件库
 * - 按总分降序排列，总分相同时按选手ID升序排列
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const TimeRaceRankingContent: React.FC<TimeRaceRankingContentProps> = ({
	sectionName,
	rankingData,
	loading = false,
	error = null,
	isPolling = false,
	lastUpdateTime = Date.now(),
	onRefresh,
	className,
	height = "auto",
	onLog,
	animated = true,
}) => {
	// 检查是否为分页数据
	const isPaginatedData = rankingData && 'paginationInfo' in rankingData;
	const paginationInfo = isPaginatedData ? (rankingData as PaginatedRankingData).paginationInfo : null;

	// 计算实际的更新时间戳：优先使用API获取时间戳，避免分页切换时时间戳变化
	const actualUpdateTime = rankingData?.fetchTimestamp || lastUpdateTime;

	// 计算动态列宽配置
	const columnConfig = useMemo(() => {
		if (!rankingData) return ["size-500", "2fr", "1fr"];

		const stageCount = rankingData.stages.length;
		const baseColumns = ["size-500", "2fr"]; // 排名列 + 选手名列

		// 根据环节数量动态分配得分列宽度
		let scoreColumnWidth: string;
		if (stageCount <= 3) {
			scoreColumnWidth = "1fr";
		} else if (stageCount <= 6) {
			scoreColumnWidth = "0.8fr";
		} else {
			scoreColumnWidth = "0.6fr";
		}

		const stageColumns = rankingData.stages.map(() => scoreColumnWidth);
		const totalColumn = ["1.2fr"]; // 总分列稍宽

		return [...baseColumns, ...stageColumns, ...totalColumn];
	}, [rankingData]);

	// 构建网格区域配置
	const gridAreas = useMemo(() => {
		if (!rankingData) return [`rank name total`];

		const stageAreas = rankingData.stages.map((_, index) => `stage${index}`).join(" ");
		return [`rank name ${stageAreas} total`];
	}, [rankingData]);

	// 处理刷新
	const handleRefresh = useCallback(() => {
		if (onRefresh) {
			onRefresh();
			onLog?.('info', `手动刷新${sectionName}排名数据`, {
				sectionName,
				timestamp: Date.now(),
				action: 'manual_refresh_click'
			});
		}
	}, [onRefresh, sectionName, onLog]);

	// 构建CSS类名
	const cssClasses = useMemo(() => [
		"time-race-ranking-content",
		animated ? "animated" : "",
		loading ? "loading" : "",
		error ? "error" : "",
		className || "",
	]
		.filter(Boolean)
		.join(" "), [animated, loading, error, className]);

	// 记录组件渲染日志和错误处理 - 移到所有条件return之前
	React.useEffect(() => {
		try {
			// 数据完整性检查
			if (rankingData) {
				if (!rankingData.players || !Array.isArray(rankingData.players)) {
					throw new Error('选手数据格式错误');
				}
				if (!rankingData.stages || !Array.isArray(rankingData.stages)) {
					throw new Error('环节数据格式错误');
				}

				// 添加分页数据调试
				console.log('[TimeRaceRankingContent] 接收到的数据', {
					totalPlayers: rankingData.totalPlayers,
					playersCount: rankingData.players.length,
					isPaginated: isPaginatedData,
					paginationInfo: isPaginatedData ? paginationInfo : null,
					sectionName
				});

				onLog?.('info', `${sectionName}排名内容组件已渲染`, {
					totalPlayers: rankingData.totalPlayers,
					stageCount: rankingData.stages.length,
					isPolling,
					isPaginated: isPaginatedData,
					currentPage: paginationInfo?.currentPage,
					timestamp: Date.now(),
					action: 'time_race_ranking_content_render'
				});
			}
		} catch (error) {
			onLog?.('error', `${sectionName}排名内容组件渲染时发现数据问题`, {
				error: error instanceof Error ? error.message : '未知错误',
				rankingData: rankingData,
				timestamp: Date.now(),
				action: 'time_race_ranking_content_render_error'
			});
		}
	}, [rankingData, onLog, sectionName, isPolling, isPaginatedData, paginationInfo]);

	// 临时调试：清除缓存功能
	const clearAllCaches = async () => {
		try {
			// 清除表结构缓存
			const { GlobalTableStructureCache } = await import('../services/api/tableStructureCache');
			GlobalTableStructureCache.clearCache();

			// 清除请求去重器
			const { GlobalRequestDeduplicator } = await import('../services/api/requestDeduplicator');
			GlobalRequestDeduplicator.clearAll();

			console.log('[DEBUG] 所有缓存已清除，即将刷新数据');

			// 刷新数据
			handleRefresh();
		} catch (error) {
			console.error('[DEBUG] 清除缓存失败:', error);
		}
	};

	// 如果有错误，显示错误信息
	if (error) {
		return (
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				padding="size-300"
				height={height}
				UNSAFE_className={className}
			>
				<Flex direction="column" alignItems="center" justifyContent="center" height="100%">
					<Text UNSAFE_style={{
						fontSize: '16px',
						color: 'var(--spectrum-global-color-red-600)',
						marginBottom: '16px'
					}}>
						{sectionName}排名数据加载失败
					</Text>
					<Text UNSAFE_style={{
						fontSize: '14px',
						color: 'var(--spectrum-global-color-gray-600)',
						marginBottom: '16px'
					}}>
						{error.message}
					</Text>
					<ButtonGroup>
						<ActionButton onPress={handleRefresh}>
							重试
						</ActionButton>
						<ActionButton onPress={clearAllCaches}>
							清除缓存并重试
						</ActionButton>
					</ButtonGroup>
				</Flex>
			</View>
		);
	}

	// 如果正在加载且没有数据，显示加载状态
	if (loading && !rankingData) {
		return (
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				padding="size-300"
				height={height}
				UNSAFE_className={className}
			>
				<Flex direction="column" alignItems="center" justifyContent="center" height="100%">
					<Text UNSAFE_style={{
						fontSize: '16px',
						color: 'var(--spectrum-global-color-gray-700)'
					}}>
						正在加载{sectionName}排名数据...
					</Text>
				</Flex>
			</View>
		);
	}

	// 如果没有数据，显示空状态
	if (!rankingData || !rankingData.players || rankingData.players.length === 0) {
		return (
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				padding="size-300"
				height={height}
				UNSAFE_className={className}
			>
				<Flex direction="column" alignItems="center" justifyContent="center" height="100%">
					<Text UNSAFE_style={{
						fontSize: '16px',
						color: 'var(--spectrum-global-color-gray-700)',
						marginBottom: '16px'
					}}>
						暂无{sectionName}排名数据
					</Text>
					<ActionButton onPress={handleRefresh}>
						刷新数据
					</ActionButton>
				</Flex>
			</View>
		);
	}

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
			UNSAFE_style={{
				// 添加全局样式支持
				'--ranking-scroll-width': '6px',
				'--ranking-scroll-radius': '3px',
				'--ranking-transition': '0.2s ease'
			} as React.CSSProperties}
		>
			{/* 内联样式定义 - 参考RankingContent.tsx */}
			<style>{`
				.ranking-list-container::-webkit-scrollbar {
					width: var(--ranking-scroll-width);
				}
				.ranking-list-container::-webkit-scrollbar-track {
					background: transparent;
				}
				.ranking-list-container::-webkit-scrollbar-thumb {
					background: var(--spectrum-global-color-gray-400);
					border-radius: var(--ranking-scroll-radius);
					transition: var(--ranking-transition);
				}
				.ranking-list-container::-webkit-scrollbar-thumb:hover {
					background: var(--spectrum-global-color-gray-600);
				}
				.ranking-row:hover {
					background-color: var(--spectrum-global-color-gray-200) !important;
				}
			`}</style>

			{/* A1 区域：排行榜标题 - 参考RankingContent.tsx */}
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="100%"
				height="size-700"
				marginBottom="size-400"
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center'
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '24px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)'
				}}>
					{sectionName}排行榜
				</Text>
			</View>

			{/* 字段显示区域：列标题 - 参考RankingContent.tsx */}
			<Grid
				areas={gridAreas}
				columns={columnConfig}
				gap="size-200"
				marginBottom="size-300"
			>
				{/* 排名列标题 */}
				<View gridArea="rank">
					<Text UNSAFE_style={{
						fontSize: '14px',
						fontWeight: 'bold',
						color: 'var(--spectrum-global-color-gray-700)',
						textAlign: 'center',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center'
					}}>
						排名
					</Text>
				</View>

				{/* 选手名列标题 */}
				<View gridArea="name">
					<Text UNSAFE_style={{
						fontSize: '14px',
						fontWeight: 'bold',
						color: 'var(--spectrum-global-color-gray-700)',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center'
					}}>
						选手名称
					</Text>
				</View>

				{/* 各环节列标题 */}
				{rankingData.stages.map((stageName, index) => (
					<View key={stageName} gridArea={`stage${index}`}>
						<Text UNSAFE_style={{
							fontSize: '14px',
							fontWeight: 'bold',
							color: 'var(--spectrum-global-color-gray-700)',
							textAlign: 'center',
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'center'
						}}>
							{stageName}
						</Text>
					</View>
				))}

				{/* 总分列标题 */}
				<View gridArea="total">
					<Text UNSAFE_style={{
						fontSize: '14px',
						fontWeight: 'bold',
						color: 'var(--spectrum-global-color-gray-700)',
						textAlign: 'center',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center'
					}}>
						总分
					</Text>
				</View>
			</Grid>

			{/* 排名列表区域 - 支持虚拟滚动优化 */}
			<View
				UNSAFE_style={{
					maxHeight: '400px',
					overflowY: 'auto',
					// 自定义滚动条样式
					scrollbarWidth: 'thin' as const,
					scrollbarColor: 'var(--spectrum-global-color-gray-400) transparent'
				}}
				UNSAFE_className="ranking-list-container"
			>
				{rankingData.players.map((player, index) => (
					<Grid
						key={player.playerId}
						areas={gridAreas}
						columns={columnConfig}
						gap="size-200"
						UNSAFE_style={{
							backgroundColor: index % 2 === 0 ? 'var(--spectrum-global-color-gray-50)' : 'transparent',
							padding: '1px',
							borderRadius: '4px',
							transition: 'background-color 0.2s ease',
							cursor: 'default'
						}}
						UNSAFE_className="ranking-row"
					>
						{/* B区域：排名序号 - 添加前三名特殊颜色 */}
						<View gridArea="rank">
							<Flex alignItems="center" justifyContent="center" height="size-500">
								<Text UNSAFE_style={{
									fontSize: '16px',
									fontWeight: 'bold',
									color: player.rank && player.rank <= 3
										? (player.rank === 1 ? '#FFD700' : player.rank === 2 ? '#C0C0C0' : '#CD7F32')
										: 'var(--spectrum-global-color-gray-800)'
								}}>
									{player.rank || '-'}
								</Text>
							</Flex>
						</View>

						{/* C区域：选手名称 */}
						<View gridArea="name">
							<Flex alignItems="center" height="size-500">
								<Text UNSAFE_style={{
									fontSize: '14px',
									color: 'var(--spectrum-global-color-gray-800)',
									fontWeight: '500'
								}}>
									{player.playerName}
								</Text>
							</Flex>
						</View>

						{/* D区域：各环节得分 */}
						{rankingData.stages.map((stageName, stageIndex) => (
							<View key={stageName} gridArea={`stage${stageIndex}`}>
								<Flex alignItems="center" justifyContent="center" height="size-500">
									<Text UNSAFE_style={{
										fontSize: '14px',
										color: 'var(--spectrum-global-color-gray-700)',
										textAlign: 'center'
									}}>
										{player.stageScores[stageName] || 0}
									</Text>
								</Flex>
							</View>
						))}

						{/* D区域：总分 */}
						<View gridArea="total">
							<Flex alignItems="center" justifyContent="center" height="size-500">
								<Text UNSAFE_style={{
									fontSize: '16px',
									fontWeight: 'bold',
									color: 'var(--spectrum-global-color-gray-900)'
								}}>
									{player.totalScore}
								</Text>
							</Flex>
						</View>
					</Grid>
				))}
			</View>

			{/* 数据统计信息 - 参考RankingContent.tsx */}
			<View marginTop="size-400">
				<Flex justifyContent="space-between" alignItems="center" wrap>
					<Text UNSAFE_style={{
						fontSize: '12px',
						color: 'var(--spectrum-global-color-gray-600)'
					}}>
						{paginationInfo && paginationInfo.hasMultiplePages
							? `第 ${paginationInfo.currentPage}/${paginationInfo.totalPages} 页，共 ${rankingData.totalPlayers} 名选手参与${sectionName}，${rankingData.stages.length} 个环节`
							: `共 ${rankingData.totalPlayers} 名选手参与${sectionName}，${rankingData.stages.length} 个环节`
						}
					</Text>

					<Flex alignItems="center" gap="size-100">
						{/* 轮询状态指示 */}
						{isPolling && (
							<Text UNSAFE_style={{
								fontSize: '12px',
								color: 'var(--spectrum-global-color-green-600)'
							}}>
								实时更新中
							</Text>
						)}

						<Text UNSAFE_style={{
							fontSize: '12px',
							color: 'var(--spectrum-global-color-gray-600)'
						}}>
							更新时间：{new Date(actualUpdateTime).toLocaleTimeString()}
						</Text>
					</Flex>
				</Flex>

				{/* 大数据量性能提示 */}
				{rankingData.totalPlayers > 50 && (
					<View marginTop="size-200">
						<Text UNSAFE_style={{
							fontSize: '11px',
							color: 'var(--spectrum-global-color-gray-500)',
							fontStyle: 'italic',
							textAlign: 'center'
						}}>
							数据量较大，已启用滚动优化以提升性能
						</Text>
					</View>
				)}
			</View>
		</View>
	);
};

export default TimeRaceRankingContent;
