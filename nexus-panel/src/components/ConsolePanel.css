/* ===== Warp 风格现代化终端界面 ===== */

/* 现代等宽字体栈 - 优先使用最新的编程字体 */
:root {
  --console-font-family:
    'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Cascadia Code',
    'Roboto Mono', 'Consolas', 'Courier New', monospace;
  --console-font-size-base: 12px;
  --console-font-size-small: 10px;
  --console-font-size-tiny: 9px;

  /* Warp 风格间距系统 - 紧凑化设计 */
  --console-spacing-xs: 2px;
  --console-spacing-sm: 4px;
  --console-spacing-md: 6px;
  --console-spacing-lg: 8px;
  --console-spacing-xl: 12px;

  /* Warp 风格深色调色板 */
  --console-bg-primary: #1a1b26;
  --console-bg-secondary: #24283b;
  --console-bg-tertiary: #2f3349;
  --console-text-primary: #c0caf5;
  --console-text-secondary: #9aa5ce;
  --console-text-muted: #565f89;
  --console-border-color: #3b4261;
  --console-hover-bg: rgba(192, 202, 245, 0.1);
}

/* 控台面板主容器 - Warp 风格现代化 */
.console-panel {
  position: relative;
  background: linear-gradient(
    135deg,
    var(--console-bg-primary) 0%,
    var(--console-bg-secondary) 100%
  );
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  border-radius: 8px 8px 0px 0px;
  font-family: var(--console-font-family);

  /* 现代化边框和阴影 */
  border-top: 1px solid var(--console-border-color);
  box-shadow:
    0 -4px 16px rgba(0, 0, 0, 0.25),
    0 -1px 4px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);

  /* 平滑过渡动画 */
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease,
    background 0.2s ease;
}

/* 拖拽调整状态 */
.console-panel.resizing {
  user-select: none;
  transition: none;
}

/* 悬浮窗模式样式 */
.console-panel.floating {
  border-radius: 8px; /* 四个角都圆角 */
  border: 1px solid var(--console-border-color);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(24, 24, 24, 0.95);
}

/* 拖拽调整高度的分隔条 - 增强视觉效果 */
.console-panel-resize-handle {
  position: absolute;
  top: -4px; /* 稍微向上移动，与边框重叠 */
  left: 0;
  right: 0;
  height: 10px; /* 增加高度，提供更大的交互区域 */
  cursor: ns-resize;
  background-color: transparent;
  z-index: 15; /* 确保在分割线之上 */

  /* 添加过渡动画 */
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease;

  /* 添加微妙的视觉提示 */
  border-radius: 4px 4px 0 0;
}

.console-panel-resize-handle:hover {
  background: linear-gradient(
    to bottom,
    rgba(0, 110, 239, 1) 0%,
    rgba(0, 110, 239, 0.8) 50%,
    rgba(0, 110, 239, 0.6) 100%
  );
  cursor: ns-resize;
}

/* 拖拽进行中状态 - 强化视觉反馈 */
.console-panel.resizing .console-panel-resize-handle {
  background: linear-gradient(
    to bottom,
    rgba(0, 110, 239, 1) 0%,
    rgba(0, 110, 239, 0.8) 50%,
    rgba(0, 110, 239, 0.6) 100%
  );
  cursor: ns-resize;
  /* 增强发光效果 */
  /* box-shadow:
    0 0 12px rgba(0, 110, 239, 0.6),
    0 -2px 6px rgba(0, 110, 239, 0.8); */
}

/* ===== Warp 风格标题栏 ===== */

/* 标题栏主容器 */
.console-panel-header {
  min-height: 36px;
  border-radius: 8px 8px 0 0;
  background-color: var(--spectrum-global-color-gray-200) !important;
  border-bottom: 1px solid var(--console-border-color);
  display: flex;
  align-items: center;
  padding: 0 var(--console-spacing-xl);
  font-family: var(--console-font-family);
  transition: all 0.2s ease;
}

/* 可点击标题栏的悬停效果 */
.console-panel-header-clickable:hover {
  background-color: var(--spectrum-global-color-gray-300) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 可点击标题栏的按下效果 */
/* .console-panel-header-clickable:active {
  background: var(--spectrum-global-color-gray-200) !important;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.3);
  transform: translateY(1px);
} */

/* 展开/折叠图标 - Warp 风格 */
.console-panel-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: var(--console-spacing-md);
  transition: all 0.2s ease;
  border-radius: 3px;
}

.console-panel-toggle-icon svg {
  width: 14px;
  height: 14px;
  color: var(--console-text-secondary);
  transition: all 0.2s ease;
}

/* 标题栏悬停时图标的变化 */
.console-panel-header-clickable:hover .console-panel-toggle-icon {
  background: rgba(192, 202, 245, 0.1);
}

.console-panel-header-clickable:hover .console-panel-toggle-icon svg {
  color: var(--console-text-primary);
  transform: scale(1.1);
}

/* 标题文字 - Warp 风格 */
.console-panel-title {
  font-size: var(--console-font-size-base);
  font-weight: 600;
  color: #b0b0b0 !important;
  font-family: var(--console-font-family);
  letter-spacing: 0.3px;
}

/* 日志数量徽章 - 现代化设计 */
.console-panel-badge {
  border-radius: 8px;
  padding: 3px 8px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(192, 202, 245, 0.1) !important;
  margin-left: var(--console-spacing-md);
}

.console-panel-badge-text {
  font-size: var(--console-font-size-tiny);
  font-weight: 700;
  color: #b0b0b0 !important;
  line-height: 1;
  font-family: var(--console-font-family);
}

/* 操作按钮 - Warp 风格 */
.console-panel-action {
  min-width: auto !important;
  padding: var(--console-spacing-md) !important;
  background: rgba(192, 202, 245, 0.1) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  width: 26px;
  height: 26px;
}

.console-panel-action:hover {
  background: rgba(192, 202, 245, 0.2) !important;
  transform: translateY(-1px);
}

.console-panel-action svg {
  width: 16px;
  height: 16px;
  color: var(--console-text-primary) !important;
}

/* ===== Warp 风格内容区域 ===== */

/* 内容区域主容器 */
.console-panel-content {
  position: relative;
  background: #181818;
  border-radius: 0 0 8px 8px;
  /* 确保内容区域完全填充，避免黑边 */
  box-sizing: border-box;
  min-height: 0; /* 允许内容收缩 */
}

/* 空状态 - 现代化设计 */
.console-panel-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 120px;
  gap: var(--console-spacing-lg);
}

.console-panel-empty-text {
  font-size: var(--console-font-size-base);
  color: var(--console-text-muted);
  font-style: italic;
  font-family: var(--console-font-family);
  text-align: center;
}

/* 空状态图标 */
.console-panel-empty-icon {
  width: 48px;
  height: 48px;
  opacity: 0.3;
  color: var(--console-text-muted);
}

/* ===== Warp 风格日志条目布局 ===== */

/* 日志条目容器 - 紧凑化设计 */
.console-panel-log-entry {
  padding: var(--console-spacing-sm) 0;
  border-left: 2px solid transparent;
  transition: all 0.15s ease;
  position: relative;
}

/* 悬停效果 - Warp 风格行高亮 */
.console-panel-log-entry:hover {
  background: var(--console-hover-bg);
  border-radius: 4px;
  margin: 0 -var(--console-spacing-lg);
}

/* 日志条目内容布局 - 三列对齐设计 */
.console-panel-log-content {
  display: grid;
  grid-template-columns: 50px 60px 1fr;
  gap: var(--console-spacing-md);
  align-items: baseline;
  font-family: var(--console-font-family);
  line-height: 1.3;
}

/* 时间戳列 */
.console-panel-log-timestamp-column {
  font-size: var(--console-font-size-small);
  color: var(--console-text-muted);
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 级别标签列 */
.console-panel-log-level-column {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 消息内容列 */
.console-panel-log-message-column {
  min-width: 0; /* 允许内容收缩 */
  word-break: break-word;
}

/* ===== Warp 风格时间戳优化 ===== */

/* 时间戳样式 - 精确到秒 */
.console-panel-log-timestamp {
  font-size: var(--console-font-size-small);
  color: var(--console-text-muted);
  font-family: var(--console-font-family);
  font-weight: 400;
  letter-spacing: 0.3px;
  min-width: 60px;
  text-align: left;
  font-variant-numeric: tabular-nums; /* 等宽数字 */
}

/* 时间戳悬停效果 */
.console-panel-log-entry:hover .console-panel-log-timestamp {
  color: var(--console-text-secondary);
  font-weight: 500;
}

/* ===== Warp 风格日志级别颜色方案 ===== */

/* 日志级别基础样式 */
.console-panel-log-level {
  font-size: var(--console-font-size-tiny);
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-family: var(--console-font-family);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
}

/* INFO 级别 - 蓝色系 */
.console-panel-log-level-info {
  color: #60a5fa;
}

/* SUCCESS 级别 - 绿色系 */
.console-panel-log-level-success {
  color: #34d399;
}

/* WARNING 级别 - 橙色系 */
.console-panel-log-level-warning {
  color: #fbbf24;
}

/* ERROR 级别 - 红色系 */
.console-panel-log-level-error {
  color: #f87171;
}

/* SEND 级别 - 紫色系 */
.console-panel-log-level-send {
  color: #a855f7;
}

/* GET 级别 - 青色系 */
.console-panel-log-level-get {
  color: #06b6d4;
}

/* ===== Warp 风格消息内容和语法高亮 ===== */

/* 日志消息基础样式 */
.console-panel-log-message {
  font-size: var(--console-font-size-base);
  color: #b0b0b0 !important;
  line-height: 1.3;
  word-break: break-word;
  font-family: var(--console-font-family);
  font-weight: 400;
}

/* MQTT 主题高亮 */
.console-panel-log-message .mqtt-topic {
  color: #7aa2f7;
  font-weight: 500;
  background: rgba(122, 162, 247, 0.1);
  padding: 1px 3px;
  border-radius: 2px;
}

/* JSON 关键词高亮 */
.console-panel-log-message .json-key {
  color: #bb9af7;
  font-weight: 500;
}

.console-panel-log-message .json-string {
  color: #9ece6a;
}

.console-panel-log-message .json-number {
  color: #ff9e64;
}

.console-panel-log-message .json-boolean {
  color: #f7768e;
  font-weight: 600;
}

/* 日志详情 - 代码块风格 */
.console-panel-log-details {
  font-size: var(--console-font-size-small);
  color: #727272;
  margin-top: var(--console-spacing-sm);
  font-family: var(--console-font-family);
  padding: var(--console-spacing-md) var(--console-spacing-lg);
  border-radius: 4px;
  white-space: pre-wrap;
  position: relative;
  overflow-x: auto;
}

/* 详情区域的语法高亮 */
.console-panel-log-details .highlight-json {
  color: var(--console-text-primary);
}

.console-panel-log-details .highlight-key {
  color: #bb9af7;
}

.console-panel-log-details .highlight-string {
  color: #9ece6a;
}

.console-panel-log-details .highlight-number {
  color: #ff9e64;
}

.console-panel-log-details .highlight-boolean {
  color: #f7768e;
}

.console-panel-log-details .highlight-null {
  color: #565f89;
  font-style: italic;
}

/* ===== Warp 风格滚动条和交互优化 ===== */

/* 现代化滚动条样式 */
.console-panel-content::-webkit-scrollbar {
  width: 6px;
}

.console-panel-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.console-panel-content::-webkit-scrollbar-thumb {
  background: #6b6b6b;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.console-panel-content::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

/* 日志条目选择效果 */
.console-panel-log-entry.selected {
  background: rgba(122, 162, 247, 0.15);
  border-left-color: #7aa2f7;
  border-left-width: 3px;
}

/* 复制提示动画 */
@keyframes copyFeedback {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.console-panel-copy-feedback {
  position: absolute;
  right: var(--console-spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  background: var(--console-bg-tertiary);
  color: var(--console-text-primary);
  padding: var(--console-spacing-xs) var(--console-spacing-md);
  border-radius: 3px;
  font-size: var(--console-font-size-tiny);
  font-family: var(--console-font-family);
  animation: copyFeedback 1.5s ease-out;
  pointer-events: none;
  z-index: 10;
}

/* 行号显示（可选功能） */
.console-panel-log-line-number {
  font-size: var(--console-font-size-tiny);
  color: var(--console-text-muted);
  font-family: var(--console-font-family);
  min-width: 30px;
  text-align: right;
  padding-right: var(--console-spacing-md);
  user-select: none;
  opacity: 0.6;
}

.console-panel-log-entry:hover .console-panel-log-line-number {
  opacity: 1;
}

/* ===== 深色主题兼容性和响应式优化 ===== */

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  :root {
    /* 深色主题下的颜色调整 */
    --console-bg-primary: #0d1117;
    --console-bg-secondary: #161b22;
    --console-bg-tertiary: #21262d;
    --console-text-primary: #f0f6fc;
    --console-text-secondary: #8b949e;
    --console-text-muted: #6e7681;
    --console-border-color: #30363d;
    --console-hover-bg: rgba(240, 246, 252, 0.08);
  }

  /* 深色主题下的滚动条优化 */
  .console-panel-content::-webkit-scrollbar-track {
    background: transparent;
  }

  /* 深色主题下的级别标签优化 */
  .console-panel-log-level-info {
    color: #388bfd;
  }

  .console-panel-log-level-success {
    color: #2ea043;
  }

  .console-panel-log-level-warning {
    color: #d29922;
  }

  .console-panel-log-level-error {
    color: #f85149;
  }

  .console-panel-log-level-send {
    color: #c084fc;
  }

  .console-panel-log-level-get {
    color: #22d3ee;
  }
}

/* 平板端优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  /* 平板端网格布局调整 */
  .console-panel-log-content {
    grid-template-columns: 55px 70px 1fr;
    gap: var(--console-spacing-sm);
  }
}

/* 移动端和小屏幕优化 */
@media (max-width: 768px) {
  :root {
    /* 移动端字体大小调整 */
    --console-font-size-base: 11px;
    --console-font-size-small: 9px;
    --console-font-size-tiny: 8px;

    /* 移动端间距调整 */
    --console-spacing-xs: 1px;
    --console-spacing-sm: 3px;
    --console-spacing-md: 4px;
    --console-spacing-lg: 6px;
    --console-spacing-xl: 8px;
  }

  /* 控台面板在小屏幕上的优化 */
  .console-panel {
    box-shadow:
      0 -2px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.03);
    border-top-width: 1px;
  }

  /* 拖拽分隔条在移动端的优化 */
  .console-panel-resize-handle {
    height: 14px;
    top: -7px;
  }

  /* 标题栏在移动端的优化 */
  .console-panel-header {
    min-height: 32px;
    padding: 0 var(--console-spacing-lg);
  }

  /* 三列布局在移动端的调整 */
  .console-panel-log-content {
    grid-template-columns: 50px 60px 1fr;
    gap: var(--console-spacing-sm);
  }

  /* 悬停效果在移动端简化 */
  .console-panel-log-entry:hover {
    background: var(--console-hover-bg);
    margin: 0 -var(--console-spacing-md);
    padding: var(--console-spacing-sm) var(--console-spacing-md);
  }

  /* 操作按钮在移动端的优化 */
  .console-panel-action {
    padding: var(--console-spacing-sm) !important;
  }

  .console-panel-action svg {
    width: 14px;
    height: 14px;
  }

  /* 悬浮窗在移动端的优化 */
  .console-panel.floating {
    border-radius: 6px;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.15);
  }
}
