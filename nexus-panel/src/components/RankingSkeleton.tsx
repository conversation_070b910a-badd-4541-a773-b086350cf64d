// React 核心依赖导入
// Adobe React Spectrum UI 组件库导入
import { View, Grid, Flex, Text, ActionButton } from "@adobe/react-spectrum";
// 导入图标
import Refresh from '@spectrum-icons/workflow/Refresh';
import Alert from '@spectrum-icons/workflow/Alert';
// 导入进度指示器组件
import { RankingProgressIndicator } from "./RankingProgressIndicator";

/**
 * RankingSkeleton 组件的 Props 接口 - Next.js 风格优化
 */
export interface RankingSkeletonProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
	/** 显示的选手行数，默认为 6 */
	rowCount?: number;
	/** 排名数据获取进度信息 */
	progress?: import("../services/api/types").RankingProgress | null;
	/** 错误信息 */
	error?: import("../services/api/types").ApiError | null;
	/** 重试回调 */
	onRetry?: () => void;
	/** 取消回调 */
	onCancel?: () => void;
}

/**
 * RankingSkeleton - 总分排名页面的骨架屏加载状态组件 (Next.js 风格优化)
 *
 * 功能特性：
 * - A1 区域：排行榜标题栏占位符（顶部全宽）
 * - B1-B6 区域：排名序号占位符（6行，左侧列）
 * - C1-C6 区域：选手名称占位符（6行，中间列）
 * - D1-D6 区域：得分明细占位符（6行，右侧列）
 * - 支持动态行数显示（通过 rowCount 属性控制）
 * - 三列网格布局：排名序号 + 选手名称 + 得分明细
 * - Next.js 风格设计：柔和灰色调色板、统一8px圆角、2s平滑动画
 * - 支持交互效果、状态指示、淡入淡出过渡
 * - 深色主题适配和无障碍访问优化
 * - 性能优化：硬件加速、减少重绘
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function RankingSkeleton({
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "loading",
	fadeEffect = null,
	rowCount = 6,
	progress = null,
	error = null,
	onRetry,
	onCancel,
}: RankingSkeletonProps) {
	// 构建CSS类名
	const cssClasses = [
		"ranking-skeleton",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "loading" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	// 如果有进度信息，显示进度指示器
	if (progress && status === "loading") {
		return (
			<RankingProgressIndicator
				progress={progress}
				onCancel={onCancel}
				height={height}
				className={className}
			/>
		);
	}

	// 如果有错误信息，显示错误状态
	if (error && status === "error") {
		return (
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				padding="size-400"
				height={height}
				UNSAFE_className={cssClasses}
			>
				{/* 错误标题 */}
				<Flex alignItems="center" justifyContent="center" marginBottom="size-300">
					<Alert UNSAFE_style={{ marginRight: '8px', color: 'var(--spectrum-global-color-red-600)' }} />
					<Text UNSAFE_style={{
						fontSize: '18px',
						fontWeight: 'bold',
						color: 'var(--spectrum-global-color-red-600)'
					}}>
						数据获取失败
					</Text>
				</Flex>

				{/* 错误信息 */}
				<View marginBottom="size-400">
					<Text UNSAFE_style={{
						fontSize: '14px',
						color: 'var(--spectrum-global-color-gray-700)',
						textAlign: 'center'
					}}>
						{error.message || '获取排名数据时发生错误，请稍后重试'}
					</Text>
				</View>

				{/* 重试按钮 */}
				{onRetry && (
					<Flex justifyContent="center">
						<ActionButton
							onPress={onRetry}
							UNSAFE_style={{
								backgroundColor: 'var(--spectrum-global-color-blue-600)',
								color: 'white',
								borderRadius: '4px',
								padding: '8px 16px'
							}}
						>
							<Refresh />
							<Text>重试</Text>
						</ActionButton>
					</Flex>
				)}
			</View>
		);
	}

	// 生成指定数量的排名行
	const renderRankingRows = () => {
		const rows = [];
		for (let i = 1; i <= rowCount; i++) {
			rows.push(
				<Grid
					key={`ranking-row-${i}`}
					areas={["rank name score"]}
					columns={["size-500", "2fr", "3fr"]}
					gap="size-200"
					marginBottom="size-200"
				>
					{/* B区域：排名序号占位符 */}
					<View gridArea="rank">
						<View
							backgroundColor="gray-300"
							borderRadius="small"
							width="100%"
							height="size-500"
							UNSAFE_className="skeleton-item"
						/>
					</View>

					{/* C区域：选手名称占位符 */}
					<View gridArea="name">
						<View
							backgroundColor="gray-300"
							borderRadius="small"
							width="100%"
							height="size-500"
							UNSAFE_className="skeleton-item"
						/>
					</View>

					{/* D区域：得分明细占位符 */}
					<View gridArea="score">
						<View
							backgroundColor="gray-300"
							borderRadius="small"
							width="100%"
							height="size-500"
							UNSAFE_className="skeleton-item"
						/>
					</View>
				</Grid>,
			);
		}
		return rows;
	};

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* A1 区域：排行榜标题栏占位符（顶部全宽） */}
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="100%"
				height="size-700"
				marginBottom="size-400"
				UNSAFE_className="skeleton-item"
			/>

			{/* 排名列表区域 */}
			<View>{renderRankingRows()}</View>
		</View>
	);
}

export default RankingSkeleton;
