// React 核心依赖导入
import { useMemo } from "react";
// Adobe React Spectrum UI 组件库导入
import { Flex, ActionButton } from "@adobe/react-spectrum";
// 导入组件专用样式
import "./DeviceStatus.css";
// 导入设备管理相关类型
import type { DeviceState, DeviceMode } from "../hooks/useDeviceManager";
import { DeviceConnectionStatus, DeviceSubmissionStatus, DeviceFaultStatus } from "../hooks/useDeviceManager";

/**
 * DeviceStatus 组件的 Props 接口
 */
export interface DeviceStatusProps {
	/** 设备数量，默认为 0（不显示设备） */
	count?: number;
	/** Badge 变体样式，默认为 'positive' */
	variant?: "positive" | "negative" | "neutral" | "info";
	/** 自定义 CSS 类名 */
	className?: string;
	/** Flex 容器的间距，默认为 'size-100' */
	gap?: string;
	/** 设备状态数据，如果提供则忽略 count 参数 */
	devices?: DeviceState[];
	/** 点击设备时的回调函数 */
	onDeviceClick?: (deviceId: number) => void;
	/** 是否显示设备标签而不是数字 */
	showLabels?: boolean;
	/** 设备标签前缀，默认为空 */
	labelPrefix?: string;
	/** 当前设备模式 */
	mode?: DeviceMode;
	/** 获取设备颜色的函数 */
	getDeviceColor?: (device: DeviceState) => string;
	/** 获取设备描边颜色的函数 */
	getDeviceBorderColor?: (device: DeviceState) => string | null;
}

/**
 * DeviceStatus - 设备状态显示组件
 *
 * 功能特性：
 * - 支持自定义设备数量
 * - 支持不同的状态样式
 * - 支持点击交互
 * - 支持自定义标签
 * - 性能优化的渲染
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function DeviceStatus({
	count = 0,
	className,
	gap = "size-100",
	devices,
	onDeviceClick,
	showLabels = false,
	labelPrefix = "",
	mode = "global",
	getDeviceColor,
	getDeviceBorderColor,
}: DeviceStatusProps) {
	// 使用 useMemo 优化设备列表的计算
	const deviceList = useMemo(() => {
		if (devices) {
			return devices;
		}

		// 如果没有提供设备数据，创建默认设备列表
		return Array.from({ length: count }, (_, index) => ({
			id: index + 1,
			connection: DeviceConnectionStatus.ONLINE,
			submission: DeviceSubmissionStatus.NOT_SUBMITTED,
			fault: DeviceFaultStatus.NORMAL,
			selected: false,
		}));
	}, [count, devices]);

	// 处理设备点击事件
	const handleDeviceClick = (deviceId: number) => {
		if (onDeviceClick) {
			onDeviceClick(deviceId);
		}
	};

	// 获取设备的CSS类名（使用useMemo优化）
	const getDeviceClassName = useMemo(() => (device: DeviceState) => {
		const baseClass = "custom-badge";
		const classes = [baseClass];

		// 根据模式和状态添加颜色类
		if (mode === 'targeted') {
			classes.push(device.selected ? 'device-selected' : 'device-unselected');
		} else {
			classes.push(device.connection === 'online' ? 'device-online' : 'device-offline');
		}

		// 添加描边类
		if (device.fault === 'fault') {
			classes.push('device-border-fault');
		} else if (device.submission === 'submitted') {
			classes.push('device-border-submitted');
		}

		return classes.join(' ');
	}, [mode]);

	// 获取设备的内联样式（使用useMemo优化）
	const getDeviceStyle = useMemo(() => (device: DeviceState) => {
		const style: React.CSSProperties = {
			cursor: onDeviceClick ? "pointer" : "default",
		};

		// 如果提供了自定义颜色函数，使用自定义颜色
		if (getDeviceColor) {
			try {
				style.backgroundColor = getDeviceColor(device);
			} catch (error) {
				console.warn('获取设备颜色失败:', error);
				// 使用默认颜色
				style.backgroundColor = '#6C757D';
			}
		}

		// 如果提供了自定义描边颜色函数，使用自定义描边
		if (getDeviceBorderColor) {
			try {
				const borderColor = getDeviceBorderColor(device);
				if (borderColor) {
					style.border = `2px solid ${borderColor}`;
				}
			} catch (error) {
				console.warn('获取设备描边颜色失败:', error);
			}
		}

		return style;
	}, [onDeviceClick, getDeviceColor, getDeviceBorderColor]);

	return (
		<Flex gap={gap} wrap UNSAFE_className={className}>
			{deviceList.map((device) => {
				const badgeElement = (
					<div
						className={getDeviceClassName(device)}
						style={getDeviceStyle(device)}
					>
						{showLabels ? `${labelPrefix}设备 ${device.id}` : device.id}
					</div>
				);

				// 如果提供了点击处理器，用 ActionButton 包装 Badge
				if (onDeviceClick) {
					return (
						<ActionButton
							key={device.id}
							isQuiet
							onPress={() => handleDeviceClick(device.id)}
							UNSAFE_style={{
							}}
							UNSAFE_className="device-action-button"
						>
							{badgeElement}
						</ActionButton>
					);
				}

				// 否则直接返回 Badge
				return (
					<div key={device.id}>
						{badgeElement}
					</div>
				);
			})}
		</Flex>
	);
}

export default DeviceStatus;
