/**
 * 侧边栏按钮样式系统使用示例
 *
 * 本文件展示如何使用新的按钮样式系统来创建具有不同视觉效果的按钮组。
 * 这个文件仅用于演示目的，实际项目中可以删除。
 */

import React from "react";
import {
	SidebarButtonGroup,
	type ButtonConfig,
	type FormFieldConfig,
} from "./SidebarButtonGroup";
import { ButtonStyleType, suggestButtonStyleType } from "./SidebarButtonStyles";

/**
 * 样式系统使用示例组件
 */
export const SidebarButtonStylesExample: React.FC = () => {
	// 示例1: 使用预定义的样式类型
	const exampleButtons1: ButtonConfig[] = [
		{
			text: "跳转",
			styleType: ButtonStyleType.PRIMARY_ACTION,
			onPress: () => console.log("主要操作: 跳转"),
		},
		{
			text: "上一题",
			styleType: ButtonStyleType.NAVIGATION,
			onPress: () => console.log("导航操作: 上一题"),
		},
		{
			text: "下一题",
			styleType: ButtonStyleType.NAVIGATION,
			onPress: () => console.log("导航操作: 下一题"),
		},
	];

	// 示例2: 内容展示类按钮
	const exampleButtons2: ButtonConfig[] = [
		{
			text: "正确答案",
			styleType: ButtonStyleType.CONTENT_DISPLAY,
			onPress: () => console.log("内容展示: 正确答案"),
		},
		{
			text: "选手答案",
			styleType: ButtonStyleType.CONTENT_DISPLAY,
			onPress: () => console.log("内容展示: 选手答案"),
		},
		{
			text: "答案解析",
			styleType: ButtonStyleType.CONTENT_DISPLAY,
			onPress: () => console.log("内容展示: 答案解析"),
		},
	];

	// 示例3: 数据操作和特殊功能
	const exampleButtons3: ButtonConfig[] = [
		{
			text: "赋分",
			styleType: ButtonStyleType.DATA_OPERATION,
			onPress: () => console.log("数据操作: 赋分"),
		},
		{
			text: "填空放大",
			styleType: ButtonStyleType.SPECIAL_FUNCTION,
			onPress: () => console.log("特殊功能: 填空放大"),
		},
	];

	// 示例4: 危险操作和信息展示
	const exampleButtons4: ButtonConfig[] = [
		{
			text: "重置",
			styleType: ButtonStyleType.DANGER_ACTION,
			onPress: () => console.log("危险操作: 重置"),
		},
		{
			text: "当前状态",
			styleType: ButtonStyleType.INFO_DISPLAY,
			// 注意: INFO_DISPLAY类型的按钮通常不需要onPress事件
		},
	];

	// 示例5: 自动推荐样式类型
	const autoStyledButtons: ButtonConfig[] = [
		"上一题",
		"跳转",
		"下一题",
		"正确答案",
		"赋分",
		"填空放大",
		"删除",
	].map((text) => ({
		text,
		styleType: suggestButtonStyleType(text),
		onPress: () => console.log(`自动样式: ${text}`),
	}));

	// 示例6: 混合使用样式类型和手动variant
	const mixedStyleButtons: ButtonConfig[] = [
		{
			text: "自定义主要",
			styleType: ButtonStyleType.PRIMARY_ACTION,
			variant: "secondary", // 手动指定variant会覆盖styleType的variant
			onPress: () => console.log("混合样式: 自定义主要"),
		},
		{
			text: "传统方式",
			variant: "accent", // 不使用styleType，使用传统方式
			onPress: () => console.log("传统样式: accent"),
		},
	];

	// 表单字段示例
	const formFields: FormFieldConfig[] = [
		{ label: "题号", name: "questionNumber", defaultValue: 1 },
	];

	return (
		<div
			style={{
				padding: "20px",
				display: "flex",
				flexDirection: "column",
				gap: "20px",
			}}
		>
			<h2>侧边栏按钮样式系统示例</h2>

			{/* 示例1: 主要操作和导航 */}
			<SidebarButtonGroup
				title="示例1: 主要操作和导航"
				tooltipContent="展示主要操作和导航按钮的样式"
				buttons={exampleButtons1}
			/>

			{/* 示例2: 内容展示 */}
			<SidebarButtonGroup
				title="示例2: 内容展示"
				tooltipContent="展示内容展示类按钮的样式"
				buttons={exampleButtons2}
			/>

			{/* 示例3: 数据操作和特殊功能 */}
			<SidebarButtonGroup
				title="示例3: 数据操作和特殊功能"
				tooltipContent="展示数据操作和特殊功能按钮的样式"
				buttons={exampleButtons3}
			/>

			{/* 示例4: 危险操作和信息展示 */}
			<SidebarButtonGroup
				title="示例4: 危险操作和信息展示"
				tooltipContent="展示危险操作和信息展示按钮的样式"
				buttons={exampleButtons4}
			/>

			{/* 示例5: 自动推荐样式 */}
			<SidebarButtonGroup
				title="示例5: 自动推荐样式"
				tooltipContent="根据按钮文本自动推荐合适的样式类型"
				buttons={autoStyledButtons}
			/>

			{/* 示例6: 混合使用 */}
			<SidebarButtonGroup
				title="示例6: 混合使用"
				tooltipContent="展示样式类型和手动variant的混合使用"
				buttons={mixedStyleButtons}
			/>

			{/* 示例7: 完整功能展示 */}
			<SidebarButtonGroup
				title="示例7: 完整功能展示"
				tooltipContent="展示表单字段、多种按钮类型和渲染顺序"
				formFields={formFields}
				buttons={[
					{ text: "跳转", styleType: ButtonStyleType.PRIMARY_ACTION },
					{ text: "上一题", styleType: ButtonStyleType.NAVIGATION },
					{ text: "下一题", styleType: ButtonStyleType.NAVIGATION },
				]}
				additionalButtons={[
					{ text: "赋分", styleType: ButtonStyleType.DATA_OPERATION },
					{ text: "重置", styleType: ButtonStyleType.DANGER_ACTION },
				]}
				renderOrder={["formFields", "buttons", "additionalButtons"]}
				showBreakBeforeButtons={true}
				showBreakBeforeAdditionalButtons={true}
			/>
		</div>
	);
};

export default SidebarButtonStylesExample;
