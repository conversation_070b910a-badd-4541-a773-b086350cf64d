// React 核心依赖导入
import React, { useState, useEffect, useMemo, useCallback } from "react";
// Adobe React Spectrum UI 组件库导入
import {
	View,
	Flex,
	ActionButton,
	Picker,
	Item,
	Divider,
	Avatar,
	DialogContainer,
	AlertDialog,
} from "@adobe/react-spectrum";
// 导入类型定义
import type { Key } from "@adobe/react-spectrum";
// 导入图标
import Refresh from "@spectrum-icons/workflow/Refresh";
import Bell from "@spectrum-icons/workflow/Bell";
import DeviceRotateLandscape from "@spectrum-icons/workflow/DeviceRotateLandscape";
// 导入组件
import { ContentToggle } from "../ContentToggle";
import { DeviceStatus } from "../DeviceStatus";
// 导入 LOGO SVG 资源
import logoSvg from "../../assets/logo.svg";
// 导入样式
import "./HeaderSection.css";
// 导入Hooks
import { useRaceApi } from "../../hooks/useRaceApi";
import { useDeviceManager } from "../../hooks/useDeviceManager.ts";

/**
 * 常用样式配置常量
 */
const HEADER_STYLES = {
	centerFlex: {
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		padding: "0 20px",
	} as React.CSSProperties,
	flexEndAlign: {
		display: "flex",
		alignItems: "center",
		justifyContent: "flex-end",
		padding: "0 20px",
	} as React.CSSProperties,
} as const;

/**
 * HeaderSection 组件 Props 接口
 */
export interface HeaderSectionProps {
	/** 内容类型 */
	contentType: string;
	/** 内容类型变化处理器 */
	onContentTypeChange: (type: string) => void;
	/** 刷新按钮点击处理器 */
	onRefresh: () => void;
	/** 赛事 API Hook 返回值 */
	raceApi: ReturnType<typeof useRaceApi>;
	/** 当前选中的项目 */
	selectedProject: string | null;
	/** 项目选择变化处理器 */
	onProjectSelectionChange: (key: Key | null) => void;
}

/**
 * HeaderSection 组件
 * 
 * 负责渲染应用程序的头部区域，包括：
 * - Logo 和内容切换器
 * - 用户信息和通知
 * - 工具栏（刷新按钮、赛事选择器、设备状态）
 * 
 * @param props - 组件属性
 * @returns HeaderSection JSX 元素
 */
export const HeaderSection: React.FC<HeaderSectionProps> = ({
	contentType,
	onContentTypeChange,
	onRefresh,
	raceApi,
	selectedProject,
	onProjectSelectionChange,
}) => {
	// 单设备刷新确认对话框状态
	const [deviceRefreshDialog, setDeviceRefreshDialog] = useState<{
		isOpen: boolean;
		deviceId: number;
	}>({
		isOpen: false,
		deviceId: 0,
	});

	// 全部设备刷新确认对话框状态
	const [allDevicesRefreshDialog, setAllDevicesRefreshDialog] = useState(false);

	// 获取当前选中赛事的参赛人数
	const currentRace = useMemo(() => {
		if (!selectedProject || !raceApi.races) {
			return null;
		}
		return raceApi.races.find(race => race.id === selectedProject);
	}, [selectedProject, raceApi.races]);

	// 设备数量（基于当前赛事的参赛人数，没有选中赛事时为0）
	const deviceCount = currentRace?.peopleCount || 0;

	// 稳定的日志回调函数
	const onLog = useCallback((level: 'info' | 'warning' | 'error', message: string, details?: unknown) => {
		// 这里可以集成到全局日志系统
		// console.log(`[设备管理] ${level}: ${message}`, details);
	}, []);

	// 使用设备管理Hook
	const deviceManager = useDeviceManager({
		deviceCount,
		mode: contentType as 'global' | 'targeted',
		onLog,
	});

	// 稳定化handleModeChange函数引用以避免无限循环
	const stableHandleModeChange = useMemo(() => deviceManager.handleModeChange, [deviceManager.handleModeChange]);

	// 监听ContentToggle模式变化
	useEffect(() => {
		stableHandleModeChange(contentType as 'global' | 'targeted');
	}, [contentType, stableHandleModeChange]);

	// 处理设备点击事件
	const handleDeviceClick = (deviceId: number) => {
		if (contentType === 'targeted') {
			// 定向指令模式：切换设备选中状态
			deviceManager.toggleDeviceSelection(deviceId);
		} else {
			// 全局指令模式：显示单设备刷新确认对话框
			setDeviceRefreshDialog({
				isOpen: true,
				deviceId,
			});
		}
	};

	// 处理项目选择变化的包装函数
	const handleProjectSelectionChangeWithDebug = useCallback((key: Key | null) => {
		// 调用传入的处理函数 - 调试日志已移除
		onProjectSelectionChange(key);
	}, [onProjectSelectionChange]);

	// 处理单设备刷新确认
	const handleDeviceRefreshConfirm = () => {
		// TODO: 实现设备刷新逻辑
		console.log(`刷新设备 ${deviceRefreshDialog.deviceId}`);
		setDeviceRefreshDialog({ isOpen: false, deviceId: 0 });
	};

	// 处理单设备刷新取消
	const handleDeviceRefreshCancel = () => {
		setDeviceRefreshDialog({ isOpen: false, deviceId: 0 });
	};

	// 处理全部设备刷新按钮点击
	const handleAllDevicesRefreshClick = () => {
		setAllDevicesRefreshDialog(true);
	};

	// 处理全部设备刷新确认
	const handleAllDevicesRefreshConfirm = () => {
		// TODO: 实现全部设备刷新逻辑
		console.log('刷新全部设备');
		setAllDevicesRefreshDialog(false);
	};

	// 处理全部设备刷新取消
	const handleAllDevicesRefreshCancel = () => {
		setAllDevicesRefreshDialog(false);
	};
	return (
		<View
			backgroundColor="gray-50"
			gridArea="header"
			height="size-1500"
		>
			{/* 头部顶栏容器 */}
			<View backgroundColor="gray-100" height="size-700">
				{/* 头部左右两端布局 */}
				<Flex justifyContent="space-between" height="100%">
					{/* 左侧功能区块 (如 Logo 或菜单) */}
					<View
						width="size-1500"
						height="100%"
						UNSAFE_style={HEADER_STYLES.centerFlex}
					>
						<img
							src={logoSvg}
							alt="Nexus Panel Logo"
							style={{
								maxWidth: "100%",
								maxHeight: "50%",
								objectFit: "contain",
								filter:
									"brightness(0) saturate(100%) invert(69%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(86%)",
							}}
						/>
						<Divider
							orientation="vertical"
							size="S"
							UNSAFE_style={{
								height: "50%",
								backgroundColor: "#000",
								marginLeft: "30px",
								alignSelf: "center",
							}}
						/>
						<ContentToggle
							selectedType={contentType}
							onChange={onContentTypeChange}
						/>
					</View>
					{/* 右侧功能区块 (如用户信息或设置) */}
					<View
						width="size-800"
						height="100%"
						UNSAFE_style={HEADER_STYLES.flexEndAlign}
					>
						<Flex alignItems="center" gap="size-150">
							{/* 通知按钮 */}
							<ActionButton isQuiet>
								<Bell />
							</ActionButton>

							{/* 用户头像 */}
							<ActionButton isQuiet>
								<Avatar
									src="https://i.imgur.com/kJOwAdv.png"
									alt="default Adobe avatar"
								/>
							</ActionButton>
						</Flex>
					</View>
				</Flex>
			</View>
			{/* 头部工具栏区域 */}
			<View
				backgroundColor="gray-100"
				height="size-600"
				marginTop="size-10"
				UNSAFE_style={{
					...HEADER_STYLES.centerFlex,
					padding: "0 16px",
				}}
			>
				<Flex
					justifyContent="space-between"
					alignItems="center"
					width="100%"
					height="100%"
				>
					{/* 左侧内容 */}
					<Flex alignItems="center" gap="size-150">
						{/* 刷新按钮 */}
						<ActionButton isQuiet onPress={onRefresh}>
							<Refresh />
						</ActionButton>

						{/* 赛事选择下拉框 */}
						<Picker
							placeholder={
								raceApi.loading
									? "正在加载赛事..."
									: raceApi.error
										? "获取赛事失败"
										: raceApi.hasData
											? "请选择赛事"
											: "暂无可用赛事"
							}
							aria-label="选择赛事"
							selectedKey={selectedProject}
							onSelectionChange={handleProjectSelectionChangeWithDebug}
							UNSAFE_className="custom-picker"
							isDisabled={raceApi.loading || raceApi.error !== null}
						>
							{raceApi.races.map((race) => (
								<Item key={race.id}>{race.name}</Item>
							))}
						</Picker>
					</Flex>

					{/* 右侧内容 */}
					<Flex alignItems="center" gap="size-150">
						{/* 设备状态区域 - 只有当设备数量大于0时才显示 */}
						{deviceCount > 0 && (
							<DeviceStatus
								devices={deviceManager.devices}
								mode={contentType as 'global' | 'targeted'}
								gap="size-50"
								onDeviceClick={handleDeviceClick}
								getDeviceColor={deviceManager.getDeviceColor}
								getDeviceBorderColor={deviceManager.getDeviceBorderColor}
							/>
						)}
						<ActionButton isQuiet onPress={handleAllDevicesRefreshClick}>
							<DeviceRotateLandscape />
						</ActionButton>
					</Flex>
				</Flex>
			</View>

			{/* 单设备刷新确认对话框 */}
			<DialogContainer onDismiss={handleDeviceRefreshCancel}>
				{deviceRefreshDialog.isOpen && (
					<AlertDialog
						title="单台设备刷新确认"
						variant="confirmation"
						primaryActionLabel="确认"
						secondaryActionLabel="取消"
						onPrimaryAction={handleDeviceRefreshConfirm}
						onSecondaryAction={handleDeviceRefreshCancel}
					>
						即将刷新选中设备：设备{deviceRefreshDialog.deviceId}
					</AlertDialog>
				)}
			</DialogContainer>

			{/* 全部设备刷新确认对话框 */}
			<DialogContainer onDismiss={handleAllDevicesRefreshCancel}>
				{allDevicesRefreshDialog && (
					<AlertDialog
						title="全部设备刷新确认"
						variant="confirmation"
						primaryActionLabel="确认"
						secondaryActionLabel="取消"
						onPrimaryAction={handleAllDevicesRefreshConfirm}
						onSecondaryAction={handleAllDevicesRefreshCancel}
					>
						即将刷新全部设备
					</AlertDialog>
				)}
			</DialogContainer>
		</View>
	);
};
