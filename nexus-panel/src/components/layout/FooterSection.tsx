// React 核心依赖导入
import React from "react";
// Adobe React Spectrum UI 组件库导入
import {
	View,
	Flex,
	StatusLight,
	ActionButton,
} from "@adobe/react-spectrum";
// 导入图标
import Info from "@spectrum-icons/workflow/Info";
// 导入 Hooks
import type { useMQTTIntegration } from "../../hooks/useMQTTIntegration";
import type { useRaceApi } from "../../hooks/useRaceApi";
// 导入样式
import "./FooterSection.css";

/**
 * 常用样式配置常量
 */
const FOOTER_STYLES = {
	centerFlex: {
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		padding: "0 20px",
	} as React.CSSProperties,
} as const;

/**
 * FooterSection 组件 Props 接口
 */
export interface FooterSectionProps {
	/** MQTT 集成 Hook 返回值 */
	mqttIntegration: ReturnType<typeof useMQTTIntegration>;
	/** 赛事 API Hook 返回值 */
	raceApi: ReturnType<typeof useRaceApi>;
	/** 应用版本号 */
	version?: string;
	/** 是否显示更新日志按钮 */
	showUpdateLog?: boolean;
}

/**
 * 获取状态指示器的变体类型
 * @param isConnected 连接状态
 * @param hasError 错误状态
 * @returns 状态指示器变体
 */
const getStatusVariant = (isConnected: boolean, hasError: boolean): "positive" | "negative" | "notice" => {
	if (hasError) return "negative";
	if (isConnected) return "positive";
	return "notice";
};

/**
 * FooterSection 组件
 * 
 * 负责渲染应用程序的底部区域，包括：
 * - 网络状态指示器（MQTT、API、导航数据）
 * - 版本信息显示
 * - 更新日志按钮
 * - 响应式布局
 * 
 * @param props - 组件属性
 * @returns FooterSection JSX 元素
 */
export const FooterSection: React.FC<FooterSectionProps> = ({
	mqttIntegration,
	raceApi,
	version = "1.0.0",
	showUpdateLog = true,
}) => {
	return (
		<View
			backgroundColor="gray-100"
			gridArea="footer"
			height="size-500"
			UNSAFE_style={{
				...FOOTER_STYLES.centerFlex,
				padding: "0 16px",
			}}
		>
			{/* Footer 内容布局 */}
			<Flex
				justifyContent="space-between"
				alignItems="center"
				width="100%"
				height="100%"
			>
				{/* 左侧：网络状态指示器 */}
				<Flex alignItems="center" gap="size-200">
					{/* MQTT 连接状态 */}
					<StatusLight
						variant={getStatusVariant(
							mqttIntegration.isConnected,
							mqttIntegration.error !== null
						)}
					>
						MQTT: {mqttIntegration.isConnected ? "已连接" : "未连接"}
					</StatusLight>

					{/* API 连接状态 */}
					<StatusLight
						variant={getStatusVariant(
							raceApi.hasData,
							raceApi.error !== null
						)}
					>
						API: {raceApi.loading ? "加载中" : raceApi.hasData ? "已连接" : "未连接"}
					</StatusLight>

					{/* 赛事导航数据状态 */}
					<StatusLight
						variant={getStatusVariant(
							raceApi.navigationData !== null,
							raceApi.error !== null
						)}
					>
						赛事: {raceApi.navigationData ? "已加载" : "未加载"}
					</StatusLight>
				</Flex>

				{/* 右侧：版本信息和操作按钮 */}
				<Flex alignItems="center" gap="size-150">
					{/* 版本信息 */}
					<View>
						<span style={{ fontSize: "12px", color: "#6b7280" }}>
							Nexus Panel v{version}
						</span>
					</View>

					{/* 更新日志按钮 */}
					{showUpdateLog && (
						<ActionButton
							isQuiet
							onPress={() => {
								// 更新日志点击处理逻辑
								// TODO: 实现更新日志显示功能
							}}
						>
							<Info />
						</ActionButton>
					)}
				</Flex>
			</Flex>
		</View>
	);
};
