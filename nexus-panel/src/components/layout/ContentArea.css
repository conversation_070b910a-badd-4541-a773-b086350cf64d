/* ContentArea 组件样式 */

/* 内容区域特定样式 */
.content-area {
  /* 如果需要特定的内容区域样式，可以在这里添加 */
}

/* 面包屑导航样式 */
.content-area .breadcrumbs {
  /* 面包屑特定样式 */
}

/* 音频播放器容器样式 */
.content-area .audio-player-container {
  /* 音频播放器容器样式 */
}

/* 骨架屏容器样式 */
.content-area .skeleton-container {
  /* 骨架屏容器样式 */
}

/* 空状态图标样式 */
.content-area .empty-state-icon {
  transition: opacity 0.3s ease-in-out;
}

.content-area .empty-state-icon:hover {
  opacity: 0.5;
}

/* ===== 内容区域滚动条样式 ===== */

/* 内容区域滚动容器样式 - 与控台面板保持一致 */
.content-area-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.content-area-scroll-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.content-area-scroll-container::-webkit-scrollbar-thumb {
  background: #6b6b6b;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content-area-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .content-area-scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .content-area-scroll-container::-webkit-scrollbar-thumb {
    background: #6b6b6b;
  }

  .content-area-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #939393;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-area {
    /* 移动端内容区域样式调整 */
  }

  .content-area .breadcrumbs-audio-container {
    flex-direction: column;
    gap: 12px;
  }

  /* 移动端滚动条优化 */
  .content-area-scroll-container::-webkit-scrollbar {
    width: 4px;
  }

  .content-area-scroll-container::-webkit-scrollbar-thumb {
    border-radius: 2px;
  }

  /* 悬浮控台容器在移动端的优化 */
  .floating-console-container {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
  }
}

/* 中等屏幕适配 (平板等) */
@media (max-width: 1024px) and (min-width: 769px) {
  .floating-console-container {
    left: 212px !important; /* 保持与导航栏对齐 */
    right: 20px !important;
  }
}

/* 大屏幕适配 (桌面等) - 考虑右侧目录栏 */
@media (min-width: 1025px) {
  .floating-console-container {
    left: 212px !important; /* 导航栏宽度 + 间距 */
    right: calc(25% + 60px) !important; /* 目录栏宽度(1fr ≈ 25%) + 间距 */
  }
}
