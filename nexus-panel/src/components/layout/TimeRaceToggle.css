/* TimeRaceToggle.css - 争分夺秒环节切换按钮样式 */

.time-race-toggle {
  display: flex;
  border: 1px solid #000000;
  border-radius: 24px;
  overflow: hidden;
  background: #1a191c;
  position: relative;
}

.time-race-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: calc(50% - 2px);
  height: calc(100% - 4px);
  background: #363538;
  border-radius: 20px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.time-race-toggle[data-selected-key='question']::before {
  transform: translateX(100%);
}

.time-race-toggle .react-aria-ToggleButton {
  padding: 8px 20px;
  border: none;
  background: transparent;
  color: #b0b0b0;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
  min-width: 80px;
  text-align: center;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.time-race-toggle .react-aria-ToggleButton:hover {
  color: #ffffff;
}

.time-race-toggle .react-aria-ToggleButton[data-selected] {
  color: #ffffff;
}

/* 悬停状态 */
.time-race-toggle:hover {
  border-color: #6a6a6a;
}
