// React 核心依赖导入
import React, { useState, useEffect } from "react";
// Adobe React Spectrum UI 组件库导入
import {
	View,
	Flex,
	Breadcrumbs,
	Item,
} from "@adobe/react-spectrum";
// 导入 React Aria Components
import { ToggleButtonGroup, ToggleButton } from "react-aria-components";
// 导入图标
import ViewList from "@spectrum-icons/workflow/ViewList";
import FileTxt from "@spectrum-icons/workflow/FileTxt";
// 导入组件
import { QuestionSkeleton } from "../QuestionSkeleton";
import { RuleIntroSkeleton } from "../RuleIntroSkeleton";
// import { UltimatePKSkeleton } from "../UltimatePKSkeleton"; // 已替换为UltimatePKContent
import { RankingSkeleton } from "../RankingSkeleton";
import { RankingContent } from "../RankingContent";
import { TimeRaceRankingContent } from "../TimeRaceRankingContent";
import { HomePageSkeleton } from "../HomePageSkeleton";
import { HomePageContent } from "../HomePageContent";
import { RuleIntroContent } from "../RuleIntroContent";
import { QuestionContent } from "../QuestionContent";
import { AudioPlayer } from "../AudioPlayer";
import UltimatePKContent from "../UltimatePKContent";
import SafetyPublicClassSkeleton from "../SafetyPublicClassSkeleton";
import SafetyPublicClassContent from "../SafetyPublicClassContent";
// 导入配置
import type { ComponentConfig, SkeletonComponentType } from "../../config";
// 导入类型
import type { GroupedConfigurationData, ProcessedConfigurationItem, ProcessedRulesIntroductionItem, ProcessedQuestionItem, RankingData, RankingProgress, ApiError } from "../../services/api/types";
// 导入 LOGO SVG 资源
import iconSvg from "../../assets/icon.svg";
// 导入样式
import "./ContentArea.css";
// 导入切换按钮样式
import "./TimeRaceToggle.css";

/**
 * 常用样式配置常量
 */
const CONTENT_STYLES = {
	centerFlex: {
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		padding: "0 20px",
	} as React.CSSProperties,
	flexEnd: {
		display: "flex",
		justifyContent: "flex-end",
	} as React.CSSProperties,
	flexColumn: {
		display: "flex",
		flexDirection: "column",
	} as React.CSSProperties,
} as const;

/**
 * ContentArea 组件 Props 接口
 */
export interface ContentAreaProps {
	/** 当前选中的导航键 */
	selectedNavigationKey: string | null;
	/** 组件配置 */
	componentConfig: ComponentConfig;
	/** 面包屑导航项目 */
	breadcrumbItems: Array<{ id: string; label: string }>;
	/** 配置数据（首页专用） */
	configurationData?: GroupedConfigurationData | null;
	/** 当前选中的配置项（首页专用） */
	selectedConfigurationItems?: {
		leader: ProcessedConfigurationItem | null;
		player: ProcessedConfigurationItem | null;
		award: ProcessedConfigurationItem | null;
	};
	/** 当前显示类型（首页专用） */
	currentDisplayType?: 'leader' | 'player' | 'award' | null;
	/** 规则介绍数据（规则页面专用） */
	rulesIntroductionData?: ProcessedRulesIntroductionItem[] | null;
	/** 当前选中的规则介绍项（规则页面专用） */
	selectedRulesIntroductionItem?: ProcessedRulesIntroductionItem | null;
	/** 规则介绍数据加载状态（规则页面专用） */
	rulesIntroductionLoading?: boolean;
	/** 规则介绍当前页码（规则页面专用） */
	rulesCurrentPage?: number;
	/** 规则介绍页面切换回调（规则页面专用） */
	onRulesPageChange?: (page: number) => void;
	/** 题目数据（题目页面专用） */
	questionData?: ProcessedQuestionItem[] | null;
	/** 当前题目（题目页面专用） */
	currentQuestion?: ProcessedQuestionItem | null;
	/** 当前题目序号（题目页面专用） */
	currentQuestionNumber?: number;
	/** 当前阶段状态（题目页面专用） */
	currentStage?: string;
	/** 初始阶段（题目页面专用） */
	initialStage?: string;
	/** 是否显示答案（题目页面专用） */
	showAnswer?: boolean;
	/** 是否显示解析（题目页面专用） */
	showExplanation?: boolean;
	/** 排名数据（排名页面专用） */
	rankingData?: RankingData | null;
	/** 排名数据加载状态（排名页面专用） */
	rankingLoading?: boolean;
	/** 排名数据错误信息（排名页面专用） */
	rankingError?: ApiError | null;
	/** 排名数据获取进度（排名页面专用） */
	rankingProgress?: RankingProgress | null;
	/** 排名数据刷新回调（排名页面专用） */
	onRankingRefresh?: () => void;
	/** 环节排名数据（争分夺秒/同分加赛专用） */
	sectionRankingData?: RankingData | null;
	/** 环节排名数据加载状态（争分夺秒/同分加赛专用） */
	sectionRankingLoading?: boolean;
	/** 环节排名数据错误信息（争分夺秒/同分加赛专用） */
	sectionRankingError?: ApiError | null;
	/** 图片加载错误回调 */
	onImageError?: (error: string) => void;
	/** 日志记录回调 */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
	/** 内容类型（用于检测争分夺秒等特殊环节） */
	contentType?: string;
	/** 节点名称（用于检测争分夺秒等特殊环节） */
	nodeName?: string;
	/** 选中的项目ID（用于获取正确的baseId） */
	selectedProject?: string | null;
	/** 当前题包ID（争分夺秒/同分加赛专用） */
	currentPackage?: string | null;
	/** 安全公开课状态（安全公开课专用） */
	safetyPublicClassState?: {
		displayType: 'program' | 'player' | null;
		selectedItemId: string | null;
	};
}

/**
 * 根据骨架屏类型渲染对应的组件
 * @param skeletonType 骨架屏类型
 * @param configurationData 配置数据（首页专用）
 * @param selectedConfigurationItems 当前选中的配置项（首页专用）
 * @param currentDisplayType 当前显示类型（首页专用）
 * @param selectedRulesIntroductionItem 当前选中的规则介绍项（规则页面专用）
 * @param questionData 题目数据（题目页面专用）
 * @param currentQuestion 当前题目（题目页面专用）
 * @param currentQuestionNumber 当前题目序号（题目页面专用）
 * @param currentStage 当前阶段状态（题目页面专用）
 * @param initialStage 初始阶段（题目页面专用）
 * @param showAnswer 是否显示答案（题目页面专用）
 * @param showExplanation 是否显示解析（题目页面专用）
 * @param rankingData 排名数据（排名页面专用）
 * @param rankingLoading 排名数据加载状态（排名页面专用）
 * @param rankingError 排名数据错误信息（排名页面专用）
 * @param rankingProgress 排名数据获取进度（排名页面专用）
 * @param onRankingRefresh 排名数据刷新回调（排名页面专用）
 * @param onImageError 图片加载错误回调
 * @param onLog 日志记录回调
 * @returns JSX元素
 */
const getDynamicSkeletonComponent = (
	skeletonType: SkeletonComponentType,
	selectedNavigationKey?: string,
	configurationData?: GroupedConfigurationData | null,
	selectedConfigurationItems?: {
		leader: ProcessedConfigurationItem | null;
		player: ProcessedConfigurationItem | null;
		award: ProcessedConfigurationItem | null;
	},
	currentDisplayType?: 'leader' | 'player' | 'award' | null,
	selectedRulesIntroductionItem?: ProcessedRulesIntroductionItem | null,
	rulesIntroductionLoading?: boolean,
	rulesCurrentPage?: number,
	onRulesPageChange?: (page: number) => void,
	questionData?: ProcessedQuestionItem[] | null,
	currentQuestion?: ProcessedQuestionItem | null,
	currentQuestionNumber?: number,
	currentStage?: string,
	initialStage?: string,
	showAnswer?: boolean,
	showExplanation?: boolean,
	rankingData?: RankingData | null,
	rankingLoading?: boolean,
	rankingError?: ApiError | null,
	rankingProgress?: RankingProgress | null,
	onRankingRefresh?: () => void,
	onImageError?: (error: string) => void,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void,
	contentType?: string,
	nodeName?: string,
	selectedProject?: string | null,
	currentPackage?: string | null,
	sectionRankingData?: RankingData | null,
	sectionRankingLoading?: boolean,
	sectionRankingError?: ApiError | null,
	safetyPublicClassState?: {
		displayType: 'program' | 'player' | null;
		selectedItemId: string | null;
	},
	timeRaceDisplayMode?: 'ranking' | 'question'
) => {
	// 检测是否为争分夺秒或同分加赛环节（在函数开头声明，避免重复）
	const isTimeRaceSection = Boolean(
		(contentType && contentType.includes('快答')) ||
		(nodeName && (nodeName.includes('争分夺秒') || nodeName.includes('同分加赛')))
	);

	switch (skeletonType) {
		case "HomePageSkeleton":
			// 如果有配置数据，使用动态内容组件
			if (configurationData && selectedConfigurationItems) {
				// 确定当前选中的配置项
				let selectedItem: ProcessedConfigurationItem | null = null;
				if (currentDisplayType) {
					selectedItem = selectedConfigurationItems[currentDisplayType];
				}

				return (
					<HomePageContent
						displayType={currentDisplayType || null}
						selectedItem={selectedItem}
						onImageError={onImageError}
						onLog={onLog}
					/>
				);
			}
			// 否则使用静态骨架屏
			return <HomePageSkeleton animated={true} height="auto" />;
		case "RuleIntroSkeleton":
			// 如果有规则介绍数据且选中了特定项，使用动态内容组件
			if (selectedRulesIntroductionItem && !rulesIntroductionLoading) {
				return (
					<RuleIntroContent
						rulesItem={selectedRulesIntroductionItem}
						currentPage={rulesCurrentPage}
						onPageChange={onRulesPageChange}
						onLog={onLog}
					/>
				);
			}
			// 如果正在加载或没有选中项，使用静态骨架屏
			return <RuleIntroSkeleton
				animated={true}
				height="auto"
				status={rulesIntroductionLoading ? "loading" : "loading"}
			/>;
		case "UltimatePKSkeleton":
			return <UltimatePKContent animated={true} height="auto" onLog={onLog} />;
		case "RankingSkeleton":
			// 如果有排名数据且不在加载中且无错误，显示RankingContent
			if (rankingData && !rankingLoading && !rankingError) {
				return (
					<RankingContent
						rankingData={rankingData}
						onRefresh={onRankingRefresh}
						onLog={onLog}
					/>
				);
			}
			// 如果正在加载，显示带进度的骨架屏
			if (rankingLoading && rankingProgress) {
				return (
					<RankingSkeleton
						animated={true}
						height="auto"
						rowCount={6}
						status="loading"
						progress={rankingProgress}
					/>
				);
			}
			// 如果有错误，显示错误状态的骨架屏
			if (rankingError) {
				return (
					<RankingSkeleton
						animated={true}
						height="auto"
						rowCount={6}
						status="error"
						error={rankingError}
						onRetry={onRankingRefresh}
					/>
				);
			}
			// 默认显示普通骨架屏
			return <RankingSkeleton animated={true} height="auto" rowCount={6} />;
		case "SafetyPublicClassSkeleton":
			// 安全公开课环节 - 如果有配置数据和状态，使用动态内容组件
			if (configurationData && safetyPublicClassState) {
				return (
					<SafetyPublicClassContent
						displayType={safetyPublicClassState.displayType}
						selectedItemId={safetyPublicClassState.selectedItemId}
						configurationData={configurationData}
						onImageError={onImageError}
						onLog={onLog}
						animated={true}
						height="auto"
					/>
				);
			}
			// 否则使用静态骨架屏
			const safetyTitle = nodeName || "安全公开课";
			return <SafetyPublicClassSkeleton title={safetyTitle} animated={true} />;
		case "QuestionSkeleton":
		default:
			// 如果有题目数据且当前题目序号有效，使用题目内容组件
			if (questionData && questionData.length > 0 && currentQuestionNumber !== undefined) {
				// 如果是争分夺秒环节，实现题目内容和排名区域的互斥显示
				if (isTimeRaceSection) {
					// 获取当前节点名称，用于确定环节类型
					const sectionName = nodeName?.includes('争分夺秒') ? '争分夺秒' : '同分加赛';

					return (
						<View>
							{/* 题目内容 - 根据显示模式控制可见性，但始终挂载以保持状态 */}
							<View UNSAFE_style={{ display: timeRaceDisplayMode === 'question' ? 'block' : 'none' }}>
								<QuestionContent
									currentQuestion={currentQuestion || null}
									currentQuestionNumber={currentQuestionNumber}
									questionData={questionData}
									currentStage={currentStage}
									initialStage={initialStage}
									showAnswer={showAnswer || false}
									showExplanation={showExplanation || false}
									animated={true}
									height="auto"
								/>
							</View>

							{/* 排名区域 - 根据显示模式控制可见性，但始终挂载以保持数据流 */}
							{selectedProject && (
								<View UNSAFE_style={{ display: timeRaceDisplayMode === 'ranking' ? 'block' : 'none' }}>
									<TimeRaceRankingContent
										sectionName={sectionName as '争分夺秒' | '同分加赛'}
										rankingData={sectionRankingData || null}
										loading={sectionRankingLoading || false}
										error={sectionRankingError || null}
										isPolling={false} // 轮询状态由外部管理
										lastUpdateTime={Date.now()}
										onRefresh={() => {
											// 刷新逻辑由外部处理
											onLog?.('info', `手动刷新${sectionName}排名数据`, {
												sectionName,
												timestamp: Date.now(),
												action: 'manual_refresh_from_content_area'
											});
										}}
										onLog={onLog}
										height="auto"
										animated={true}
									/>
								</View>
							)}
						</View>
					);
				} else {
					// 非争分夺秒环节，只显示题目内容
					return (
						<QuestionContent
							currentQuestion={currentQuestion || null}
							currentQuestionNumber={currentQuestionNumber}
							questionData={questionData}
							currentStage={currentStage}
							initialStage={initialStage}
							showAnswer={showAnswer || false}
							showExplanation={showExplanation || false}
							animated={true}
							height="auto"
						/>
					);
				}
			}

			// 否则使用静态骨架屏，如果是争分夺秒环节则显示排名区域
			return (
				<QuestionSkeleton
					animated={true}
					height="auto"
					showRankingArea={isTimeRaceSection}
					rankingRowCount={8}
				/>
			);
	}
};

/**
 * ContentArea 组件
 * 
 * 负责渲染应用程序的主内容区域，包括：
 * - 面包屑导航
 * - 音频播放器
 * - 动态骨架屏组件
 * - 空状态显示
 * 
 * @param props - 组件属性
 * @returns ContentArea JSX 元素
 */
export const ContentArea: React.FC<ContentAreaProps> = ({
	selectedNavigationKey,
	componentConfig,
	breadcrumbItems,
	configurationData,
	selectedConfigurationItems,
	currentDisplayType,
	selectedRulesIntroductionItem,
	rulesIntroductionLoading,
	rulesCurrentPage,
	onRulesPageChange,
	questionData,
	currentQuestion,
	currentQuestionNumber,
	currentStage,
	initialStage,
	showAnswer,
	showExplanation,
	rankingData,
	rankingLoading,
	rankingError,
	rankingProgress,
	onRankingRefresh,
	sectionRankingData,
	sectionRankingLoading,
	sectionRankingError,
	onImageError,
	onLog,
	contentType,
	nodeName,
	selectedProject,
	currentPackage,
	safetyPublicClassState,
}) => {
	// 争分夺秒和同分加赛环节的显示模式状态管理
	const [timeRaceDisplayMode, setTimeRaceDisplayMode] = useState<'ranking' | 'question'>('ranking');

	// 监听导航变化，重置显示模式为默认值（排行榜）
	useEffect(() => {
		setTimeRaceDisplayMode('ranking');
	}, [selectedNavigationKey]);

	// 动态计算音频源
	const audioSrc = React.useMemo(() => {
		// 如果是规则介绍页面且有选中的规则项
		if (componentConfig.skeleton === "RuleIntroSkeleton" && selectedRulesIntroductionItem) {
			if (selectedRulesIntroductionItem.audioUrls && selectedRulesIntroductionItem.audioUrls.length > 0) {
				return selectedRulesIntroductionItem.audioUrls[0];
			}
		}
		// 如果是题目页面且有当前题目
		if (componentConfig.skeleton === "QuestionSkeleton" && currentQuestion) {
			if (currentQuestion.primaryAudioUrl) {
				return currentQuestion.primaryAudioUrl;
			}
		}
		// 其他页面使用默认音频源
		return "https://ohvfx.com/test1.mp3";
	}, [componentConfig.skeleton, selectedRulesIntroductionItem, currentQuestion]);

	// 判断是否应该显示音频播放器
	const shouldShowAudioPlayer = React.useMemo(() => {
		if (!componentConfig.showAudioPlayer) return false;

		// 如果是规则介绍页面，只有在有音频源时才显示
		if (componentConfig.skeleton === "RuleIntroSkeleton") {
			return selectedRulesIntroductionItem &&
				selectedRulesIntroductionItem.audioUrls &&
				selectedRulesIntroductionItem.audioUrls.length > 0;
		}

		// 如果是题目页面，只有在有音频源时才显示
		if (componentConfig.skeleton === "QuestionSkeleton") {
			return currentQuestion && currentQuestion.primaryAudioUrl;
		}

		// 其他页面按原有逻辑显示
		return true;
	}, [componentConfig.showAudioPlayer, componentConfig.skeleton, selectedRulesIntroductionItem, currentQuestion]);

	// 检测是否为争分夺秒或同分加赛环节
	const isTimeRaceSection = Boolean(
		(contentType && contentType.includes('快答')) ||
		(nodeName && (nodeName.includes('争分夺秒') || nodeName.includes('同分加赛')))
	);
	return (
		<View
			flex="1"
			UNSAFE_style={{
				...CONTENT_STYLES.flexColumn,
				height: "100%",
				overflow: "auto",
				paddingBottom: "60px", // 底部预留空间，避免被悬浮控台面板遮挡
			}}
		>
			{selectedNavigationKey ? (
				/* 有选择导航时的内容区域 - 带 padding */
				<View
					flex="1"
					padding="size-200"
					UNSAFE_style={{
						overflow: "auto",
						minHeight: 0, // 允许 flex 子元素收缩
					}}
				>
					{/* 面包屑导航和音频播放器 - 在同一行显示，使用动态配置控制显示 */}
					{(componentConfig.showBreadcrumbs && breadcrumbItems.length > 0) || componentConfig.showAudioPlayer ? (
						<Flex
							alignItems="center"
							justifyContent="start"
							marginBottom="size-200"
						>
							{/* 左侧：面包屑导航 - 根据动态配置显示 */}
							{componentConfig.showBreadcrumbs && breadcrumbItems.length > 0 && (
								<View flex={componentConfig.showAudioPlayer ? "1" : "auto"}>
									<Breadcrumbs>
										{breadcrumbItems.map((item) => (
											<Item key={item.id}>{item.label}</Item>
										))}
									</Breadcrumbs>
								</View>
							)}

							{/* 右侧：音频播放器 - 根据动态配置和内容显示 */}
							{shouldShowAudioPlayer && (
								<View flex="2" UNSAFE_style={CONTENT_STYLES.flexEnd}>
									<AudioPlayer
										audioSrc={audioSrc}
										autoPlay={componentConfig.skeleton === "RuleIntroSkeleton" || componentConfig.skeleton === "QuestionSkeleton"}
										onPlay={() => {
											onLog?.('info', '音频开始播放', {
												audioSrc,
												selectedNavigationKey,
												isRulesIntroduction: componentConfig.skeleton === "RuleIntroSkeleton",
												ruleTitle: selectedRulesIntroductionItem?.title,
												timestamp: Date.now(),
												action: 'audio_play'
											});
										}}
										onPause={() => {
											onLog?.('info', '音频暂停播放', {
												audioSrc,
												selectedNavigationKey,
												timestamp: Date.now(),
												action: 'audio_pause'
											});
										}}
										onEnded={() => {
											onLog?.('info', '音频播放完成', {
												audioSrc,
												selectedNavigationKey,
												timestamp: Date.now(),
												action: 'audio_ended'
											});
										}}
										onAutoPlayFailed={(error) => {
											onLog?.('warning', '音频自动播放失败', {
												audioSrc,
												selectedNavigationKey,
												error: error.message,
												timestamp: Date.now(),
												action: 'audio_autoplay_failed'
											});
										}}
										onTimeUpdate={() => {
											/* 播放进度更新 */
										}}
									/>
								</View>
							)}

							{/* 争分夺秒环节切换按钮 - 只在相关环节显示 */}
							{isTimeRaceSection && questionData && questionData.length > 0 && (
								<View UNSAFE_style={{ marginLeft: '12px' }}>
									<ToggleButtonGroup
										className="time-race-toggle"
										data-selected-key={timeRaceDisplayMode}
										selectionMode="single"
										selectedKeys={new Set([timeRaceDisplayMode])}
										onSelectionChange={(keys) => {
											if (typeof keys !== "string" && keys.size > 0) {
												const newMode = Array.from(keys)[0] as 'ranking' | 'question';
												setTimeRaceDisplayMode(newMode);
												onLog?.('info', '争分夺秒环节显示模式切换', {
													from: timeRaceDisplayMode,
													to: newMode,
													timestamp: Date.now()
												});
											}
										}}
									>
										<ToggleButton id="ranking">
											<ViewList size="S" />
											排行榜
										</ToggleButton>
										<ToggleButton id="question">
											<FileTxt size="S" />
											题目
										</ToggleButton>
									</ToggleButtonGroup>
								</View>
							)}
						</Flex>
					) : null}

					{/* 题目显示区域 */}
					<View>
						{/* 根据动态绑定显示不同的骨架屏组件 */}
						{getDynamicSkeletonComponent(
							componentConfig.skeleton,
							selectedNavigationKey,
							configurationData,
							selectedConfigurationItems,
							currentDisplayType,
							selectedRulesIntroductionItem,
							rulesIntroductionLoading,
							rulesCurrentPage,
							onRulesPageChange,
							questionData,
							currentQuestion,
							currentQuestionNumber,
							currentStage,
							initialStage,
							showAnswer,
							showExplanation,
							rankingData,
							rankingLoading,
							rankingError,
							rankingProgress,
							onRankingRefresh,
							onImageError,
							onLog,
							contentType,
							nodeName,
							selectedProject,
							currentPackage,
							sectionRankingData,
							sectionRankingLoading,
							sectionRankingError,
							safetyPublicClassState,
							timeRaceDisplayMode
						)}
					</View>
				</View>
			) : (
				/* 未选择导航时显示居中图标 - 无 padding，完全居中 */
				<View
					flex="1"
					UNSAFE_style={{
						...CONTENT_STYLES.centerFlex,
						minHeight: 0, // 允许 flex 子元素收缩
						padding: 0, // 覆盖 centerFlex 的 padding
					}}
				>
					<img
						src={iconSvg}
						alt="Nexus Panel Icon"
						style={{
							width: "120px",
							height: "80px",
							objectFit: "contain",
							opacity: 0.3,
							filter:
								"brightness(0) saturate(100%) invert(69%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(86%)",
						}}
					/>
				</View>
			)}
		</View>
	);
};
