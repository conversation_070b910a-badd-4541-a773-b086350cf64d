/* FooterSection 组件样式 */

/* 底部区域容器 */
.footer-section {
  /* 如果需要特定的底部区域样式，可以在这里添加 */
  color: rgb(235, 235, 235) !important;
}

/* 状态指示器组样式 */
.footer-section .status-indicators {
  /* 状态指示器组样式 */
}

/* 版本信息样式 */
.footer-section .version-info {
  /* 版本信息样式 */
}

/* 更新日志按钮样式 */
.footer-section .update-log-button {
  /* 更新日志按钮样式 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-section {
    /* 移动端底部区域样式调整 */
  }

  .footer-section .status-indicators {
    flex-direction: column;
    gap: 8px;
  }

  .footer-section .version-info {
    font-size: 10px;
  }
}
