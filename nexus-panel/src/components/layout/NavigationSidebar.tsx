// React 核心依赖导入
import React from "react";
// Adobe React Spectrum UI 组件库导入
import { View } from "@adobe/react-spectrum";
// 导入组件
import { NavigationTreeView } from "../NavigationTreeView";
// 导入类型
import type { NavigationNode } from "../../services/api";
// 导入样式
import "./NavigationSidebar.css";

/**
 * 常用样式配置常量
 */
const NAVIGATION_STYLES = {
	flexColumn: {
		display: "flex",
		flexDirection: "column",
	} as React.CSSProperties,
} as const;

/**
 * NavigationSidebar 组件 Props 接口
 */
export interface NavigationSidebarProps {
	/** 动态导航数据 */
	navigationData: NavigationNode[] | null;
	/** 导航数据加载状态 */
	loading: boolean;
	/** 导航选择变化处理器 */
	onSelectionChange: (key: string | null) => void;
	/** 无障碍标签 */
	ariaLabel?: string;
	/** 配置数据是否就绪 */
	configurationDataReady?: boolean;
}

/**
 * NavigationSidebar 组件
 * 
 * 负责渲染侧边导航栏区域，包括：
 * - NavigationTreeView 包装和样式
 * - 导航区域布局管理
 * - 加载状态处理
 * - 响应式设计支持
 * 
 * @param props - 组件属性
 * @returns NavigationSidebar JSX 元素
 */
export const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
	navigationData,
	loading,
	onSelectionChange,
	ariaLabel = "赛事导航菜单",
	configurationDataReady = false,
}) => {
	return (
		<View
			backgroundColor="gray-100"
			gridArea="nav"
			padding="size-150"
			UNSAFE_style={{
				overflow: "hidden",
				...NAVIGATION_STYLES.flexColumn,
			}}
		>
			{/* 导航菜单 - 使用 NavigationTreeView 组件 */}
			<NavigationTreeView
				ariaLabel={ariaLabel}
				onSelectionChange={onSelectionChange}
				navigationData={navigationData}
				loading={loading}
				configurationDataReady={configurationDataReady}
			/>
		</View>
	);
};
