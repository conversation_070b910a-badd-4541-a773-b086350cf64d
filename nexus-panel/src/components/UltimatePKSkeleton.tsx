// React 核心依赖导入
// Adobe React Spectrum UI 组件库导入
import { View, Flex } from "@adobe/react-spectrum";

/**
 * UltimatePKSkeleton 组件的 Props 接口 - Next.js 风格优化
 */
export interface UltimatePKSkeletonProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
}

/**
 * UltimatePKSkeleton - 终极PK页面的骨架屏加载状态组件 (Next.js 风格优化)
 *
 * 功能特性：
 * - A1 区域：当前阶段标题占位符（居中显示）
 * - C1 区域：正方倒计时显示占位符（左侧）
 * - C2 区域：反方倒计时显示占位符（右侧）
 * - B1 区域：正方名称占位符（左侧）
 * - B2 区域：反方名称占位符（右侧）
 * - Next.js 风格设计：柔和灰色调色板、统一8px圆角、2s平滑动画
 * - 支持交互效果、状态指示、淡入淡出过渡
 * - 深色主题适配和无障碍访问优化
 * - 性能优化：硬件加速、减少重绘
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function UltimatePKSkeleton({
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "loading",
	fadeEffect = null,
}: UltimatePKSkeletonProps) {
	// 构建CSS类名
	const cssClasses = [
		"ultimate-pk-skeleton",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "loading" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* A1 区域：当前阶段标题占位符（居中显示） */}
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="100%"
				height="size-800"
				marginBottom="size-400"
				UNSAFE_className="skeleton-item"
				UNSAFE_style={{
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			/>

			{/* C1 & C2 区域：正方和反方倒计时显示占位符（中间左右对称布局） */}
			<Flex
				justifyContent="space-between"
				alignItems="center"
				marginBottom="size-300"
				gap="size-300"
			>
				{/* C1 区域：正方倒计时显示占位符（左侧） */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="45%"
					height="size-2000"
					UNSAFE_className="skeleton-item"
				/>

				{/* C2 区域：反方倒计时显示占位符（右侧） */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="45%"
					height="size-2000"
					UNSAFE_className="skeleton-item"
				/>
			</Flex>

			{/* B1 & B2 区域：正方和反方名称占位符（底部左右对称布局） */}
			<Flex justifyContent="space-between" alignItems="center" gap="size-300">
				{/* B1 区域：正方名称占位符（左侧） */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="45%"
					height="size-600"
					UNSAFE_className="skeleton-item"
				/>

				{/* B2 区域：反方名称占位符（右侧） */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="45%"
					height="size-600"
					UNSAFE_className="skeleton-item"
				/>
			</Flex>
		</View>
	);
}

export default UltimatePKSkeleton;
