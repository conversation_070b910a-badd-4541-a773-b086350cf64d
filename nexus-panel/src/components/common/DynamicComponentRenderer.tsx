// React 核心依赖导入
import React, { useMemo } from "react";
// 导入配置模块
import {
	getComponentConfig,
	getButtonGroupsByType,
	getButtonGroupsByTypeWithContext,
	findNavigationNode,
	type StaticNavigationNode,
	navigationNodes,
	type ComponentConfig,
	type ButtonGroupConfig,
} from "../../config";
// 导入类型
import type { NavigationNode } from "../../services/api";
// 导入样式
import "./DynamicComponentRenderer.css";

/**
 * DynamicComponentRenderer 组件 Props 接口
 */
export interface DynamicComponentRendererProps {
	/** 当前选中的导航键 */
	selectedNavigationKey: string | null;
	/** 动态导航数据 */
	navigationData: NavigationNode[] | null;
	/** 日志记录回调函数（可选） */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
	/** 事件处理器（可选） */
	handlers?: {
		onPackageChange?: (packageId: string, packageName: string) => void;
		onPackageChangeConfirmed?: (packageId: string, packageName: string) => void;
		onQuestionPrevious?: () => void;
		onQuestionJump?: (formState: Record<string, string | number>) => void;
		onQuestionNext?: () => void;
		currentQuestionNumber?: number;
		totalQuestions?: number;
		onScoreAssignment?: () => void;
		onPageFirst?: () => void;
		onPageSecond?: () => void;
		// 分页信息
		paginationInfo?: {
			hasMultiplePages: boolean;
			currentPage: number;
			totalPages: number;
		};
		// 安全公开课相关数据
		configurationData?: any;
		playerInfoData?: any[];
	};
	/** 渲染函数 - 接收计算后的配置数据 */
	children: (config: {
		componentConfig: ComponentConfig;
		buttonGroups: ButtonGroupConfig[];
		breadcrumbItems: Array<{ id: string; label: string }>;
	}) => React.ReactNode;
}

/**
 * DynamicComponentRenderer 组件
 * 
 * 负责统一管理动态组件渲染逻辑，包括：
 * - 组件配置计算和缓存
 * - 按钮组配置获取
 * - 面包屑路径生成
 * - 渲染策略优化
 * 
 * @param props - 组件属性
 * @returns DynamicComponentRenderer JSX 元素
 */
export const DynamicComponentRenderer: React.FC<DynamicComponentRendererProps> = ({
	selectedNavigationKey,
	navigationData,
	onLog,
	handlers,
	children,
}) => {
	// 注意：计时器清理逻辑已移至按钮组配置函数中，确保正确的初始化顺序

	// 缓存组件配置以优化性能
	const componentConfig = useMemo(() => {
		return getComponentConfig(selectedNavigationKey, navigationData);
	}, [selectedNavigationKey, navigationData]);

	// 获取当前节点的内容类型和名称
	const { currentContentType, currentNodeName, originalContentType } = useMemo(() => {
		if (!selectedNavigationKey || !navigationData) return {
			currentContentType: undefined,
			currentNodeName: undefined,
			originalContentType: undefined
		};
		const currentNode = findNavigationNode(navigationData, selectedNavigationKey);
		return {
			currentContentType: currentNode?.contentType,
			currentNodeName: currentNode?.name,
			originalContentType: currentNode?.contentType // 保留原始内容类型用于按钮组配置
		};
	}, [selectedNavigationKey, navigationData]);

	// 根据动态绑定获取侧边栏按钮组配置（支持内容类型、节点名称和日志）
	const buttonGroups = useMemo(() => {
		// 使用原始内容类型进行按钮组配置，这样可以正确解析时长
		return getButtonGroupsByTypeWithContext(componentConfig.buttonGroups, originalContentType, currentNodeName, onLog, handlers);
	}, [componentConfig.buttonGroups, currentContentType, originalContentType, currentNodeName, onLog, handlers, selectedNavigationKey]);

	// 生成面包屑路径（支持动态导航数据）
	const breadcrumbItems = useMemo(() => {
		if (!selectedNavigationKey) return [];

		// 优先使用动态导航数据
		if (navigationData && navigationData.length > 0) {
			const items: { id: string; label: string }[] = [];

			// 在动态导航数据中查找当前节点
			const currentNode = findNavigationNode(navigationData, selectedNavigationKey);
			if (currentNode) {
				items.push({ id: currentNode.id, label: currentNode.name });

				// 如果有父节点ID，查找父节点
				if (currentNode.parentId) {
					const parentNode = findNavigationNode(navigationData, currentNode.parentId);
					if (parentNode) {
						items.unshift({ id: parentNode.id, label: parentNode.name });
					}
				}
			}

			return items;
		}

		// 降级到静态导航数据
		const items: StaticNavigationNode[] = [];
		let currentKey: string | null = selectedNavigationKey;

		// 从当前节点向上遍历到根节点
		while (currentKey && navigationNodes[currentKey]) {
			const node: StaticNavigationNode = navigationNodes[currentKey];
			items.unshift(node); // 添加到数组开头
			currentKey = node.parentId || null;
		}

		return items;
	}, [selectedNavigationKey, navigationData]);

	// 调用渲染函数，传递计算后的配置数据
	return (
		<>
			{children({
				componentConfig,
				buttonGroups,
				breadcrumbItems,
			})}
		</>
	);
};
