/**
 * 智能图片加载组件
 * 
 * 支持重试机制的图片组件，失败时自动重试并最终显示错误图标
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { View, ProgressCircle } from '@adobe/react-spectrum';
import AlertIcon from '@spectrum-icons/workflow/Alert';

// ==================== 类型定义 ====================

/**
 * 图片加载状态
 */
export type ImageLoadState = 'loading' | 'loaded' | 'error' | 'retrying';

/**
 * ImageWithRetry 组件 Props 接口
 */
export interface ImageWithRetryProps {
	/** 图片URL */
	src: string;
	/** 替代文本 */
	alt?: string;
	/** 自定义CSS类名 */
	className?: string;
	/** 自定义样式 */
	style?: React.CSSProperties;
	/** 最大重试次数，默认为2 */
	maxRetries?: number;
	/** 重试间隔（毫秒），默认为[1000, 2000] */
	retryDelays?: number[];
	/** 加载成功回调 */
	onLoad?: () => void;
	/** 加载失败回调 */
	onError?: (error: string) => void;
	/** 重试回调 */
	onRetry?: (attempt: number) => void;
	/** 最终失败回调 */
	onFinalError?: () => void;
	/** 图片宽度 */
	width?: string | number;
	/** 图片高度 */
	height?: string | number;
	/** 是否显示加载状态 */
	showLoadingState?: boolean;
	/** 是否显示错误图标 */
	showErrorIcon?: boolean;
	/** 错误图标大小 */
	errorIconSize?: 'S' | 'M' | 'L' | 'XL';
}

// ==================== 常量定义 ====================

/**
 * 默认重试延迟配置（指数退避）
 */
const DEFAULT_RETRY_DELAYS = [1000, 2000]; // 1秒，2秒

/**
 * 默认最大重试次数
 */
const DEFAULT_MAX_RETRIES = 2;

// ==================== 主要组件 ====================

/**
 * ImageWithRetry - 智能图片加载组件 (短期修复版本)
 *
 * 功能特性：
 * - 自动重试机制（指数退避）
 * - 增强型图片加载检测（多重验证）
 * - 渐进式显示策略（opacity过渡）
 * - 图片状态轮询机制（后备方案）
 * - 加载状态管理
 * - 错误处理和图标显示
 * - 内存泄漏防护
 * - 可配置的重试策略
 * - 详细调试日志记录
 *
 * 修复版本: v1.1.0 - 解决图片持续加载状态问题
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const ImageWithRetry: React.FC<ImageWithRetryProps> = ({
	src,
	alt = '图片',
	className,
	style,
	maxRetries = DEFAULT_MAX_RETRIES,
	retryDelays = DEFAULT_RETRY_DELAYS,
	onLoad,
	onError,
	onRetry,
	onFinalError,
	width,
	height,
	showLoadingState = true,
	showErrorIcon = true,
	errorIconSize = 'M',
}) => {
	// 状态管理
	const [loadState, setLoadState] = useState<ImageLoadState>('loading');
	const [retryCount, setRetryCount] = useState(0);
	const [currentSrc, setCurrentSrc] = useState(src);

	// 引用管理
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);
	const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const imgRef = useRef<HTMLImageElement | null>(null);
	const mountedRef = useRef(true);
	const initialSrcRef = useRef<string | null>(null);

	// 确保组件挂载时 mountedRef 为 true
	useEffect(() => {
		mountedRef.current = true;
		console.log('[ImageWithRetry] 组件挂载，初始化状态:', { src, loadState });
	}, [src, loadState]);

	// 增强型图片加载检测函数
	const checkImageLoaded = useCallback((imgElement: HTMLImageElement): boolean => {
		if (!imgElement) {
			console.log('[ImageWithRetry] 检测失败: 图片元素不存在');
			return false;
		}

		// 多重检测机制
		const isComplete = imgElement.complete;
		const hasNaturalDimensions = imgElement.naturalWidth > 0 && imgElement.naturalHeight > 0;
		const hasValidSrc = Boolean(imgElement.src && imgElement.src !== '');

		console.log('[ImageWithRetry] 图片加载状态检测:', {
			src: imgElement.src,
			complete: isComplete,
			naturalWidth: imgElement.naturalWidth,
			naturalHeight: imgElement.naturalHeight,
			hasNaturalDimensions,
			hasValidSrc,
			currentLoadState: loadState
		});

		// 图片被认为已加载的条件：complete为true且有自然尺寸
		return isComplete && hasNaturalDimensions && hasValidSrc;
	}, [loadState]);

	// 清理定时器
	const clearRetryTimeout = useCallback(() => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
	}, []);

	// 清理轮询定时器
	const clearPollTimeout = useCallback(() => {
		if (pollTimeoutRef.current) {
			clearTimeout(pollTimeoutRef.current);
			pollTimeoutRef.current = null;
		}
	}, []);

	// 启动图片状态轮询机制（后备方案）
	const startPolling = useCallback(() => {
		if (!mountedRef.current) return;

		let pollCount = 0;
		const maxPolls = 10; // 最多轮询10次
		const pollInterval = 500; // 每500ms轮询一次

		const poll = () => {
			if (!mountedRef.current || pollCount >= maxPolls) {
				console.log('[ImageWithRetry] 轮询结束:', { pollCount, maxPolls, mounted: mountedRef.current });
				return;
			}

			const imgElement = imgRef.current;
			if (imgElement && checkImageLoaded(imgElement)) {
				console.log('[ImageWithRetry] 轮询检测成功:', { pollCount, src: currentSrc });
				setLoadState('loaded');
				clearPollTimeout();
				onLoad?.();
				return;
			}

			pollCount++;
			console.log('[ImageWithRetry] 轮询检测中:', { pollCount, maxPolls });

			pollTimeoutRef.current = setTimeout(poll, pollInterval);
		};

		// 延迟启动轮询，给onLoad事件一些时间
		pollTimeoutRef.current = setTimeout(poll, pollInterval);
	}, [checkImageLoaded, currentSrc, onLoad, clearPollTimeout]);

	// 重试加载
	const retryLoad = useCallback(() => {
		if (!mountedRef.current || retryCount >= maxRetries) {
			return;
		}

		const delay = retryDelays[retryCount] || retryDelays[retryDelays.length - 1];

		setLoadState('retrying');
		onRetry?.(retryCount + 1);

		timeoutRef.current = setTimeout(() => {
			if (!mountedRef.current) return;

			setRetryCount(prev => prev + 1);
			setLoadState('loading');
			// 添加时间戳防止缓存
			setCurrentSrc(`${src}?retry=${retryCount + 1}&t=${Date.now()}`);
		}, delay);
	}, [src, retryCount, maxRetries, retryDelays, onRetry]);

	// 处理图片加载成功 (增强版本)
	const handleLoad = useCallback(() => {
		if (!mountedRef.current) {
			console.log('[ImageWithRetry] handleLoad: 组件已卸载，跳过处理');
			return;
		}

		const imgElement = imgRef.current;
		if (!imgElement) {
			console.log('[ImageWithRetry] handleLoad: 图片元素不存在');
			return;
		}

		// 使用增强型检测验证图片是否真正加载完成
		const isReallyLoaded = checkImageLoaded(imgElement);

		console.log('[ImageWithRetry] handleLoad 触发:', {
			src: currentSrc,
			isReallyLoaded,
			previousState: loadState
		});

		if (isReallyLoaded) {
			setLoadState('loaded');
			clearRetryTimeout();
			clearPollTimeout();
			onLoad?.();
			console.log('[ImageWithRetry] 图片加载成功确认:', currentSrc);
		} else {
			console.log('[ImageWithRetry] handleLoad: 检测未通过，启动轮询机制');
			startPolling();
		}
	}, [onLoad, clearRetryTimeout, checkImageLoaded, currentSrc, loadState, clearPollTimeout, startPolling]);

	// 处理图片加载失败 (增强版本)
	const handleError = useCallback(() => {
		if (!mountedRef.current) {
			console.log('[ImageWithRetry] handleError: 组件已卸载，跳过处理');
			return;
		}

		// 清理轮询定时器
		clearPollTimeout();

		const errorMessage = `图片加载失败: ${src} (尝试 ${retryCount + 1}/${maxRetries + 1})`;

		console.log('[ImageWithRetry] handleError 触发:', {
			src: currentSrc,
			retryCount,
			maxRetries,
			errorMessage
		});

		if (retryCount < maxRetries) {
			// 还有重试机会
			console.log('[ImageWithRetry] 准备重试:', { currentRetry: retryCount + 1, maxRetries: maxRetries + 1 });
			onError?.(errorMessage);
			retryLoad();
		} else {
			// 最终失败
			console.log('[ImageWithRetry] 最终失败，显示错误状态:', currentSrc);
			setLoadState('error');
			clearRetryTimeout();
			onError?.(errorMessage);
			onFinalError?.();
		}
	}, [src, retryCount, maxRetries, onError, onFinalError, retryLoad, clearRetryTimeout, clearPollTimeout, currentSrc]);

	// src变化时重置状态 (增强版本)
	useEffect(() => {
		if (src !== currentSrc.split('?')[0]) {
			console.log('[ImageWithRetry] 图片源变化，重置状态:', { oldSrc: currentSrc, newSrc: src });
			setLoadState('loading');
			setRetryCount(0);
			setCurrentSrc(src);
			clearRetryTimeout();
			clearPollTimeout();
		}
	}, [src, currentSrc, clearRetryTimeout, clearPollTimeout]);

	// 组件重新挂载时强制重置状态（解决回到主屏再显示的问题）
	useEffect(() => {
		// 只在组件首次挂载或 src 真正变化时重置状态
		if (src && src !== initialSrcRef.current) {
			console.log('[ImageWithRetry] 检测到新的图片源，重置加载状态:', { oldSrc: initialSrcRef.current, newSrc: src });
			initialSrcRef.current = src;
			setLoadState('loading');
			setRetryCount(0);
			clearRetryTimeout();
			clearPollTimeout();
		}
	}, [src, clearRetryTimeout, clearPollTimeout]);

	// 监听图片元素变化，立即检查加载状态
	useEffect(() => {
		const imgElement = imgRef.current;
		if (!imgElement || loadState === 'loaded' || loadState === 'error') {
			return;
		}

		// 立即检查图片是否已经加载完成（解决缓存图片onLoad不触发的问题）
		const checkImmediately = () => {
			if (checkImageLoaded(imgElement)) {
				console.log('[ImageWithRetry] 立即检测发现图片已加载:', currentSrc);
				setLoadState('loaded');
				onLoad?.();
				return true;
			}
			return false;
		};

		// 使用requestAnimationFrame确保DOM更新完成后再检查
		const rafId = requestAnimationFrame(() => {
			if (!checkImmediately()) {
				// 如果立即检查未成功，启动后备轮询机制
				const delayedPollTimeout = setTimeout(() => {
					if (mountedRef.current && loadState === 'loading') {
						console.log('[ImageWithRetry] 启动后备轮询机制:', currentSrc);
						startPolling();
					}
				}, 1000); // 1秒后启动轮询

				return () => {
					clearTimeout(delayedPollTimeout);
				};
			}
		});

		return () => {
			cancelAnimationFrame(rafId);
		};
	}, [currentSrc, loadState, startPolling, checkImageLoaded, onLoad]);

	// 组件卸载时清理 (增强版本)
	useEffect(() => {
		return () => {
			console.log('[ImageWithRetry] 组件卸载，清理资源:', currentSrc);
			mountedRef.current = false;
			clearRetryTimeout();
			clearPollTimeout();
		};
	}, [clearRetryTimeout, clearPollTimeout, currentSrc]);

	// 渲染加载状态 (优化版本)
	const renderLoadingState = () => {
		if (!showLoadingState) return null;

		// 根据加载状态显示不同的透明度
		const isRetrying = loadState === 'retrying';
		const loadingOpacity = loadState === 'loaded' ? 0 : (isRetrying ? 0.8 : 1);

		return (
			<View
				borderRadius="small"
				width={width || "100%"}
				height={height || "auto"}
				UNSAFE_style={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					...style, // 先应用外部样式
					// 然后强制应用居中样式，确保不被覆盖
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					opacity: loadingOpacity,
					transition: 'opacity 0.3s ease-in-out',
					zIndex: 1,
				}}
				UNSAFE_className={`image-loading ${className || ''}`}
			>
				{loadState === 'loading' && (
					<ProgressCircle aria-label="Loading…" isIndeterminate />
				)}
				{loadState === 'retrying' && (
					<View UNSAFE_style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
						<ProgressCircle aria-label="Retrying…" isIndeterminate />
						<div style={{
							fontSize: '12px',
							color: 'var(--spectrum-global-color-gray-600)',
							textAlign: 'center'
						}}>
							重试中... ({retryCount}/{maxRetries})
						</div>
					</View>
				)}
			</View>
		);
	};

	// 渲染错误状态
	const renderErrorState = () => {
		if (!showErrorIcon) return null;

		return (
			<View
				backgroundColor="gray-100"
				borderRadius="small"
				width={width || "100%"}
				height={height || "auto"}
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					minHeight: '100px',
					flexDirection: 'column',
					gap: '8px',
					...style,
				}}
				UNSAFE_className={`image-error ${className || ''}`}
			>
				<AlertIcon size={errorIconSize} />
				<div style={{
					fontSize: '12px',
					color: 'var(--spectrum-global-color-gray-600)',
					textAlign: 'center'
				}}>
					图片加载失败
				</div>
			</View>
		);
	};

	// 渲染图片 (渐进式显示版本)
	const renderImage = () => {
		// 计算图片透明度和变换效果
		const isLoaded = loadState === 'loaded';
		const opacity = isLoaded ? 1 : 0;
		const transform = isLoaded ? 'scale(1)' : 'scale(0.95)';

		return (
			<img
				ref={imgRef}
				src={currentSrc}
				alt={alt}
				className={className}
				style={{
					width: width || "100%",
					height: height || "auto",
					opacity: opacity,
					transform: transform,
					transition: 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out',
					display: 'block', // 始终显示，通过opacity控制可见性
					...style,
				}}
				onLoad={handleLoad}
				onError={handleError}
			/>
		);
	};

	// 主渲染逻辑
	return (
		<div style={{ position: 'relative', width: width || "100%", height: height || "auto" }}>
			{/* 始终渲染图片元素 */}
			{renderImage()}

			{/* 根据状态渲染对应的UI */}
			{(loadState === 'loading' || loadState === 'retrying') && renderLoadingState()}
			{loadState === 'error' && renderErrorState()}
		</div>
	);
};

export default ImageWithRetry;
