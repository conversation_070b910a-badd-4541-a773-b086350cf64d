/* DynamicComponentRenderer 组件样式 */

/* 动态组件渲染器容器 */
.dynamic-component-renderer {
  /* 如果需要特定的渲染器样式，可以在这里添加 */
}

/* 配置缓存优化相关样式 */
.dynamic-component-renderer .config-cache {
  /* 配置缓存相关样式 */
}

/* 渲染性能优化样式 */
.dynamic-component-renderer .render-optimization {
  /* 渲染优化相关样式 */
}

/* ===== 侧边栏滚动条样式 ===== */

/* 侧边栏滚动容器样式 - 与控台面板保持一致 */
.sidebar-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb {
  background: #6b6b6b;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .sidebar-scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .sidebar-scroll-container::-webkit-scrollbar-thumb {
    background: #6b6b6b;
  }

  .sidebar-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #939393;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dynamic-component-renderer {
    /* 移动端动态渲染器样式调整 */
  }

  /* 移动端滚动条优化 */
  .sidebar-scroll-container::-webkit-scrollbar {
    width: 4px;
  }

  .sidebar-scroll-container::-webkit-scrollbar-thumb {
    border-radius: 2px;
  }
}
