/**
 * QuestionContent - 题目内容主组件
 * 
 * 功能特性：
 * - 整合所有题目子组件（Header、Answer、Body、Explanation）
 * - 支持初始状态和题目状态的切换
 * - 集成题目导航逻辑
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局
 */

import React from 'react';
import { View, Flex } from '@adobe/react-spectrum';
// 导入题目子组件
import { QuestionHeader } from './question/QuestionHeader';
import { QuestionAnswer } from './question/QuestionAnswer';
import { QuestionBody } from './question/QuestionBody';
import { QuestionExplanation } from './question/QuestionExplanation';
// 导入类型
import type { ProcessedQuestionItem } from '../services/api/types';

// ==================== 类型定义 ====================

/**
 * QuestionContent组件Props接口
 */
export interface QuestionContentProps {
	/** 当前题目数据（null表示初始状态） */
	currentQuestion: ProcessedQuestionItem | null;
	/** 当前题目序号（0表示初始状态） */
	currentQuestionNumber: number;
	/** 题目数据列表（用于计算总题数） */
	questionData?: ProcessedQuestionItem[] | null;
	/** 当前阶段状态（通过按钮切换后的状态） */
	currentStage?: string;
	/** 初始阶段（从导航节点获取） */
	initialStage?: string;
	/** 是否显示答案 */
	showAnswer?: boolean;
	/** 是否显示解析 */
	showExplanation?: boolean;
	/** 自定义CSS类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'success' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
	/** React Spectrum样式 */
	UNSAFE_style?: React.CSSProperties;
}

// ==================== 组件实现 ====================

/**
 * QuestionContent - 题目内容主组件
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const QuestionContent: React.FC<QuestionContentProps> = ({
	currentQuestion,
	currentQuestionNumber,
	questionData,
	showAnswer = false,
	showExplanation = false,
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "success",
	fadeEffect = null,
	UNSAFE_style,
}) => {
	// 判断是否为初始状态
	const isInitialState = currentQuestionNumber === 0;

	// 计算总题数
	const totalQuestions = questionData?.length || 0;

	// 构建CSS类名
	const cssClasses = [
		"question-content",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "success" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
			UNSAFE_style={UNSAFE_style}
		>
			{/* 顶部区域：A1 和 A2 */}
			<Flex
				justifyContent="space-between"
				alignItems="start"
				marginBottom="size-300"
				wrap="wrap"
				gap="size-200"
			>
				{/* A1 区域：题号、题型、分值、阶段 */}
				<QuestionHeader
					question={currentQuestion}
					isInitialState={isInitialState}
					totalQuestions={totalQuestions}
				/>

				{/* A2 区域：正确答案 */}
				<QuestionAnswer
					question={currentQuestion}
					isInitialState={isInitialState}
					showAnswer={showAnswer}
				/>
			</Flex>

			{/* B2 区域：题干和选项内容 */}
			<QuestionBody
				question={currentQuestion}
				isInitialState={isInitialState}
			/>

			{/* 解析卡片：位于底部 */}
			<QuestionExplanation
				question={currentQuestion}
				showExplanation={showExplanation}
			/>
		</View>
	);
};

export default QuestionContent;
