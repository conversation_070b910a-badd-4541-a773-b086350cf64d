// ContentToggle/index.tsx
import { ToggleButtonGroup, ToggleButton } from "react-aria-components";
import "./ContentToggle.css";

interface ContentToggleProps {
	selectedType: string;
	onChange: (type: string) => void;
}

/**
 * ContentToggle 组件
 *
 * 提供全局指令和定向指令的切换功能：
 * - 全局指令：向所有设备发送MQTT消息 (target: "all")
 * - 定向指令：向特定设备发送MQTT消息 (target: "player-1", "player-2"等)
 *
 * 未来扩展：可在useMQTTIntegration中根据selectedType决定MQTT消息的target字段
 */
export function ContentToggle({ selectedType, onChange }: ContentToggleProps) {
	return (
		<ToggleButtonGroup
			className="content-toggle"
			data-selected-key={selectedType}
			selectionMode="single"
			selectedKeys={new Set([selectedType])}
			onSelectionChange={(keys) => {
				if (typeof keys !== "string" && keys.size > 0) {
					onChange(Array.from(keys)[0] as string);
				}
			}}
		>
			<ToggleButton id="global">全局指令</ToggleButton>
			<ToggleButton id="targeted">定向指令</ToggleButton>
		</ToggleButtonGroup>
	);
}
