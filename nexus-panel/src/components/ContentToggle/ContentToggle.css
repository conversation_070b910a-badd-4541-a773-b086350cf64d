.content-toggle {
  display: flex;
  margin-left: 25px;
  border: 1px solid #000000;
  border-radius: 24px;
  overflow: hidden;
  background: #1a191c;
  position: relative;
}

.content-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: calc(50% - 2px);
  height: calc(100% - 4px);
  background: #363538;
  border-radius: 20px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.content-toggle[data-selected-key='targeted']::before {
  transform: translateX(100%);
}

.react-aria-ToggleButton {
  padding: 8px 20px;
  border: none;
  background: transparent;
  color: #b0b0b0;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
  min-width: 80px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.react-aria-ToggleButton:hover {
  color: #ffffff;
}

.react-aria-ToggleButton[data-selected] {
  color: #ffffff;
}
/* 悬停状态 */
.content-toggle:hover {
  border-color: #6a6a6a;
}
