/**
 * TimeRaceRankingContainer - 争分夺秒排名容器组件
 *
 * 这个组件展示了如何使用新的简化架构：
 * - 使用 useTimeRaceRanking Hook 管理数据和状态
 * - 使用 TimeRaceRankingContent 组件进行纯UI展示
 * - 实现了关注点分离，提高了可维护性和可测试性
 * - 监听题包切换并自动重置倒计时器
 */

import React, { useRef } from 'react';
import { TimeRaceRankingContent } from './TimeRaceRankingContent';
import { useTimeRaceRanking } from '../hooks/useTimeRaceRanking';
import { globalTimerManager } from '../utils/globalTimerManager';
import type { LogFunction } from '../hooks/useRaceApi';

/**
 * 容器组件的Props接口
 */
export interface TimeRaceRankingContainerProps {
  /** 环节名称 */
  sectionName: '争分夺秒' | '同分加赛';
  /** 基础ID */
  baseId: string;
  /** 当前题包ID */
  currentPackage: string | null;
  /** 轮询间隔（毫秒），默认5000ms */
  pollingInterval?: number;
  /** 每页显示数量，默认8 */
  pageSize?: number;
  /** 自定义 CSS 类名 */
  className?: string;
  /** 自定义高度，默认为 "auto" */
  height?: string;
  /** 日志记录回调函数 */
  onLog?: LogFunction;
}

/**
 * TimeRaceRankingContainer - 争分夺秒排名容器组件
 * 
 * 这个组件演示了新架构的使用方式：
 * 1. 使用 useTimeRaceRanking Hook 管理所有数据和状态逻辑
 * 2. 将数据通过props传递给纯展示组件 TimeRaceRankingContent
 * 3. 实现了数据逻辑与UI逻辑的完全分离
 */
export const TimeRaceRankingContainer: React.FC<TimeRaceRankingContainerProps> = ({
  sectionName,
  baseId,
  currentPackage,
  pollingInterval = 5000,
  pageSize = 8,
  className,
  height = "auto",
  onLog,
}) => {

  // 使用数据管理Hook
  const {
    rankingData,
    loading,
    error,
    isPolling,
    lastUpdateTime,
    refresh } = useTimeRaceRanking({
      sectionName,
      baseId,
      pollingInterval,
      pageSize,
      initialPage: 1,
      onLog
    });

  // 强制控制台日志 - 确认组件函数被调用
  console.log(`[TimeRaceRankingContainer] 组件函数被调用`, {
    sectionName,
    baseId,
    currentPackage,
    timestamp: Date.now()
  });

  // 强制控制台日志 - 确认 useEffect 即将被设置
  console.log(`[TimeRaceRankingContainer] 即将设置 useEffect`, {
    sectionName,
    currentPackage,
    timestamp: Date.now()
  });

  // 用于防止初始化时触发重置
  const isInitialMount = useRef(true);
  const prevPackageId = useRef<string | null>(currentPackage);

  // 组件初始化时输出一次日志
  React.useEffect(() => {
    console.log(`[TimeRaceRankingContainer] ${sectionName}排名容器组件已初始化，baseId: ${baseId}`);
  }, [sectionName, baseId]);

  // 监听题包切换并重置计时器
  React.useEffect(() => {
    const currentPackageId = currentPackage;

    // 强制输出调试信息
    console.log(`[TimeRaceRankingContainer] useEffect 触发`, {
      currentPackageId,
      previousPackageId: prevPackageId.current,
      isInitialMount: isInitialMount.current,
      sectionName,
      timestamp: Date.now()
    });

    // 跳过组件初始挂载时的执行
    if (isInitialMount.current) {
      isInitialMount.current = false;
      prevPackageId.current = currentPackageId;
      console.log(`[TimeRaceRankingContainer] 初始化完成，当前题包: ${currentPackageId}`);
      return;
    }

    // 检查题包是否真的发生了变化
    if (currentPackageId !== prevPackageId.current) {
      const previousPackageId = prevPackageId.current;
      // 更新前一个题包ID的引用
      prevPackageId.current = currentPackageId;

      // 只有在争分夺秒或同分加赛环节才重置计时器
      if (sectionName === '争分夺秒' || sectionName === '同分加赛') {
        // 重置计时器
        globalTimerManager.reset();

        console.log(`[TimeRaceRankingContainer] 题包切换: ${previousPackageId} → ${currentPackageId}，已重置${sectionName}倒计时器`);

        onLog?.('info', `题包切换，已重置${sectionName}倒计时器`, {
          sectionName,
          previousPackage: previousPackageId,
          newPackage: currentPackageId,
          timestamp: Date.now(),
          action: 'timer_reset_on_package_change'
        });
      }
    } else {
      console.log(`[TimeRaceRankingContainer] useEffect 触发但题包未变化: ${currentPackageId}`);
    }
  }, [currentPackage, sectionName, onLog]);

  return (
    <TimeRaceRankingContent
      sectionName={sectionName}
      rankingData={rankingData}
      loading={loading}
      error={error}
      isPolling={isPolling}
      lastUpdateTime={lastUpdateTime}
      onRefresh={refresh}
      className={className}
      height={height}
      onLog={onLog}
      animated={true}
    />
  );
};

export default TimeRaceRankingContainer;
