// React 核心依赖导入
// Adobe React Spectrum UI 组件库导入
import { View, Flex, Grid } from "@adobe/react-spectrum";

/**
 * QuestionSkeleton 组件的 Props 接口 - Next.js 风格优化
 */
export interface QuestionSkeletonProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
	/** 是否显示排名区域，默认为 false */
	showRankingArea?: boolean;
	/** 排名区域行数，默认为 8 */
	rankingRowCount?: number;
}

/**
 * QuestionSkeleton - 题目显示区域的骨架屏加载状态组件 (Next.js 风格优化)
 *
 * 功能特性：
 * - A1 区域：题号、题型、分值占位符
 * - A2 区域：正确答案占位符
 * - B2 区域：题干和选项内容占位符
 * - 排名区域（可选）：支持争分夺秒和同分加赛环节的动态排名显示
 * - Next.js 风格设计：柔和灰色调色板、统一8px圆角、2s平滑动画
 * - 支持交互效果、状态指示、淡入淡出过渡
 * - 深色主题适配和无障碍访问优化
 * - 性能优化：硬件加速、减少重绘
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function QuestionSkeleton({
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "loading",
	fadeEffect = null,
	showRankingArea = false,
	rankingRowCount = 8,
}: QuestionSkeletonProps) {
	// 构建CSS类名
	const cssClasses = [
		"question-skeleton",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "loading" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* 顶部区域：A1 和 A2 */}
			<Flex
				justifyContent="space-between"
				alignItems="start"
				marginBottom="size-300"
				wrap="wrap"
				gap="size-200"
			>
				{/* A1 区域：题号、题型、分值 */}
				<Flex alignItems="center" gap="size-150" flex="1" minWidth="size-3000">
					{/* 题号占位符 */}
					<View
						backgroundColor="gray-300"
						borderRadius="small"
						width="size-800"
						height="size-400"
						UNSAFE_className="skeleton-item"
					/>

					{/* 题型占位符 */}
					<View
						backgroundColor="gray-300"
						borderRadius="small"
						width="size-1200"
						height="size-400"
						UNSAFE_className="skeleton-item"
					/>

					{/* 分值占位符 */}
					<View
						backgroundColor="gray-300"
						borderRadius="small"
						width="size-600"
						height="size-400"
						UNSAFE_className="skeleton-item"
					/>
				</Flex>

				{/* A2 区域：正确答案 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="size-1600"
					height="size-400"
					UNSAFE_className="skeleton-item"
				/>
			</Flex>

			{/* B2 区域：题干和选项内容 */}
			<View>
				{/* 题干占位符 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="100%"
					height="size-3000"
					marginBottom="size-300"
					UNSAFE_className="skeleton-item"
				/>

				{/* 选项占位符 */}
				<Grid
					areas={[
						"option-a option-b",
						"option-c option-d",
						"option-e option-f",
					]}
					columns={["1fr", "1fr"]}
					rows={["auto", "auto", "auto"]}
					gap="size-300"
				>
					{/* 选项 A */}
					<View gridArea="option-a">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>

					{/* 选项 B */}
					<View gridArea="option-b">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>

					{/* 选项 C */}
					<View gridArea="option-c">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>

					{/* 选项 D */}
					<View gridArea="option-d">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>

					{/* 选项 E */}
					<View gridArea="option-e">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>

					{/* 选项 F */}
					<View gridArea="option-f">
						<Flex alignItems="center" gap="size-150">
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="size-300"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
							<View
								backgroundColor="gray-300"
								borderRadius="small"
								width="100%"
								height="size-300"
								UNSAFE_className="skeleton-item"
							/>
						</Flex>
					</View>
				</Grid>
			</View>

			{/* 排名区域（可选显示） */}
			{showRankingArea && (
				<View marginTop="size-400">
					{/* 排名区域标题 */}
					<View
						backgroundColor="gray-300"
						borderRadius="small"
						width="size-2400"
						height="size-600"
						marginBottom="size-300"
						UNSAFE_className="skeleton-item"
					/>

					{/* 排名列表骨架屏 */}
					<View>
						{/* 生成指定数量的排名行 */}
						{Array.from({ length: rankingRowCount }, (_, index) => (
							<Grid
								key={`ranking-row-${index}`}
								areas={["rank name score"]}
								columns={["size-500", "2fr", "3fr"]}
								gap="size-200"
								marginBottom="size-200"
							>
								{/* 排名序号占位符 */}
								<View gridArea="rank">
									<View
										backgroundColor="gray-300"
										borderRadius="small"
										width="100%"
										height="size-500"
										UNSAFE_className="skeleton-item"
									/>
								</View>

								{/* 选手名称占位符 */}
								<View gridArea="name">
									<View
										backgroundColor="gray-300"
										borderRadius="small"
										width="100%"
										height="size-500"
										UNSAFE_className="skeleton-item"
									/>
								</View>

								{/* 得分明细占位符 */}
								<View gridArea="score">
									<View
										backgroundColor="gray-300"
										borderRadius="small"
										width="100%"
										height="size-500"
										UNSAFE_className="skeleton-item"
									/>
								</View>
							</Grid>
						))}
					</View>
				</View>
			)}
		</View>
	);
}

export default QuestionSkeleton;
