/* DeviceStatus 组件样式 */

/* 设备状态容器 */
.device-status-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spectrum-global-dimension-size-100);
  align-items: center;
}

/* 设备状态 Badge 基础样式 */
.device-status-badge {
  min-width: 32px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  user-select: none;
}

/* 可点击状态的悬停效果 */
.device-status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 不可点击状态 */
.device-status-badge.non-clickable {
  cursor: default;
}

.device-status-badge.non-clickable:hover {
  transform: none;
  box-shadow: none;
}

/* 设备状态变体样式 */
.device-status-badge.positive {
  background-color: var(--spectrum-global-color-green-600);
  color: white;
}

.device-status-badge.negative {
  background-color: var(--spectrum-global-color-red-600);
  color: white;
}

.device-status-badge.neutral {
  background-color: var(--spectrum-global-color-gray-600);
  color: white;
}

.device-status-badge.info {
  background-color: var(--spectrum-global-color-blue-600);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-status-container {
    gap: var(--spectrum-global-dimension-size-75);
  }

  .device-status-badge {
    min-width: 28px;
    height: 20px;
    font-size: 11px;
  }
}

/* 设备状态动画 */
@keyframes devicePulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 活跃设备动画效果 */
.device-status-badge.active {
  animation: devicePulse 2s infinite;
}

/* 设备状态指示器 */
.device-status-badge::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

/* 在线状态指示器 */
.device-status-badge.online::before {
  background-color: var(--spectrum-global-color-green-400);
}

/* 离线状态指示器 */
.device-status-badge.offline::before {
  background-color: var(--spectrum-global-color-red-400);
}

/* 警告状态指示器 */
.device-status-badge.warning::before {
  background-color: var(--spectrum-global-color-orange-400);
}

.custom-badge {
  border-radius: 999px !important;
  height: 24px !important;
  width: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  position: relative;
  transition: all 0.2s ease-in-out;
  text-align: center !important;
  line-height: 1 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.custom-badge:hover {
  background-color: #0078d4 !important;
}

/* 设备状态自定义配色 */
.device-online {
  background-color: #007bff !important;
  color: white !important;
}

.device-offline {
  background-color: #6c757d !important;
  color: white !important;
}

.device-selected {
  background-color: #007bff !important;
  color: white !important;
}

.device-unselected {
  background-color: #6c757d !important;
  color: white !important;
}

/* 设备状态描边样式 */
.device-border-submitted {
  border: 2px solid #28a745 !important;
}

.device-border-fault {
  border: 2px solid #dc3545 !important;
}

/* 设备 ActionButton 包装器样式 */
.device-action-button {
  padding: 0 !important;
  margin: 0 !important;
  min-height: auto !important;

  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  min-inline-size: 24px !important;
}

.device-action-button:hover {
  background: transparent !important;
  box-shadow: none !important;
}

.device-action-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

.device-action-button:active {
  background: transparent !important;
  transform: none !important;
}

/* 强制覆盖 React Spectrum Badge 的默认样式 */
.custom-badge * {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 针对 Badge 内部文本的样式 */
.custom-badge span,
.custom-badge div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
}
