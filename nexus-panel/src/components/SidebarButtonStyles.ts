/**
 * 侧边栏按钮样式系统配置文件
 *
 * 提供统一的按钮样式类型定义和配置映射，
 * 确保不同功能类型的按钮具有一致的视觉表现。
 */

/**
 * 按钮样式类型常量
 * 定义不同功能类型按钮的预设样式
 */
export const ButtonStyleType = {
	/** 主要操作 - 用于核心功能按钮（如跳转、确认等） */
	PRIMARY_ACTION: "primary-action",
	/** 导航操作 - 用于导航类按钮（如上一题、下一题等） */
	NAVIGATION: "navigation",
	/** 内容展示 - 用于内容显示类按钮（如正确答案、答案解析等） */
	CONTENT_DISPLAY: "content-display",
	/** 数据操作 - 用于数据处理类按钮（如赋分、统计等） */
	DATA_OPERATION: "data-operation",
	/** 特殊功能 - 用于特殊功能按钮（如填空放大等） */
	SPECIAL_FUNCTION: "special-function",
	/** 危险操作 - 用于可能产生不可逆影响的操作 */
	DANGER_ACTION: "danger-action",
	/** 信息展示 - 用于纯信息展示，不可点击 */
	INFO_DISPLAY: "info-display",
} as const;

/**
 * 按钮样式类型的类型定义
 */
export type ButtonStyleType =
	(typeof ButtonStyleType)[keyof typeof ButtonStyleType];

/**
 * 按钮样式配置接口
 * 定义每种样式类型对应的React Spectrum variant和CSS类名
 */
export interface ButtonStyleConfig {
	/** React Spectrum Button variant */
	variant: "accent" | "primary" | "secondary" | "negative";
	/** 自定义CSS类名 */
	className?: string;
	/** 是否禁用状态 */
	isDisabled?: boolean;
}

/**
 * 按钮样式映射配置
 * 将样式类型映射到具体的样式配置
 */
export const BUTTON_STYLE_MAP: Record<ButtonStyleType, ButtonStyleConfig> = {
	[ButtonStyleType.PRIMARY_ACTION]: {
		variant: "accent",
		className: "sidebar-button-primary-action",
	},
	[ButtonStyleType.NAVIGATION]: {
		variant: "primary",
		className: "sidebar-button-navigation",
	},
	[ButtonStyleType.CONTENT_DISPLAY]: {
		variant: "secondary",
		className: "sidebar-button-content-display",
	},
	[ButtonStyleType.DATA_OPERATION]: {
		variant: "primary",
		className: "sidebar-button-data-operation",
	},
	[ButtonStyleType.SPECIAL_FUNCTION]: {
		variant: "accent",
		className: "sidebar-button-special-function",
	},
	[ButtonStyleType.DANGER_ACTION]: {
		variant: "negative",
		className: "sidebar-button-danger-action",
	},
	[ButtonStyleType.INFO_DISPLAY]: {
		variant: "secondary",
		className: "sidebar-button-info-display",
	},
};

/**
 * 获取按钮样式配置的工具函数
 * @param styleType 按钮样式类型
 * @returns 对应的样式配置
 */
export const getButtonStyleConfig = (
	styleType: ButtonStyleType,
): ButtonStyleConfig => {
	return BUTTON_STYLE_MAP[styleType];
};

/**
 * 合并自定义样式配置的工具函数
 * @param styleType 按钮样式类型
 * @param customConfig 自定义配置（可选）
 * @returns 合并后的样式配置
 */
export const mergeButtonStyleConfig = (
	styleType: ButtonStyleType,
	customConfig?: Partial<ButtonStyleConfig>,
): ButtonStyleConfig => {
	const baseConfig = getButtonStyleConfig(styleType);

	if (!customConfig) {
		return baseConfig;
	}

	return {
		...baseConfig,
		...customConfig,
		// 合并CSS类名
		className:
			[baseConfig.className, customConfig.className]
				.filter(Boolean)
				.join(" ") || undefined,
	};
};

/**
 * 预定义的按钮功能类型映射
 * 为常见的按钮文本提供推荐的样式类型
 */
export const BUTTON_TEXT_STYLE_SUGGESTIONS: Record<string, ButtonStyleType> = {
	// 导航类
	上一题: ButtonStyleType.NAVIGATION,
	下一题: ButtonStyleType.NAVIGATION,
	上一位: ButtonStyleType.NAVIGATION,
	下一位: ButtonStyleType.NAVIGATION,
	第一页: ButtonStyleType.NAVIGATION,
	第二页: ButtonStyleType.NAVIGATION,

	// 主要操作类
	跳转: ButtonStyleType.PRIMARY_ACTION,
	确认: ButtonStyleType.PRIMARY_ACTION,
	提交: ButtonStyleType.PRIMARY_ACTION,

	// 内容展示类
	正确答案: ButtonStyleType.CONTENT_DISPLAY,
	选手答案: ButtonStyleType.CONTENT_DISPLAY,
	答案解析: ButtonStyleType.CONTENT_DISPLAY,

	// 数据操作类
	赋分: ButtonStyleType.DATA_OPERATION,
	统计: ButtonStyleType.DATA_OPERATION,
	导出: ButtonStyleType.DATA_OPERATION,

	// 特殊功能类
	填空放大: ButtonStyleType.SPECIAL_FUNCTION,
	全屏: ButtonStyleType.SPECIAL_FUNCTION,

	// 危险操作类
	删除: ButtonStyleType.DANGER_ACTION,
	重置: ButtonStyleType.DANGER_ACTION,
	清空: ButtonStyleType.DANGER_ACTION,
};

/**
 * 根据按钮文本自动推荐样式类型的工具函数
 * @param buttonText 按钮文本
 * @returns 推荐的样式类型，如果没有匹配则返回PRIMARY_ACTION
 */
export const suggestButtonStyleType = (buttonText: string): ButtonStyleType => {
	return (
		BUTTON_TEXT_STYLE_SUGGESTIONS[buttonText] || ButtonStyleType.PRIMARY_ACTION
	);
};
