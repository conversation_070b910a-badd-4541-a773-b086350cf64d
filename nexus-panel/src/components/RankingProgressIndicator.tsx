// React 核心依赖导入
import React from "react";
// Adobe React Spectrum UI 组件库导入
import { View, ProgressBar, Text, Flex, ActionButton } from "@adobe/react-spectrum";
// 导入图标
import Cancel from '@spectrum-icons/workflow/Cancel';
// 导入类型
import type { RankingProgress } from "../services/api/types";

/**
 * RankingProgressIndicator 组件的 Props 接口
 */
export interface RankingProgressIndicatorProps {
	/** 排名数据获取进度信息 */
	progress: RankingProgress;
	/** 取消操作回调 */
	onCancel?: () => void;
	/** 自定义 CSS 类名 */
	className?: string;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 日志记录回调函数 */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
}

/**
 * 获取阶段的中文描述
 */
function getStageDescription(stage: RankingProgress['stage']): string {
	switch (stage) {
		case 'table_structure':
			return '获取表结构信息';
		case 'answer_records':
			return '获取答题记录';
		case 'player_info':
			return '获取选手信息';
		case 'calculating':
			return '计算排名数据';
		case 'complete':
			return '数据获取完成';
		default:
			return '处理中';
	}
}

/**
 * 获取阶段的进度颜色
 */
function getProgressColor(stage: RankingProgress['stage'], progress: number): string {
	if (progress === 100) {
		return 'var(--spectrum-global-color-green-600)';
	}

	switch (stage) {
		case 'table_structure':
			return 'var(--spectrum-global-color-blue-600)';
		case 'answer_records':
			return 'var(--spectrum-global-color-orange-600)';
		case 'player_info':
			return 'var(--spectrum-global-color-purple-600)';
		case 'calculating':
			return 'var(--spectrum-global-color-indigo-600)';
		case 'complete':
			return 'var(--spectrum-global-color-green-600)';
		default:
			return 'var(--spectrum-global-color-gray-600)';
	}
}

/**
 * RankingProgressIndicator - 排名数据获取进度指示器组件
 *
 * 功能特性：
 * - 显示当前加载阶段的中文描述
 * - 显示具体进度百分比（使用 ProgressBar 组件）
 * - 显示分页获取进度（如"第3页/共15页"）
 * - 支持取消操作
 * - 根据不同阶段显示不同的进度颜色
 * - 使用 Adobe React Spectrum 组件库
 * - 提供详细的进度信息和用户反馈
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const RankingProgressIndicator: React.FC<RankingProgressIndicatorProps> = ({
	progress,
	onCancel,
	className,
	height = "auto",
	onLog,
}) => {
	// 用于跟踪进度更新时间的 ref
	const lastProgressTime = React.useRef(Date.now());

	// 构建CSS类名
	const cssClasses = [
		"ranking-progress-indicator",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	// 获取阶段描述和颜色
	const stageDescription = getStageDescription(progress.stage);
	const progressColor = getProgressColor(progress.stage, progress.progress);

	// 记录进度更新日志和异常检测
	React.useEffect(() => {
		try {
			// 进度数据验证
			if (progress.progress < 0 || progress.progress > 100) {
				onLog?.('warning', '检测到异常的进度值', {
					progress: progress.progress,
					stage: progress.stage,
					timestamp: Date.now(),
					action: 'ranking_progress_invalid'
				});
			}

			// 检测进度是否长时间停滞
			const currentTime = Date.now();

			if (progress.progress > 0 && progress.progress < 100) {
				if (currentTime - lastProgressTime.current > 30000) { // 30秒无进度更新
					onLog?.('warning', '进度更新可能停滞，数据量较大时属正常现象', {
						stage: progress.stage,
						progress: progress.progress,
						stallTime: currentTime - lastProgressTime.current,
						timestamp: Date.now(),
						action: 'ranking_progress_stall'
					});
				}
			}
			lastProgressTime.current = currentTime;

			onLog?.('info', `排名数据获取进度更新: ${progress.progress}%`, {
				stage: progress.stage,
				progress: progress.progress,
				message: progress.message,
				currentPage: progress.currentPage,
				totalPages: progress.totalPages,
				timestamp: Date.now(),
				action: 'ranking_progress_update'
			});
		} catch (error) {
			onLog?.('error', '进度指示器更新时发生错误', {
				error: error instanceof Error ? error.message : '未知错误',
				progress: progress,
				timestamp: Date.now(),
				action: 'ranking_progress_error'
			});
		}
	}, [progress, onLog]);

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-400"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* 标题区域 */}
			<Flex justifyContent="space-between" alignItems="center" marginBottom="size-300">
				<Text UNSAFE_style={{
					fontSize: '18px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)'
				}}>
					正在获取排名数据
				</Text>

				{/* 取消按钮 */}
				{onCancel && (
					<ActionButton
						isQuiet
						onPress={() => {
							onLog?.('info', '用户取消排名数据获取', {
								stage: progress.stage,
								progress: progress.progress,
								timestamp: Date.now(),
								action: 'ranking_fetch_cancelled'
							});
							onCancel();
						}}
						aria-label="取消获取"
					>
						<Cancel />
					</ActionButton>
				)}
			</Flex>

			{/* 当前阶段描述 */}
			<View marginBottom="size-300">
				<Text UNSAFE_style={{
					fontSize: '16px',
					color: 'var(--spectrum-global-color-gray-700)',
					fontWeight: '500'
				}}>
					当前阶段：{stageDescription}
				</Text>
			</View>

			{/* 进度条 */}
			<View marginBottom="size-300">
				<ProgressBar
					label="数据获取进度"
					value={progress.progress}
					maxValue={100}
					showValueLabel={true}
					UNSAFE_style={{
						'--spectrum-progressbar-fill-color': progressColor
					} as React.CSSProperties}
				/>
			</View>

			{/* 详细进度信息 */}
			<View marginBottom="size-200">
				<Text UNSAFE_style={{
					fontSize: '14px',
					color: 'var(--spectrum-global-color-gray-600)'
				}}>
					{progress.message}
				</Text>
			</View>

			{/* 分页信息（如果有） */}
			{progress.currentPage && progress.totalPages && (
				<View marginBottom="size-200">
					<Text UNSAFE_style={{
						fontSize: '14px',
						color: 'var(--spectrum-global-color-gray-600)',
						fontStyle: 'italic'
					}}>
						正在处理第 {progress.currentPage} 页，共 {progress.totalPages} 页
					</Text>
				</View>
			)}

			{/* 阶段进度指示器 */}
			<View marginTop="size-300">
				<Flex gap="size-100" wrap>
					{[
						{ key: 'table_structure', label: '表结构' },
						{ key: 'answer_records', label: '答题记录' },
						{ key: 'player_info', label: '选手信息' },
						{ key: 'calculating', label: '计算排名' },
						{ key: 'complete', label: '完成' }
					].map((stage, index) => {
						const isActive = progress.stage === stage.key;
						const isCompleted = getStageOrder(progress.stage) > index;
						const isUpcoming = getStageOrder(progress.stage) < index;

						return (
							<View
								key={stage.key}
								UNSAFE_style={{
									padding: '4px 8px',
									borderRadius: '12px',
									backgroundColor: isActive
										? progressColor
										: isCompleted
											? 'var(--spectrum-global-color-green-600)'
											: isUpcoming
												? 'var(--spectrum-global-color-gray-300)'
												: 'var(--spectrum-global-color-gray-400)',
									color: isUpcoming ? 'var(--spectrum-global-color-gray-700)' : 'white',
									fontSize: '12px',
									fontWeight: isActive ? 'bold' : 'normal',
									transition: 'all 0.3s ease'
								}}
							>
								{stage.label}
							</View>
						);
					})}
				</Flex>
			</View>

			{/* 提示信息 */}
			<View marginTop="size-400">
				<Text UNSAFE_style={{
					fontSize: '12px',
					color: 'var(--spectrum-global-color-gray-500)',
					textAlign: 'center',
					fontStyle: 'italic'
				}}>
					数据量较大时可能需要较长时间，请耐心等待...
				</Text>
			</View>
		</View>
	);
};

/**
 * 获取阶段的顺序索引
 */
function getStageOrder(stage: RankingProgress['stage']): number {
	const stageOrder = {
		'table_structure': 0,
		'answer_records': 1,
		'player_info': 2,
		'calculating': 3,
		'complete': 4
	};
	return stageOrder[stage] ?? -1;
}

export default RankingProgressIndicator;
