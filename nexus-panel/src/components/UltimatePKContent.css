/* UltimatePKContent 组件样式 */

.ultimate-pk-content {
  /* 基础容器样式 */
  position: relative;
  min-height: 400px;

  /* 深色主题适配 */
  background-color: var(--spectrum-global-color-gray-100);
  border: 1px solid var(--spectrum-global-color-gray-300);

  /* 圆角和阴影 */
  border-radius: var(--spectrum-global-dimension-size-100);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s ease;
}

/* 计时器警告状态（时间不足30秒） */
.ultimate-pk-content .timer-display.warning {
  color: var(--spectrum-global-color-orange-600);
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-orange-100) 0%,
    var(--spectrum-global-color-orange-200) 100%
  );
}

/* 计时器危险状态（时间归零） */
.ultimate-pk-content .timer-display.danger {
  color: var(--spectrum-global-color-red-600);
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-red-100) 0%,
    var(--spectrum-global-color-red-200) 100%
  );
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 计时器运行状态 */
.ultimate-pk-content .timer-display.running {
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-blue-100) 0%,
    var(--spectrum-global-color-blue-200) 100%
  );
  color: var(--spectrum-global-color-blue-700);
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

/* 选手名称显示样式 */
.ultimate-pk-content .speaker-name {
  font-size: 14px;
  font-weight: bold;
  color: var(--spectrum-global-color-gray-800);
  text-align: center;
  padding: var(--spectrum-global-dimension-size-150);
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-gray-200) 0%,
    var(--spectrum-global-color-gray-300) 100%
  );
  border-radius: var(--spectrum-global-dimension-size-50);
}

/* 正方选手名称 */
.ultimate-pk-content .speaker-name.positive {
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-green-100) 0%,
    var(--spectrum-global-color-green-200) 100%
  );
  color: var(--spectrum-global-color-green-700);
}

/* 反方选手名称 */
.ultimate-pk-content .speaker-name.negative {
  background: linear-gradient(
    135deg,
    var(--spectrum-global-color-red-100) 0%,
    var(--spectrum-global-color-red-200) 100%
  );
  color: var(--spectrum-global-color-red-700);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ultimate-pk-content .timer-display {
    font-size: 24px;
    padding: var(--spectrum-global-dimension-size-200);
  }

  .ultimate-pk-content .stage-title {
    font-size: 16px;
  }

  .ultimate-pk-content .speaker-name {
    font-size: 12px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .ultimate-pk-content {
    background-color: var(--spectrum-global-color-gray-200);
    border-color: var(--spectrum-global-color-gray-400);
  }

  .ultimate-pk-content .stage-title,
  .ultimate-pk-content .timer-display,
  .ultimate-pk-content .speaker-name {
    background: linear-gradient(
      135deg,
      var(--spectrum-global-color-gray-300) 0%,
      var(--spectrum-global-color-gray-400) 100%
    );
    color: var(--spectrum-global-color-gray-900);
  }
}

/* 无障碍访问支持 */
@media (prefers-reduced-motion: reduce) {
  .ultimate-pk-content,
  .ultimate-pk-content.animated,
  .ultimate-pk-content .timer-display {
    animation: none;
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ultimate-pk-content {
    border-width: 2px;
  }

  .ultimate-pk-content .timer-display,
  .ultimate-pk-content .stage-title,
  .ultimate-pk-content .speaker-name {
    border: 1px solid var(--spectrum-global-color-gray-600);
  }
}
