/* React Spectrum TreeView 自定义样式 - 完整交互状态 */
/* ===== CSS Modules 类名兼容 (备用方案) ===== */
/* 如果 role 选择器不生效，使用 CSS Modules 类名 */

/* 悬停状态 - CSS Modules 方式 */
.navigation-tree-view [class*="s1-"]:hover {
	cursor: pointer !important;
	border-radius: 8px;
}
/* 悬停状态 - CSS Modules 方式 */
.navigation-tree-view [class*="s1-_o-1ytnijz"] {
	/* background-color: rgba(0, 110, 239, 0.1) !important; */
	border-radius: 8px;
	display: none;
}

/* 未选中状态 - CSS Modules 方式 */
.navigation-tree-view [class*="s1-"][aria-selected="false"] {
	color: #b0b0b0 !important;
	border-radius: 8px;
}

/* 选中状态 - CSS Modules 方式 */
.navigation-tree-view [class*="s1-"][aria-selected="true"] {
	background-color: #006eef !important;
	color: #ffffff !important;
	border-radius: 8px;
}

/* 焦点状态 - CSS Modules 方式 */
.navigation-tree-view [class*="s1-"]:focus-visible {
	outline: 2px solid #0078d4 !important;
	outline-offset: -2px !important;
}

/* 展开状态 - CSS Modules 方式 */
/* .navigation-tree-view [class*="s1-"][aria-expanded="true"] {
  border-left: 2px solid rgba(0, 110, 239, 0.3) !important;
} */

/* ===== 动画和过渡效果 ===== */
.navigation-tree-view [role="treeitem"] {
	transition:
		background-color 0.2s ease,
		color 0.2s ease,
		transform 0.2s ease;
}

/* 展开按钮动画 */
.navigation-tree-view [role="button"][aria-label*="展开"],
.navigation-tree-view [role="button"][aria-label*="折叠"] {
	transition: transform 0.2s ease;
}
