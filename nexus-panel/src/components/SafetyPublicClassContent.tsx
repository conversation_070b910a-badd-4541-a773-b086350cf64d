/**
 * 安全公开课内容组件
 *
 * 替代静态SafetyPublicClassSkeleton，根据配置数据和选择状态动态显示内容
 * 支持节目显示和选手显示两种模式
 */

import React from 'react';
import { View, Text } from '@adobe/react-spectrum';
import { ImageWithRetry } from './common/ImageWithRetry';
import type { GroupedConfigurationData, ProcessedConfigurationItem } from '../services/api/types';

// ==================== 类型定义 ====================

/**
 * SafetyPublicClassContent 组件 Props 接口
 */
export interface SafetyPublicClassContentProps {
	/** 当前显示类型 */
	displayType: 'program' | 'player' | null;
	/** 当前选中的配置项ID */
	selectedItemId: string | null;
	/** 配置数据 */
	configurationData: GroupedConfigurationData | null;
	/** 自定义CSS类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
	/** 图片加载错误回调 */
	onImageError?: (error: string) => void;
	/** 日志记录回调 */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
}

// ==================== 辅助函数 ====================

/**
 * 获取显示类型的中文标题
 */
function getDisplayTypeTitle(displayType: 'program' | 'player' | null): string {
	switch (displayType) {
		case 'program':
			return '节目显示';
		case 'player':
			return '选手显示';
		default:
			return '安全公开课';
	}
}

/**
 * 根据ID查找选中的配置项
 */
function findSelectedItem(
	displayType: 'program' | 'player' | null,
	selectedItemId: string | null,
	configurationData: GroupedConfigurationData | null
): ProcessedConfigurationItem | null {
	if (!displayType || !selectedItemId || !configurationData) {
		return null;
	}

	const dataArray = displayType === 'program'
		? configurationData.programDisplay
		: configurationData.playerDisplay;

	return dataArray.find(item => item.id.toString() === selectedItemId) || null;
}

/**
 * 渲染A1区域内容（标题区域）
 */
function renderA1Area(displayType: 'program' | 'player' | null): React.ReactNode {
	const title = getDisplayTypeTitle(displayType);

	return (
		<View
			backgroundColor="gray-300"
			borderRadius="small"
			width="100%"
			height="size-600"
			marginBottom="size-400"
			UNSAFE_className="skeleton-item safety-public-class-title-area"
			UNSAFE_style={{
				display: "flex",
				alignItems: "center",
				justifyContent: "center",
			}}
		>
			<Text UNSAFE_style={{
				fontSize: '18px',
				fontWeight: 'bold',
				color: 'var(--spectrum-global-color-gray-800)'
			}}>
				{title}
			</Text>
		</View>
	);
}

/**
 * 渲染B1区域内容（主要内容区域）
 */
function renderB1Area(
	displayType: 'program' | 'player' | null,
	selectedItem: ProcessedConfigurationItem | null,
	onImageError?: (error: string) => void,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void
): React.ReactNode {
	if (!selectedItem) {
		// 未选择任何项目时显示占位符
		return renderDefaultContent(displayType);
	}

	// 根据显示类型渲染不同内容
	switch (displayType) {
		case 'program':
			return renderProgramContent(selectedItem);
		case 'player':
			return renderPlayerContent(selectedItem, onImageError, onLog);
		default:
			return renderDefaultContent(displayType);
	}
}

/**
 * 渲染节目显示内容
 * 第一行：显示标题，第二行：显示内容
 */
function renderProgramContent(selectedItem: ProcessedConfigurationItem): React.ReactNode {
	return (
		<View marginBottom="size-100" UNSAFE_style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
			{/* 第一行：标题 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-100"
				UNSAFE_style={{
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '60px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)'
				}}>
					{selectedItem.title}
				</Text>
			</View>

			{/* 第二行：内容 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-300"
			>
				<Text UNSAFE_style={{
					fontSize: '40px',
					lineHeight: '1.6',
					color: 'var(--spectrum-global-color-gray-700)',
					display: "block",
					textAlign: "center", // 文本居中对齐
					whiteSpace: 'pre-wrap', // 保留换行符和空格
					width: '100%',
				}}>
					{selectedItem.content}
				</Text>
			</View>
		</View>
	);
}

/**
 * 渲染选手显示内容
 * 第一行：显示图片，第二行：显示标题
 */
function renderPlayerContent(
	selectedItem: ProcessedConfigurationItem,
	onImageError?: (error: string) => void,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void
): React.ReactNode {
	return (
		<View marginBottom="size-100" UNSAFE_style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
			{/* 第一行：图片 */}
			<View
				borderRadius="small"
				backgroundColor="gray-200"
				UNSAFE_style={{
					minHeight: '300px', // 增加一些空间避免裁切
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
					padding: '10px', // 添加内边距
				}}
			>
				{selectedItem.primaryImageUrl ? (
					<ImageWithRetry
						src={selectedItem.primaryImageUrl}
						alt={selectedItem.title}
						onError={(error) => {
							onImageError?.(error);
							onLog?.('error', '选手图片加载失败', {
								imageUrl: selectedItem.primaryImageUrl,
								itemTitle: selectedItem.title,
								error,
								timestamp: Date.now(),
								action: 'image_load_error'
							});
						}}
						onLoad={() => {
							onLog?.('success', '选手图片加载成功', {
								imageUrl: selectedItem.primaryImageUrl,
								itemTitle: selectedItem.title,
								timestamp: Date.now(),
								action: 'image_load_success'
							});
						}}
						style={{
							objectFit: 'contain', // 改为 contain 以保持宽高比
							borderRadius: '6px',
							maxHeight: '250px', // 最大高度限制
							width: 'auto', // 宽度自动缩放
							display: 'block',
							margin: '0 auto', // 图片在容器内居中
						}}
					/>
				) : (
					<Text UNSAFE_style={{
						fontSize: '14px',
						color: 'var(--spectrum-global-color-gray-600)'
					}}>
						暂无图片
					</Text>
				)}
			</View>

			{/* 第二行：标题 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-200"
				UNSAFE_style={{
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '50px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)'
				}}>
					{selectedItem.title}
				</Text>
			</View>
		</View>
	);
}

/**
 * 渲染默认内容
 */
function renderDefaultContent(displayType: 'program' | 'player' | null): React.ReactNode {
	const placeholderText = displayType === 'program' ? '请在右侧选择要显示的节目' :
		displayType === 'player' ? '请在右侧选择要显示的选手' : '请在右侧选择显示内容';

	return (
		<View marginBottom="size-100">
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="100%"
				height="size-3000"
				UNSAFE_className="skeleton-item"
				UNSAFE_style={{
					display: "flex",
					flexDirection: "column",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '60px',
					fontWeight: 'bold',
					marginBottom: '8px'
				}}>
					安全公开课
				</Text>
				<Text UNSAFE_style={{
					fontSize: '14px',
					color: 'var(--spectrum-global-color-gray-600)'
				}}>
					{placeholderText}
				</Text>
			</View>
		</View>
	);
}

// ==================== 主要组件 ====================

/**
 * SafetyPublicClassContent - 安全公开课内容组件
 *
 * 功能特性：
 * - 根据显示类型和选中项动态渲染内容
 * - A1区域：显示当前类型标题
 * - B1区域：根据类型显示不同格式的内容
 * - 支持节目显示和选手显示两种模式
 * - 支持图片加载和错误处理
 * - 兼容原有骨架屏样式系统
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const SafetyPublicClassContent: React.FC<SafetyPublicClassContentProps> = ({
	displayType,
	selectedItemId,
	configurationData,
	className,
	animated = true,
	height = "auto",
	interactive = false,
	status = "loading",
	fadeEffect = null,
	onImageError,
	onLog,
}) => {
	// 查找选中的配置项
	const selectedItem = findSelectedItem(displayType, selectedItemId, configurationData);

	// 构建CSS类名
	const cssClasses = [
		"safety-public-class-content",
		animated ? "animated" : "",
		interactive ? "interactive" : "",
		status !== "loading" ? status : "",
		fadeEffect ? fadeEffect : "",
		className || "",
	]
		.filter(Boolean)
		.join(" ");

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height={height}
			UNSAFE_className={cssClasses}
		>
			{/* A1 区域：标题区域 */}
			{renderA1Area(displayType)}

			{/* B1 区域：主要内容区域 */}
			{renderB1Area(displayType, selectedItem, onImageError, onLog)}
		</View>
	);
};

export default SafetyPublicClassContent;
