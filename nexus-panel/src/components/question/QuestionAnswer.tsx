/**
 * QuestionAnswer - 题目答案显示组件（A2区域）
 * 
 * 功能特性：
 * - 显示题目的正确答案
 * - 支持初始状态和题目状态的切换
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局
 */

import React from 'react';
import { View, Text } from '@adobe/react-spectrum';
import type { ProcessedQuestionItem } from '../../services/api/types';

// ==================== 类型定义 ====================

/**
 * QuestionAnswer组件Props接口
 */
export interface QuestionAnswerProps {
	/** 当前题目数据（null表示初始状态） */
	question: ProcessedQuestionItem | null;
	/** 是否为初始状态 */
	isInitialState?: boolean;
	/** 是否显示答案（用于控制答案的显示/隐藏） */
	showAnswer?: boolean;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum样式 */
	UNSAFE_style?: React.CSSProperties;
}

// ==================== 组件实现 ====================

/**
 * QuestionAnswer - 题目答案显示组件
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const QuestionAnswer: React.FC<QuestionAnswerProps> = ({
	question,
	isInitialState = false,
	showAnswer = true,
	className,
	UNSAFE_style,
}) => {
	// 如果是初始状态或没有题目数据，显示占位符
	if (isInitialState || !question) {
		return (
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				width="size-1600"
				height="size-400"
				UNSAFE_className={`skeleton-item ${className || ''}`}
				UNSAFE_style={UNSAFE_style}
			/>
		);
	}

	// 显示实际答案
	return (
		<View
			backgroundColor="gray-200"
			borderRadius="small"
			padding="size-100"
			UNSAFE_className={className}
			UNSAFE_style={{
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				minWidth: '120px',
				height: '32px',
				...UNSAFE_style,
			}}
		>
			<Text UNSAFE_style={{
				fontSize: '14px',
				fontWeight: 'bold',
				color: showAnswer
					? 'var(--spectrum-global-color-gray-800)'
					: 'var(--spectrum-global-color-gray-400)',
			}}>
				正确答案：{question.answer}
			</Text>
		</View>
	);
};

export default QuestionAnswer;
