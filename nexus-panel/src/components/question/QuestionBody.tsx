/**
 * QuestionBody - 题目主体内容组件（B2区域）
 * 
 * 功能特性：
 * - 显示题干和选项内容
 * - 支持初始状态显示"比赛即将开始"
 * - 自动解析和显示A、B、C、D选项
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局
 */

import React from 'react';
import { View, Text, Grid, Flex } from '@adobe/react-spectrum';
import type { ProcessedQuestionItem } from '../../services/api/types';

// ==================== 类型定义 ====================

/**
 * QuestionBody组件Props接口
 */
export interface QuestionBodyProps {
	/** 当前题目数据（null表示初始状态） */
	question: ProcessedQuestionItem | null;
	/** 是否为初始状态 */
	isInitialState?: boolean;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum样式 */
	UNSAFE_style?: React.CSSProperties;
}

// ==================== 组件实现 ====================

/**
 * QuestionBody - 题目主体内容组件
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const QuestionBody: React.FC<QuestionBodyProps> = ({
	question,
	isInitialState = false,
	className,
	UNSAFE_style,
}) => {
	// 如果是初始状态，显示"比赛即将开始"
	if (isInitialState || !question) {
		return (
			<View
				UNSAFE_className={className}
				UNSAFE_style={UNSAFE_style}
			>
				{/* 题干占位符或初始状态提示 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="100%"
					marginBottom="size-300"
					UNSAFE_style={{
						minHeight: '200px',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
					}}
				>
					{isInitialState ? (
						<Text UNSAFE_style={{
							fontSize: '48px',
							fontWeight: 'bold',
							color: 'var(--spectrum-global-color-gray-700)',
							textAlign: 'center',
						}}>
							比赛即将开始
						</Text>
					) : (
						<View
							backgroundColor="gray-300"
							borderRadius="small"
							width="100%"
							height="size-3000"
							UNSAFE_className="skeleton-item"
						/>
					)}
				</View>

				{/* 选项占位符（仅在非初始状态显示） */}
				{!isInitialState && (
					<Grid
						areas={[
							"option-a option-b",
							"option-c option-d",
							"option-e option-f",
						]}
						columns={["1fr", "1fr"]}
						rows={["auto", "auto", "auto"]}
						gap="size-300"
					>
						{/* 选项 A */}
						<View gridArea="option-a">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>

						{/* 选项 B */}
						<View gridArea="option-b">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>

						{/* 选项 C */}
						<View gridArea="option-c">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>

						{/* 选项 D */}
						<View gridArea="option-d">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>

						{/* 选项 E */}
						<View gridArea="option-e">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>

						{/* 选项 F */}
						<View gridArea="option-f">
							<Flex alignItems="center" gap="size-150">
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="size-300"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
								<View
									backgroundColor="gray-300"
									borderRadius="small"
									width="100%"
									height="size-300"
									UNSAFE_className="skeleton-item"
								/>
							</Flex>
						</View>
					</Grid>
				)}
			</View>
		);
	}

	// 显示实际题目内容
	return (
		<View
			UNSAFE_className={className}
			UNSAFE_style={UNSAFE_style}
		>
			{/* 题干 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-300"
				marginBottom="size-300"
				UNSAFE_style={{
					minHeight: '120px',
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '18px',
					lineHeight: '1.6',
					color: 'var(--spectrum-global-color-gray-800)',
					whiteSpace: 'pre-wrap',
				}}>
					{question.stem}
				</Text>
			</View>

			{/* 选项 */}
			{question.options.length > 0 && (
				<Grid
					areas={[
						"option-a option-b",
						"option-c option-d",
						"option-e option-f",
					]}
					columns={["1fr", "1fr"]}
					rows={["auto", "auto", "auto"]}
					gap="size-300"
				>
					{question.options.map((option, index) => {
						const gridAreas = ["option-a", "option-b", "option-c", "option-d", "option-e", "option-f"];
						const gridArea = gridAreas[index] || `option-${index}`;

						return (
							<View key={option.label} gridArea={gridArea}>
								<Flex alignItems="center" gap="size-150">
									{/* 选项标识 */}
									<View
										backgroundColor="gray-200"
										borderRadius="small"
										UNSAFE_style={{
											width: '24px',
											height: '24px',
											display: 'flex',
											alignItems: 'center',
											justifyContent: 'center',
											flexShrink: 0,
										}}
									>
										<Text UNSAFE_style={{
											fontSize: '14px',
											fontWeight: 'bold',
											color: 'var(--spectrum-global-color-gray-800)',
										}}>
											{option.label}
										</Text>
									</View>

									{/* 选项内容 */}
									<View
										backgroundColor="gray-200"
										borderRadius="small"
										padding="size-150"
										flex="1"
									>
										<Text UNSAFE_style={{
											fontSize: '14px',
											lineHeight: '1.4',
											color: 'var(--spectrum-global-color-gray-800)',
										}}>
											{option.content}
										</Text>
									</View>
								</Flex>
							</View>
						);
					})}
				</Grid>
			)}
		</View>
	);
};

export default QuestionBody;
