/**
 * QuestionExplanation - 题目解析卡片组件
 * 
 * 功能特性：
 * - 显示题目的解析内容
 * - 独立的卡片样式，位于题目内容底部
 * - 支持解析内容的显示/隐藏控制
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局
 */

import React from 'react';
import { View, Text, Heading } from '@adobe/react-spectrum';
import type { ProcessedQuestionItem } from '../../services/api/types';

// ==================== 类型定义 ====================

/**
 * QuestionExplanation组件Props接口
 */
export interface QuestionExplanationProps {
	/** 当前题目数据（null表示无解析） */
	question: ProcessedQuestionItem | null;
	/** 是否显示解析（用于控制解析的显示/隐藏） */
	showExplanation?: boolean;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum样式 */
	UNSAFE_style?: React.CSSProperties;
}

// ==================== 组件实现 ====================

/**
 * QuestionExplanation - 题目解析卡片组件
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const QuestionExplanation: React.FC<QuestionExplanationProps> = ({
	question,
	showExplanation = true,
	className,
	UNSAFE_style,
}) => {
	// 如果不显示解析、没有题目数据或解析内容为空，则不渲染
	if (!showExplanation || !question || !question.explanation || question.explanation.trim() === '') {
		return null;
	}

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			marginTop="size-400"
			UNSAFE_className={className}
			UNSAFE_style={{
				border: '1px solid var(--spectrum-global-color-gray-300)',
				...UNSAFE_style,
			}}
		>
			{/* 解析标题 */}
			<View
				marginBottom="size-200"
				UNSAFE_style={{
					borderBottom: '1px solid var(--spectrum-global-color-gray-300)',
					paddingBottom: '8px',
				}}
			>
				<Heading level={4} UNSAFE_style={{
					fontSize: '16px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-900)',
					margin: 0,
				}}>
					答案解析
				</Heading>
			</View>

			{/* 解析内容 */}
			<View>
				<Text UNSAFE_style={{
					fontSize: '14px',
					lineHeight: '1.6',
					color: 'var(--spectrum-global-color-gray-700)',
					whiteSpace: 'pre-wrap', // 保持换行格式
				}}>
					{question.explanation}
				</Text>
			</View>
		</View>
	);
};

export default QuestionExplanation;
