/**
 * QuestionHeader - 题目头部信息组件（A1区域）
 *
 * 功能特性：
 * - 显示题号、题型、分值信息
 * - 支持初始状态和题目状态的切换
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局
 */

import React from 'react';
import { View, Flex, Text } from '@adobe/react-spectrum';
import type { ProcessedQuestionItem } from '../../services/api/types';

// ==================== 类型定义 ====================

/**
 * QuestionHeader组件Props接口
 */
export interface QuestionHeaderProps {
	/** 当前题目数据（null表示初始状态） */
	question: ProcessedQuestionItem | null;
	/** 是否为初始状态 */
	isInitialState?: boolean;
	/** 题目总数（用于显示进度格式） */
	totalQuestions?: number;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum样式 */
	UNSAFE_style?: React.CSSProperties;
}

// ==================== 组件实现 ====================

/**
 * QuestionHeader - 题目头部信息组件
 * 
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const QuestionHeader: React.FC<QuestionHeaderProps> = ({
	question,
	isInitialState = false,
	totalQuestions,
	className,
	UNSAFE_style,
}) => {

	// 如果是初始状态，显示占位符
	if (isInitialState || !question) {
		return (
			<Flex
				alignItems="center"
				gap="size-150"
				flex="1"
				minWidth="size-3000"
				UNSAFE_className={className}
				UNSAFE_style={UNSAFE_style}
			>
				{/* 题号占位符 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="size-800"
					height="size-400"
					UNSAFE_className="skeleton-item"
				/>

				{/* 题型占位符 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="size-1200"
					height="size-400"
					UNSAFE_className="skeleton-item"
				/>

				{/* 分值占位符 */}
				<View
					backgroundColor="gray-300"
					borderRadius="small"
					width="size-600"
					height="size-400"
					UNSAFE_className="skeleton-item"
				/>
			</Flex>
		);
	}

	// 显示实际题目信息
	return (
		<Flex
			alignItems="center"
			gap="size-150"
			flex="1"
			minWidth="size-3000"
			UNSAFE_className={className}
			UNSAFE_style={UNSAFE_style}
		>
			{/* 题号 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-100"
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					minWidth: '60px',
					height: '32px',
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '14px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)',
				}}>
					{question.questionNumber}/{totalQuestions || '?'}
				</Text>
			</View>

			{/* 题型 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-100"
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					minWidth: '80px',
					height: '32px',
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '14px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)',
				}}>
					{question.questionType}
				</Text>
			</View>

			{/* 分值 */}
			<View
				backgroundColor="gray-200"
				borderRadius="small"
				padding="size-100"
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					minWidth: '50px',
					height: '32px',
				}}
			>
				<Text UNSAFE_style={{
					fontSize: '14px',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)',
				}}>
					{question.score}分
				</Text>
			</View>
		</Flex>
	);
};

export default QuestionHeader;
