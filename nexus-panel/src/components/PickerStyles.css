/* React Spectrum Picker 自定义样式 - 针对实际类名 */

/* 方法1: 使用实际的 CSS Modules 类名 */
.custom-picker [class*="spectrum-FieldButton"] {
	border: 1px solid #000000;
	background-color: #1a191c;
	border-radius: 8px;
	color: #ffffff;
	min-width: 100px;
	max-width: 150px;
}

/* 悬停状态 */
.custom-picker [class*="spectrum-FieldButton"]:hover {
	border-color: #6a6a6a;
	background-color: #3a3a3a;
}

/* 打开状态 */
.custom-picker [class*="spectrum-FieldButton"][aria-expanded="true"] {
	border-color: #0078d4;
}

/* 文本和图标样式 */
.custom-picker [class*="spectrum-FieldButton"] * {
	color: #ffffff;
}

/* 占位符文本 */
.custom-picker [class*="spectrum-FieldButton"] [class*="placeholder"] {
	color: #cccccc;
}
