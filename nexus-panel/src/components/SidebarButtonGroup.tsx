// React 核心依赖导入
import React, { useState, useEffect } from "react";
// Adobe React Spectrum UI 组件库导入
import {
	View,
	Header,
	Button,
	ActionButton,
	TooltipTrigger,
	Tooltip,
	Form,
	NumberField,
	TextField,
	Picker,
	Item,
	DialogContainer,
	AlertDialog,
	ActionGroup,
} from "@adobe/react-spectrum";
// 导入图标
import Info from "@spectrum-icons/workflow/Info";
// 导入样式系统
import { ButtonStyleType, getButtonStyleConfig } from "./SidebarButtonStyles";
// Motion动画导入
import { motion } from "motion/react";
import { sidebarAnimation } from "../config/motionConfig";
import type { SidebarAnimationProps } from "../types/motion";
// 导入样式
import "./SidebarButtonGroup.css";
// 导入 ActionGroup 配置类型
import type { ActionGroupButtonConfig } from "../config/buttonGroupConfigurations";

/**
 * 按钮配置接口
 */
export interface ButtonConfig {
	/** 按钮文本 */
	text: string;
	/** 按钮样式类型（推荐使用，会自动应用对应的variant和样式） */
	styleType?: ButtonStyleType;
	/** 按钮变体样式（手动指定，会覆盖styleType的variant设置） */
	variant?: "accent" | "primary" | "secondary" | "negative";
	/** 自定义CSS类名（会与styleType的className合并） */
	className?: string;
	/** 点击事件处理器 */
	onPress?: () => void;
	/** 动态处理器（接收表单状态作为参数） */
	dynamicHandler?: (formState: Record<string, string | number>) => void;
	/** 是否禁用（支持boolean值或getter函数） */
	isDisabled?: boolean | (() => boolean);
}

/**
 * 表单字段配置接口
 */
export interface FormFieldConfig {
	/** 字段标签 */
	label: string;
	/** 字段名称 */
	name: string;
	/** 字段类型，默认为 'number' */
	type?: 'number' | 'picker' | 'text';
	/** 默认值（number 类型时为数字，text 类型时为字符串） */
	defaultValue?: number | string;
	/** 最小值（当 type 为 'number' 时使用） */
	minValue?: number;
	/** 最大值（当 type 为 'number' 时使用） */
	maxValue?: number;
	/** 最大宽度 */
	maxWidth?: string;
	/** Picker 选项配置（当 type 为 'picker' 时使用） */
	options?: Array<{ key: string; label: string }>;
	/** 占位符文本（Picker 和 TextField 共用） */
	placeholder?: string;
	/** Picker 初始选中值 */
	selectedKey?: string;
	/** Picker 选择变更回调 */
	onSelectionChange?: (key: string) => void;
	/** TextField 文本值（当 type 为 'text' 时使用） */
	textValue?: string;
	/** TextField 文本变更回调 */
	onTextChange?: (value: string) => void;
}

/**
 * 渲染顺序类型定义
 */
export type RenderOrderItem = "buttons" | "formFields" | "additionalButtons";

/**
 * 侧边栏按钮组Props接口
 */
export interface SidebarButtonGroupProps extends SidebarAnimationProps {
	/** 组件标题 */
	title: string;
	/** Tooltip提示内容 */
	tooltipContent?: string;
	/** 按钮配置数组 */
	buttons: ButtonConfig[];
	/** 表单字段配置（可选） */
	formFields?: FormFieldConfig[];
	/** 额外的按钮组（在表单字段后显示） */
	additionalButtons?: ButtonConfig[];
	/** 内容渲染顺序，默认为 ['formFields', 'buttons', 'additionalButtons'] */
	renderOrder?: RenderOrderItem[];
	/** 是否在按钮前显示换行 */
	showBreakBeforeButtons?: boolean;
	/** 是否在额外按钮前显示换行 */
	showBreakBeforeAdditionalButtons?: boolean;
	/** 自定义样式 */
	UNSAFE_style?: React.CSSProperties;
	/** 外部重置字段名称（当此值变化时，对应字段将被重置） */
	resetFieldName?: string;
	/** 重置字段的时间戳（用于触发重置） */
	resetTimestamp?: number;
}

/**
 * 侧边栏按钮组组件
 *
 * 用于创建具有统一样式的侧边栏功能区块，包含标题、可选的表单字段和按钮组。
 * 支持自定义按钮配置、表单字段和Tooltip提示。
 *
 * @param props - 组件属性
 * @returns 侧边栏按钮组JSX元素
 */
export const SidebarButtonGroup: React.FC<SidebarButtonGroupProps> = ({
	title,
	tooltipContent = "控制题目区域",
	buttons,
	formFields = [],
	additionalButtons = [],
	renderOrder = ["formFields", "buttons", "additionalButtons"],
	showBreakBeforeButtons = false,
	showBreakBeforeAdditionalButtons = false,
	UNSAFE_style,
	// 外部重置控制props
	resetFieldName,
	resetTimestamp,
	// 动画相关props
	animated = true,
	duration,
	transition,
	reduceMotion = false,
	groupSwitchAnimation = { enabled: true, direction: 'right' },
}) => {
	// 表单状态管理
	const [formState, setFormState] = useState<Record<string, string | number>>(() => {
		const initialState: Record<string, string | number> = {};
		formFields.forEach(field => {
			if (field.type === 'picker') {
				// 确保picker字段始终有一个值，避免从未控制到受控的转换
				initialState[field.name] = field.selectedKey || '';
			} else if (field.type === 'number' && field.defaultValue !== undefined) {
				initialState[field.name] = field.defaultValue as number;
			} else if (field.type === 'text' && field.defaultValue !== undefined) {
				initialState[field.name] = field.defaultValue as string;
			} else if (field.type === 'text') {
				// 确保text字段始终有一个值，避免从未控制到受控的转换
				initialState[field.name] = field.textValue || '';
			}
		});
		return initialState;
	});

	// 强制重新渲染状态（用于响应终极PK阶段变化）
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const [_forceUpdateTrigger, setForceUpdateTrigger] = useState<number>(0);

	// AlertDialog 状态管理
	const [alertDialogState, setAlertDialogState] = useState<{
		isOpen: boolean;
		title: string;
		message: string;
	}>({
		isOpen: false,
		title: "",
		message: "",
	});

	// 监听外部重置信号
	useEffect(() => {
		if (resetFieldName && resetTimestamp) {
			const field = formFields.find(f => f.name === resetFieldName);
			if (field && field.type === 'picker') {
				// 重置对应字段为空字符串，不触发onSelectionChange回调
				setFormState(prev => ({
					...prev,
					[resetFieldName]: ''
				}));
			}
		}
	}, [resetFieldName, resetTimestamp, formFields]);

	// 监听终极PK阶段变化事件，触发按钮状态重新计算
	useEffect(() => {
		let debounceTimer: NodeJS.Timeout | null = null;

		const handleStageChange = () => {
			// 检查当前按钮组是否包含终极PK相关按钮
			const hasUltimatePKButtons = title === "正方控制" || title === "反方控制";
			if (hasUltimatePKButtons) {
				// 清除之前的定时器
				if (debounceTimer) {
					clearTimeout(debounceTimer);
				}
				// 防抖处理：延迟50ms触发重新渲染
				debounceTimer = setTimeout(() => {
					setForceUpdateTrigger(prev => prev + 1);
					debounceTimer = null;
				}, 50);
			}
		};

		// 添加事件监听器
		window.addEventListener('ultimatePKStageChanged', handleStageChange);

		// 清理函数
		return () => {
			window.removeEventListener('ultimatePKStageChanged', handleStageChange);
			if (debounceTimer) {
				clearTimeout(debounceTimer);
			}
		};
	}, [title]);

	// 监听formFields的defaultValue变化，实时更新表单状态
	useEffect(() => {
		// 使用 setTimeout 将状态更新延迟到下一个事件循环，避免在渲染过程中更新状态
		const timeoutId = setTimeout(() => {
			formFields.forEach(field => {
				if (field.type === 'number' && field.defaultValue !== undefined) {
					// 检查当前值是否与新的defaultValue不同
					setFormState(prev => {
						if (prev[field.name] !== field.defaultValue) {
							console.log(`[SidebarButtonGroup] 更新字段 ${field.name}: ${prev[field.name]} -> ${field.defaultValue}`);
							return {
								...prev,
								[field.name]: field.defaultValue as number
							};
						}
						return prev;
					});
				} else if (field.type === 'text' && field.defaultValue !== undefined) {
					// 检查当前值是否与新的defaultValue不同
					setFormState(prev => {
						if (prev[field.name] !== field.defaultValue) {
							console.log(`[SidebarButtonGroup] 更新字段 ${field.name}: ${prev[field.name]} -> ${field.defaultValue}`);
							return {
								...prev,
								[field.name]: field.defaultValue as string
							};
						}
						return prev;
					});
				}
			});
		}, 0);

		return () => clearTimeout(timeoutId);
	}, [formFields]);

	/**
	 * 处理表单字段值变更
	 * @param fieldName 字段名称
	 * @param value 新值
	 */
	const handleFieldChange = (fieldName: string, value: string | number) => {
		setFormState(prev => ({
			...prev,
			[fieldName]: value
		}));
	};

	/**
	 * 处理"下一个"按钮的选择切换逻辑
	 * @param fieldName 要切换的字段名称
	 */
	const handleNextSelection = (fieldName: string) => {
		const field = formFields.find(f => f.name === fieldName);
		if (!field || !field.options || field.options.length === 0) {
			console.warn(`字段 ${fieldName} 没有可用选项`);
			return;
		}

		const currentValue = formState[fieldName] as string;
		const currentIndex = field.options.findIndex(option => option.key === currentValue);

		if (currentIndex === -1) {
			// 如果当前没有选择，选择第一个选项
			const firstOption = field.options[0];
			handleFieldChange(fieldName, firstOption.key);
			// 触发字段的onSelectionChange回调，确保状态同步到全局
			field.onSelectionChange?.(firstOption.key);
		} else if (currentIndex === field.options.length - 1) {
			// 如果已经是最后一个选项，显示 AlertDialog 提示
			setAlertDialogState({
				isOpen: true,
				title: "切换提示",
				message: `已经是最后一个选项: ${field.options[currentIndex].label}`,
			});
		} else {
			// 切换到下一个选项
			const nextOption = field.options[currentIndex + 1];
			handleFieldChange(fieldName, nextOption.key);
			// 触发字段的onSelectionChange回调，确保状态同步到全局
			field.onSelectionChange?.(nextOption.key);
		}
	};
	/**
	 * 获取按钮的最终样式配置
	 * @param button 按钮配置
	 * @returns 包含variant、className和isDisabled的样式配置
	 */
	const getButtonFinalStyle = (button: ButtonConfig) => {
		// 获取按钮的禁用状态，支持getter方法
		const getIsDisabled = (): boolean | undefined => {
			try {
				// 检查isDisabled是否为getter函数（通过检查descriptor）
				const descriptor = Object.getOwnPropertyDescriptor(button, 'isDisabled');
				if (descriptor && typeof descriptor.get === 'function') {
					return descriptor.get.call(button);
				}
				// 检查isDisabled是否为函数
				if (typeof button.isDisabled === 'function') {
					return button.isDisabled();
				}
				// 否则直接返回属性值
				return button.isDisabled;
			} catch (error) {
				// 如果getter方法调用失败，记录错误并返回false（不禁用）
				console.warn('[SidebarButtonGroup] 获取按钮禁用状态时发生错误:', error);
				return false;
			}
		};

		// 如果指定了styleType，使用样式系统
		if (button.styleType) {
			const styleConfig = getButtonStyleConfig(button.styleType);
			const buttonIsDisabled = getIsDisabled();
			return {
				variant: button.variant || styleConfig.variant, // 手动指定的variant优先
				className:
					[styleConfig.className, button.className].filter(Boolean).join(" ") ||
					undefined,
				isDisabled: buttonIsDisabled !== undefined ? buttonIsDisabled : styleConfig.isDisabled,
			};
		}

		// 否则使用传统方式
		return {
			variant: button.variant || "accent",
			className: button.className,
			isDisabled: getIsDisabled(),
		};
	};

	// 动画配置
	const shouldAnimate = animated && !reduceMotion;
	const animationConfig = {
		duration: duration || sidebarAnimation.groupSwitch.duration,
		ease: sidebarAnimation.groupSwitch.ease,
		...transition
	};

	return (
		<motion.div
			initial={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 0, x: 20 } : undefined}
			animate={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 1, x: 0 } : undefined}
			exit={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 0, x: -20 } : undefined}
			transition={shouldAnimate ? animationConfig : undefined}
			style={{ width: '100%' }}
		>
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				height="auto"
				width="auto"
				alignSelf="stretch"
				padding="size-200"
				UNSAFE_style={UNSAFE_style}
			>
				{/* 标题区域 */}
				<View
					marginBottom="size-100"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
					}}
				>
					<Header
						UNSAFE_style={{
							fontSize: "16px",
							fontWeight: "bold",
							lineHeight: "1.5",
						}}
					>
						{title}
					</Header>
					<TooltipTrigger delay={0} placement="top">
						<ActionButton isQuiet>
							<Info
								aria-label="Information"
								UNSAFE_style={{
									width: "12px",
									height: "12px",
								}}
							/>
						</ActionButton>
						<Tooltip>{tooltipContent}</Tooltip>
					</TooltipTrigger>
				</View>

				{/* 动态渲染内容区域 */}
				{renderOrder.map((item, orderIndex) => {
					switch (item) {
						case "buttons":
							return (
								<React.Fragment key={`buttons-${orderIndex}`}>
									{/* 换行（如果需要） */}
									{showBreakBeforeButtons && <br />}
									{/* 按钮区域 */}
									{buttons.map((button, index) => {
										const buttonStyle = getButtonFinalStyle(button);

										// 为"下一个"按钮、"显示 XX"按钮和"开启打分"按钮创建动态处理器
										const getButtonHandler = () => {
											if (button.dynamicHandler) {
												// 检查是否是需要验证的按钮类型
												if (button.text.startsWith("显示") || button.text === "开启打分") {
													return () => {
														// 找到对应的 picker 字段
														const pickerField = formFields.find(field => field.type === 'picker');
														if (pickerField) {
															const currentSelection = formState[pickerField.name] as string;
															if (!currentSelection || currentSelection === '') {
																// 没有选择时显示提示对话框
																let displayType = "";
																let dataSourceType = "";

																if (button.text.includes("领导")) {
																	displayType = "领导";
																	dataSourceType = "领导";
																} else if (button.text.includes("选手")) {
																	displayType = "选手";
																	dataSourceType = "选手";
																} else if (button.text.includes("奖项")) {
																	displayType = "奖项";
																	dataSourceType = "奖项";
																} else if (button.text === "显示标题") {
																	displayType = "节目";
																	dataSourceType = "节目显示";
																} else if (button.text === "开启打分") {
																	displayType = "选手";
																	dataSourceType = "选手显示";
																} else {
																	displayType = "内容";
																	dataSourceType = "数据源";
																}

																setAlertDialogState({
																	isOpen: true,
																	title: "选择提示",
																	message: `请先从侧边栏选择要显示的${displayType}（需要先选择"${dataSourceType}"数据源）`,
																});
																return;
															}
														}
														// 有选择时执行原有逻辑
														button.dynamicHandler!(formState);
													};
												} else {
													// 其他 dynamicHandler 按钮直接执行
													return () => button.dynamicHandler!(formState);
												}
											} else if (button.text === "下一个") {
												// 自动为"下一个"按钮绑定切换逻辑
												const pickerField = formFields.find(field => field.type === 'picker');
												if (pickerField) {
													return () => handleNextSelection(pickerField.name);
												}
											}
											return button.onPress;
										};

										return (
											<Button
												key={`${button.text}-${index}`}
												variant={buttonStyle.variant}
												onPress={getButtonHandler()}
												isDisabled={buttonStyle.isDisabled}
												UNSAFE_className={buttonStyle.className}
											>
												{button.text}
											</Button>
										);
									})}
								</React.Fragment>
							);

						case "formFields":
							return (
								<React.Fragment key={`formFields-${orderIndex}`}>
									{/* 表单字段区域 */}
									{formFields.length > 0 && (
										<Form validationBehavior="native" maxWidth="size-2000">
											{formFields.map((field, index) => {
												// 根据字段类型渲染不同的组件
												if (field.type === 'picker') {
													return (
														<Picker
															key={`${field.name}-${index}`}
															label={field.label}
															placeholder={field.placeholder || "请选择"}
															selectedKey={formState[field.name] as string || ''}
															onSelectionChange={(key) => {
																if (key) {
																	handleFieldChange(field.name, key as string);
																	// 调用字段配置的回调函数（如果存在）
																	field.onSelectionChange?.(key as string);
																}
															}}
															UNSAFE_className="1custom-picker"
															marginBottom="size-200"
														>
															{(field.options || []).map((option) => (
																<Item key={option.key}>{option.label}</Item>
															))}
														</Picker>
													);
												} else if (field.type === 'text') {
													return (
														<TextField
															key={`${field.name}-${index}`}
															label={field.label}
															name={field.name}
															value={formState[field.name] as string || field.textValue || ''}
															onChange={(value) => {
																handleFieldChange(field.name, value);
																field.onTextChange?.(value);
															}}
															marginBottom="size-200"
														/>
													);
												} else {
													// 默认为 NumberField
													return (
														<NumberField
															key={`${field.name}-${index}`}
															label={field.label}
															name={field.name}
															value={formState[field.name] as number || (field.defaultValue as number) || 0}
															onChange={(value) => handleFieldChange(field.name, value)}
															minValue={field.minValue}
															maxValue={field.maxValue}
														/>
													);
												}
											})}
										</Form>
									)}
								</React.Fragment>
							);

						case "additionalButtons":
							return (
								<React.Fragment key={`additionalButtons-${orderIndex}`}>
									{/* 换行（如果需要额外按钮前换行） */}
									{showBreakBeforeAdditionalButtons &&
										additionalButtons.length > 0 && <br />}
									{/* 额外按钮区域 */}
									{additionalButtons.map((button, index) => {
										const buttonStyle = getButtonFinalStyle(button);
										return (
											<Button
												key={`additional-${button.text}-${index}`}
												variant={buttonStyle.variant}
												onPress={button.onPress}
												isDisabled={buttonStyle.isDisabled}
												UNSAFE_className={buttonStyle.className}
											>
												{button.text}
											</Button>
										);
									})}
								</React.Fragment>
							);

						default:
							return null;
					}
				})}
			</View>

			{/* AlertDialog 组件 */}
			<DialogContainer onDismiss={() => setAlertDialogState(prev => ({ ...prev, isOpen: false }))}>
				{alertDialogState.isOpen && (
					<AlertDialog
						title={alertDialogState.title}
						variant="warning"
						primaryActionLabel="确定"
						onPrimaryAction={() => setAlertDialogState(prev => ({ ...prev, isOpen: false }))}
						onCancel={() => setAlertDialogState(prev => ({ ...prev, isOpen: false }))}
					>
						{alertDialogState.message}
					</AlertDialog>
				)}
			</DialogContainer>
		</motion.div>
	);
};

/**
 * ActionGroup 侧边栏组件Props接口
 */
export interface SidebarActionGroupProps extends SidebarAnimationProps {
	/** ActionGroup 配置 */
	config: ActionGroupButtonConfig;
	/** 自定义样式 */
	UNSAFE_style?: React.CSSProperties;
}

/**
 * ActionGroup 侧边栏组件
 *
 * 专门用于渲染 ActionGroup 配置的组件
 */
export const SidebarActionGroup: React.FC<SidebarActionGroupProps> = ({
	config,
	UNSAFE_style,
	// 动画相关props
	animated = true,
	duration,
	transition,
	reduceMotion = false,
	groupSwitchAnimation = { enabled: true, direction: 'right' },
}) => {
	// 确认对话框状态管理
	const [confirmationDialog, setConfirmationDialog] = useState<{
		isOpen: boolean;
		targetKey: string;
		targetLabel: string;
		config: import('../config/buttonGroupConfigurations').ConfirmationDialogConfig;
	} | null>(null);

	// 受控选择状态管理（用于确认对话框场景）
	const [selectedKeys, setSelectedKeys] = useState<string[]>(() => {
		// 如果有确认对话框配置，使用受控状态；否则使用默认值
		return config.confirmationDialog ? config.defaultSelectedKeys : [];
	});

	// 标记是否正在执行确认操作（避免重复触发确认对话框）
	const [isConfirming, setIsConfirming] = useState<boolean>(false);

	// 动画配置 - 与 SidebarButtonGroup 保持一致
	const shouldAnimate = animated && !reduceMotion;
	const animationConfig = {
		duration: duration || sidebarAnimation.groupSwitch.duration,
		ease: sidebarAnimation.groupSwitch.ease,
		...transition
	};

	return (
		<motion.div
			initial={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 0, x: 20 } : undefined}
			animate={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 1, x: 0 } : undefined}
			exit={shouldAnimate && groupSwitchAnimation.enabled ? { opacity: 0, x: -20 } : undefined}
			transition={shouldAnimate ? animationConfig : undefined}
			style={{ width: '100%' }}
		>
			<View
				backgroundColor="gray-100"
				borderRadius="medium"
				height="auto"
				width="auto"
				alignSelf="stretch"
				padding="size-200"
				UNSAFE_style={UNSAFE_style}
			>
				{/* 标题区域 */}
				<View
					marginBottom="size-100"
					UNSAFE_style={{
						display: "flex",
						alignItems: "center",
					}}
				>
					<Header
						UNSAFE_style={{
							fontSize: "16px",
							fontWeight: "bold",
							lineHeight: "1.5",
						}}
					>
						{config.title}
					</Header>
					<TooltipTrigger delay={0} placement="top">
						<ActionButton isQuiet>
							<Info
								aria-label="Information"
								UNSAFE_style={{
									width: "12px",
									height: "12px",
								}}
							/>
						</ActionButton>
						<Tooltip>{config.tooltipContent}</Tooltip>
					</TooltipTrigger>
				</View>

				{/* ActionGroup 区域 */}
				<ActionGroup
					selectionMode={config.selectionMode}
					disallowEmptySelection={config.disallowEmptySelection}
					{...(config.confirmationDialog
						? { selectedKeys: selectedKeys }
						: { defaultSelectedKeys: config.defaultSelectedKeys }
					)}
					onSelectionChange={(keys) => {
						// 调试日志：onSelectionChange 被调用
						console.log(`[SidebarActionGroup] onSelectionChange 被调用`, {
							keys: Array.from(keys),
							hasConfirmationDialog: !!config.confirmationDialog,
							isConfirming,
							selectedKeys,
							timestamp: Date.now()
						});

						// 将 Selection 转换为单个 key
						const selectedKey = Array.from(keys)[0] as string;
						if (selectedKey) {
							// 调试日志：处理选择的 key
							console.log(`[SidebarActionGroup] 处理选择的 key: ${selectedKey}`, {
								hasConfirmationDialog: !!config.confirmationDialog,
								isConfirming,
								isAlreadySelected: selectedKeys.includes(selectedKey),
								timestamp: Date.now()
							});

							// 检查是否需要显示确认对话框
							if (config.confirmationDialog) {
								// 如果正在执行确认操作，直接返回（避免重复触发）
								if (isConfirming) {
									console.log(`[SidebarActionGroup] 正在执行确认操作，跳过`);
									return;
								}

								// 检查是否是当前已选中的项（避免重复确认）
								if (selectedKeys.includes(selectedKey)) {
									console.log(`[SidebarActionGroup] 已经选中，跳过: ${selectedKey}`);
									return; // 已经选中，无需处理
								}

								// 找到目标选项的标签
								const targetItem = config.items.find(item => item.key === selectedKey);
								if (targetItem) {
									// 调试日志：显示确认对话框
									console.log(`[SidebarActionGroup] 显示确认对话框`, {
										targetKey: selectedKey,
										targetLabel: targetItem.label,
										timestamp: Date.now()
									});

									// 显示确认对话框，但不更新选择状态
									setConfirmationDialog({
										isOpen: true,
										targetKey: selectedKey,
										targetLabel: targetItem.label,
										config: config.confirmationDialog
									});
									return; // 阻止直接执行选择变更
								}
							} else {
								// 没有确认对话框配置，直接执行选择变更
								console.log(`[SidebarActionGroup] 直接执行选择变更: ${selectedKey}`);
								config.onSelectionChange(selectedKey);
							}
						}
					}}
					UNSAFE_style={{
						width: "100%",
					}}
				>
					{config.items.map((item) => (
						<Item key={item.key}>{item.label}</Item>
					))}
				</ActionGroup>

				{/* 确认对话框 */}
				{confirmationDialog && (
					<DialogContainer onDismiss={() => setConfirmationDialog(null)}>
						{confirmationDialog.isOpen && (
							<AlertDialog
								title={confirmationDialog.config.title}
								variant={confirmationDialog.config.variant || "confirmation"}
								primaryActionLabel={confirmationDialog.config.confirmLabel || "确认"}
								secondaryActionLabel={confirmationDialog.config.cancelLabel || "取消"}
								onPrimaryAction={() => {
									try {
										// 调试日志：确认对话框确认按钮被点击
										console.log(`[SidebarActionGroup] 确认对话框确认按钮被点击`, {
											targetKey: confirmationDialog.targetKey,
											targetLabel: confirmationDialog.targetLabel,
											timestamp: Date.now()
										});

										// 用户确认，设置确认标记，更新选择状态并执行原始的选择变更回调
										setIsConfirming(true);
										setSelectedKeys([confirmationDialog.targetKey]);

										// 调试日志：即将调用 onSelectionChange
										console.log(`[SidebarActionGroup] 即将调用 onSelectionChange`, {
											targetKey: confirmationDialog.targetKey,
											hasOnSelectionChange: !!config.onSelectionChange,
											timestamp: Date.now()
										});

										config.onSelectionChange(confirmationDialog.targetKey);

										// 调试日志：onSelectionChange 调用完成
										console.log(`[SidebarActionGroup] onSelectionChange 调用完成`, {
											targetKey: confirmationDialog.targetKey,
											timestamp: Date.now()
										});

										setConfirmationDialog(null);
										// 使用 setTimeout 在下一个事件循环中重置确认标记
										setTimeout(() => setIsConfirming(false), 0);
									} catch (error) {
										// 调试日志：捕获异常
										console.error(`[SidebarActionGroup] onPrimaryAction 异常`, {
											error: error instanceof Error ? error.message : String(error),
											stack: error instanceof Error ? error.stack : undefined,
											targetKey: confirmationDialog.targetKey,
											timestamp: Date.now()
										});
									}
								}}
								onSecondaryAction={() => {
									// 用户取消，关闭对话框但不执行选择变更，保持原有选择状态
									setConfirmationDialog(null);
								}}
							>
								{confirmationDialog.config.content.replace('{targetLabel}', confirmationDialog.targetLabel)}
							</AlertDialog>
						)}
					</DialogContainer>
				)}
			</View>
		</motion.div>
	);
};
