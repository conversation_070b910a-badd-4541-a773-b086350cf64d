import React, { useState, useRef, useCallback } from "react";
import {
	View,
	Flex,
	Text,
	ActionButton,
} from "@adobe/react-spectrum";
import ChevronUp from "@spectrum-icons/workflow/ChevronUp";
import ChevronDown from "@spectrum-icons/workflow/ChevronDown";
import Delete from "@spectrum-icons/workflow/Delete";
import Refresh from "@spectrum-icons/workflow/Refresh";
import { motion, AnimatePresence } from "motion/react";
import { slideVerticalVariants } from "../config/motionConfig";
import type { ConsolePanelAnimationProps } from "../types/motion";
import "./ConsolePanel.css";

// 控台日志条目接口
interface ConsoleLogEntry {
	id: string;
	timestamp: Date;
	level: "info" | "warning" | "error" | "success" | "send" | "get";
	message: string;
	details?: string;
}

// 控台面板属性接口
interface ConsolePanelProps extends ConsolePanelAnimationProps {
	/** 是否默认展开 */
	defaultExpanded?: boolean;
	/** 默认高度 */
	defaultHeight?: number;
	/** 最小高度 */
	minHeight?: number;
	/** 最大高度 */
	maxHeight?: number;
	/** 日志条目 */
	logs?: ConsoleLogEntry[];
	/** 清空日志回调 */
	onClearLogs?: () => void;
	/** 刷新状态回调 */
	onRefreshStatus?: () => void;
	/** 是否为悬浮窗模式 */
	floating?: boolean;
}

/**
 * 控台状态面板组件
 * 类似 VS Code 终端面板的设计，支持折叠/展开和高度调节
 */
export function ConsolePanel({
	defaultExpanded = false,
	defaultHeight = 200,
	minHeight = 100,
	maxHeight = 400,
	logs = [],
	onClearLogs,
	onRefreshStatus,
	floating = false,
	// 动画相关props - 仅用于内部日志条目
	animated = true,
	delay,
	reduceMotion = false,
	logEntryAnimation = { enabled: true, staggerDelay: 0.05 },
}: ConsolePanelProps) {
	// 面板展开/折叠状态
	const [isExpanded, setIsExpanded] = useState(defaultExpanded);

	// 面板高度状态
	const [panelHeight, setPanelHeight] = useState(defaultHeight);

	// 是否正在拖拽调整高度
	const [isResizing, setIsResizing] = useState(false);

	// 面板容器引用
	const panelRef = useRef<HTMLDivElement>(null);

	// 拖拽开始位置
	const dragStartY = useRef<number>(0);
	const dragStartHeight = useRef<number>(0);

	// 处理拖拽移动
	const handleResizeMove = useCallback(
		(e: MouseEvent) => {
			const deltaY = dragStartY.current - e.clientY; // 向上拖拽为正值
			const newHeight = Math.max(
				minHeight,
				Math.min(maxHeight, dragStartHeight.current + deltaY),
			);

			setPanelHeight(newHeight);
		},
		[minHeight, maxHeight],
	);

	// 处理拖拽结束
	const handleResizeEnd = useCallback(() => {
		setIsResizing(false);

		// 移除全局鼠标事件监听
		document.removeEventListener("mousemove", handleResizeMove);
		document.removeEventListener("mouseup", handleResizeEnd);

		// 恢复用户选择和光标样式
		document.body.style.userSelect = '';
		document.body.style.cursor = '';
	}, [handleResizeMove]);

	// 处理拖拽开始
	const handleResizeStart = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation(); // 防止事件冒泡到标题栏的点击事件

			// 立即设置拖拽状态，确保动画被禁用
			setIsResizing(true);
			dragStartY.current = e.clientY;
			dragStartHeight.current = panelHeight;

			// 添加全局鼠标事件监听
			document.addEventListener("mousemove", handleResizeMove);
			document.addEventListener("mouseup", handleResizeEnd);

			// 添加用户选择禁用，提升拖拽体验
			document.body.style.userSelect = 'none';
			document.body.style.cursor = 'ns-resize';
		},
		[panelHeight, handleResizeMove, handleResizeEnd],
	);

	// 切换展开/折叠状态
	const toggleExpanded = () => {
		setIsExpanded(!isExpanded);
	};

	// 动画配置 - 仅用于内部日志条目动画
	const shouldAnimate = animated && !reduceMotion && !isResizing;





	// 格式化时间戳 - 精确到秒
	const formatTimestamp = (timestamp: Date) => {
		const hours = timestamp.getHours().toString().padStart(2, '0');
		const minutes = timestamp.getMinutes().toString().padStart(2, '0');
		const seconds = timestamp.getSeconds().toString().padStart(2, '0');

		return `${hours}:${minutes}:${seconds}`;
	};



	return (
		<div
			ref={panelRef}
			className={`console-panel ${isExpanded ? "expanded" : "collapsed"} ${isResizing ? "resizing" : ""} ${floating ? "floating" : ""}`}
			style={{
				// 直接控制高度，移除 motion 动画干扰
				height: isExpanded ? `${panelHeight}px` : "40px",
				transition: isResizing ? "none" : "height 0.3s ease", // 拖拽时禁用过渡
			}}
		>
			{/* 拖拽调整高度的分隔条 */}
			{isExpanded && (
				<div
					className="console-panel-resize-handle"
					onMouseDown={handleResizeStart}
				/>
			)}

			{/* 标题栏 */}
			<div
				className="console-panel-header console-panel-header-clickable"
				style={{
					cursor: "pointer",
					position: "relative",
					backgroundColor: "var(--spectrum-global-color-gray-200)",
					height: "32px",
					borderRadius: "8px 8px 0 0",
				}}
				// 整个标题栏可点击，触发展开/折叠
				onClick={toggleExpanded}
				role="button"
				tabIndex={0}
				aria-label={isExpanded ? "折叠控台面板" : "展开控台面板"}
				onKeyDown={(e: React.KeyboardEvent) => {
					if (e.key === "Enter" || e.key === " ") {
						e.preventDefault();
						toggleExpanded();
					}
				}}
			>
				<Flex
					justifyContent="space-between"
					alignItems="center"
					height="100%"
					width="100%"
				>
					{/* 左侧：标题和展开/折叠图标 */}
					<Flex alignItems="center" gap="size-100">
						{/* 展开/折叠图标 - 仅作为视觉指示器 */}
						<View UNSAFE_className="console-panel-toggle-icon">
							{isExpanded ? <ChevronDown /> : <ChevronUp />}
						</View>
						<Text UNSAFE_className="console-panel-title">控台状态</Text>
						{logs.length > 0 && (
							<View

								UNSAFE_className="console-panel-badge"
							>
								<Text UNSAFE_className="console-panel-badge-text">
									{logs.length}
								</Text>
							</View>
						)}
					</Flex>

					{/* 右侧：操作按钮 */}
					{isExpanded && (
						<div
							style={{
								display: "flex",
								alignItems: "center",
								gap: "4px",
								position: "relative",
								zIndex: 10, // 确保操作按钮在标题栏点击事件之上
							}}
							onClick={(e) => e.stopPropagation()} // 阻止事件冒泡到标题栏
						>
							<ActionButton
								isQuiet
								onPress={() => {
									if (onRefreshStatus) {
										onRefreshStatus();
									}
								}}
								UNSAFE_className="console-panel-action"
							>
								<Refresh />
							</ActionButton>
							<ActionButton
								isQuiet
								onPress={() => {
									if (onClearLogs) {
										onClearLogs();
									}
								}}
								UNSAFE_className="console-panel-action"
							>
								<Delete />
							</ActionButton>
						</div>
					)}
				</Flex>
			</div>

			{/* 内容区域 */}
			{isExpanded && (
				<View
					UNSAFE_className="console-panel-content"
					UNSAFE_style={{
						// 精确计算内容区域高度，避免黑边问题
						height: `${Math.max(0, panelHeight - 36)}px`, // 减去标题栏高度，确保非负
						overflow: "auto",
						// 确保背景完全填充
						backgroundColor: "#181818",
					}}
				>
					{logs.length === 0 ? (
						// 空状态
						<View UNSAFE_className="console-panel-empty">
							<Text UNSAFE_className="console-panel-empty-text">
								暂无控台信息
							</Text>
						</View>
					) : (
						// 日志列表 - Warp 风格三列布局
						<View padding="size-100">
							<AnimatePresence>
								{logs.map((log, index) => (
									<motion.div
										key={log.id}
										className="console-panel-log-entry"
										initial={shouldAnimate && logEntryAnimation.enabled ? slideVerticalVariants.hidden : undefined}
										animate={shouldAnimate && logEntryAnimation.enabled ? slideVerticalVariants.visible : undefined}
										exit={shouldAnimate && logEntryAnimation.enabled ? slideVerticalVariants.exit : undefined}
										transition={shouldAnimate && logEntryAnimation.enabled ? {
											...slideVerticalVariants.visible.transition,
											delay: delay || (index * logEntryAnimation.staggerDelay)
										} : undefined}
									>
										{/* 三列网格布局 */}
										<div className="console-panel-log-content">
											{/* 时间戳列 */}
											<div className="console-panel-log-timestamp-column">
												<Text UNSAFE_className="console-panel-log-timestamp">
													{formatTimestamp(log.timestamp)}
												</Text>
											</div>

											{/* 级别标签列 */}
											<div className="console-panel-log-level-column">
												<Text
													UNSAFE_className={`console-panel-log-level console-panel-log-level-${log.level}`}
												>
													{`[${log.level.toUpperCase()}]`}
												</Text>
											</div>

											{/* 消息内容列 */}
											<div className="console-panel-log-message-column">
												<Text
													UNSAFE_className="console-panel-log-message"
													UNSAFE_style={{
														// 支持 HTML 内容用于语法高亮
														whiteSpace: 'pre-wrap'
													}}
												>
													{log.message}
												</Text>
												{log.details && (
													<Text UNSAFE_className="console-panel-log-details">
														{log.details}
													</Text>
												)}
											</div>
										</div>
									</motion.div>
								))}
							</AnimatePresence>
						</View>
					)}
				</View>
			)}
		</div>
	);
}

export default ConsolePanel;
