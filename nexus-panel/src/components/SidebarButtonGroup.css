/* 侧边栏按钮组样式系统 */

/* ===== 基础样式 ===== */

/* 侧边栏按钮组容器样式增强 */

/* 所有侧边栏按钮的通用样式 */
.sidebar-button-base {
  margin: 4px 6px !important; /* 增加垂直和水平间距 */
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  border-radius: 6px;
  min-height: 32px;
  font-size: 14px;
}

/* ===== 主要操作按钮样式 ===== */
.sidebar-button-primary-action {
  /* 默认状态 - 明亮的蓝色，清晰可见 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #1f6feb 0%, #0969da 100%) !important;
  border: 1px solid transparent !important;
  border-color: #388bfd !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-primary-action:hover {
  /* Hover状态 - 更亮的蓝色，增强交互感 */
  background: linear-gradient(135deg, #1f6feb 0%, #2287fb 100%);
  border: 1px solid #4dabf7;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 133, 244, 0.4);
  cursor: pointer;
}

.sidebar-button-primary-action:active {
  /* Active状态 - 最深的蓝色，明确的按下反馈 */
  background: linear-gradient(135deg, #0550ae 0%, #033d8b 100%) !important;
  color: #e0e7ff !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(5, 80, 174, 0.4) inset,
    0 1px 3px rgba(5, 80, 174, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 导航操作按钮样式 ===== */
.sidebar-button-navigation {
  /* 默认状态 - 清晰的紫色，适合导航功能 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%) !important;
  border: 1px solid #7c3aed !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-navigation:hover {
  /* Hover状态 - 更深的紫色，增强交互感 */
  background: linear-gradient(135deg, #6d28d9 0%, #7c3aed 100%);
  border: 1px solid #6d28d9;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(109, 40, 217, 0.3);
  cursor: pointer;
}

.sidebar-button-navigation:active {
  /* Active状态 - 最深的紫色，明确的按下反馈 */
  background: linear-gradient(135deg, #581c87 0%, #6d28d9 100%) !important;
  border: 1px solid #581c87 !important;
  color: #e9d5ff !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(88, 28, 135, 0.4) inset,
    0 1px 3px rgba(88, 28, 135, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 内容展示按钮样式 ===== */
.sidebar-button-content-display {
  /* 默认状态 - 清新的绿色，适合内容展示功能 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #238636 0%, #1a7f37 100%) !important;
  border: 1px solid transparent !important;
  border-color: #2ea043 !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-content-display:hover {
  /* Hover状态 - 更深的绿色，增强交互感 */
  background: linear-gradient(135deg, #15803d 0%, #16a34a 100%);
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(21, 128, 61, 0.3);
  cursor: pointer;
}

.sidebar-button-content-display:active {
  /* Active状态 - 最深的绿色，明确的按下反馈 */
  background: linear-gradient(135deg, #14532d 0%, #15803d 100%) !important;

  color: #dcfce7 !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(20, 83, 45, 0.4) inset,
    0 1px 3px rgba(20, 83, 45, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 数据操作按钮样式 ===== */
.sidebar-button-data-operation {
  /* 默认状态 - 专业的蓝色，适合数据操作功能 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: 1px solid #60a5fa !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-data-operation:hover {
  /* Hover状态 - 更深的蓝色，增强交互感 */
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  border: 1px solid #3b82f6;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
  cursor: pointer;
}

.sidebar-button-data-operation:active {
  /* Active状态 - 最深的蓝色，明确的按下反馈 */
  background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%) !important;
  border: 1px solid #1d4ed8 !important;
  color: #dbeafe !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(29, 78, 216, 0.4) inset,
    0 1px 3px rgba(29, 78, 216, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 特殊功能按钮样式 ===== */
.sidebar-button-special-function {
  /* 默认状态 - 醒目的金黄色，适合特殊功能 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #bf8700 0%, #9a6700 100%) !important;
  border: 1px solid #d29922 !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-special-function:hover {
  /* Hover状态 - 更亮的金黄色，增强交互感 */
  background: linear-gradient(135deg, #d4a017 0%, #bf8700 100%);
  border: 1px solid #e5b429;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(212, 160, 23, 0.4);
  cursor: pointer;
}

.sidebar-button-special-function:active {
  /* Active状态 - 最深的金黄色，明确的按下反馈 */
  background: linear-gradient(135deg, #8b6914 0%, #7a5c0f 100%) !important;

  color: #fef3c7 !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(139, 105, 20, 0.4) inset,
    0 1px 3px rgba(139, 105, 20, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 危险操作按钮样式 ===== */
.sidebar-button-danger-action {
  /* 默认状态 - 基础配色规范 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #da3633 0%, #cf222e 100%) !important;
  border: 1px solid transparent !important;
  border-color: #f85149 !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-danger-action:hover {
  /* Hover状态 - 在基础配色基础上适当变亮，增强视觉反馈 */
  background: linear-gradient(135deg, #f85149 0%, #da3633 100%);
  border: 1px solid #ff6b6b;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(248, 81, 73, 0.4);
  cursor: pointer;
}

.sidebar-button-danger-action:active {
  /* Active状态 - 在基础配色基础上变为更深的色调，提供明确的按下反馈 */
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%) !important;

  color: #fecaca !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(185, 28, 28, 0.4) inset,
    0 1px 3px rgba(185, 28, 28, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 信息展示按钮样式 ===== */
.sidebar-button-info-display {
  /* 默认状态 - 柔和的青色，适合信息展示 */
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%) !important;
  border: 1px solid #0f766e !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 2px 4px rgba(15, 118, 110, 0.2) !important;
  transition: all 0.15s ease-in-out !important;
}

.sidebar-button-info-display:hover {
  /* Hover状态 - 更深的青色，增强交互感 */
  background: linear-gradient(135deg, #134e4a 0%, #0f766e 100%);
  border: 1px solid #134e4a;
  color: #ffffff;
  border-radius: 8px;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(19, 78, 74, 0.3);
  cursor: pointer;
}

.sidebar-button-info-display:active {
  /* Active状态 - 最深的青色，明确的按下反馈 */
  background: linear-gradient(135deg, #042f2e 0%, #134e4a 100%) !important;
  border: 1px solid #042f2e !important;
  color: #ccfbf1 !important;
  border-radius: 8px !important;
  transform: translateY(1px) !important;
  box-shadow:
    0 1px 2px rgba(4, 47, 46, 0.4) inset,
    0 1px 3px rgba(4, 47, 46, 0.2) !important;
  transition: all 0.1s ease-in-out !important;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .sidebar-button-base {
    margin: 3px 4px; /* 小屏幕下保持适当间距 */
    min-height: 28px;
    font-size: 13px;
  }

  /* 小屏幕下的按钮组间距调整 */
  .sidebar-button-group .spectrum-Button {
    margin: 4px 6px; /* 小屏幕下减少间距以节省空间 */
  }

  .sidebar-button-group .spectrum-Button + .spectrum-Button {
    margin-left: 6px;
  }

  .sidebar-button-group {
    padding: 2px; /* 小屏幕下减少容器内边距 */
  }
}

/* 中等屏幕优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar-button-group .spectrum-Button {
    margin: 5px 7px; /* 中等屏幕下的适中间距 */
  }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
  .sidebar-button-group .spectrum-Button {
    margin: 6px 8px; /* 大屏幕下保持较大间距 */
  }
}

/* ===== 深色主题适配 ===== */
@media (prefers-color-scheme: dark) {
  .sidebar-button-navigation {
    background: linear-gradient(
      135deg,
      var(--spectrum-global-color-gray-800) 0%,
      var(--spectrum-global-color-gray-700) 100%
    );
    border-color: var(--spectrum-global-color-gray-600);
    color: var(--spectrum-global-color-gray-100);
  }

  .sidebar-button-navigation:hover {
    background: linear-gradient(
      135deg,
      var(--spectrum-global-color-blue-800) 0%,
      var(--spectrum-global-color-blue-700) 100%
    );
    border-color: var(--spectrum-global-color-blue-500);
  }

  .sidebar-button-content-display {
    background: var(--spectrum-global-color-gray-800);
    border-color: var(--spectrum-global-color-gray-600);
    color: var(--spectrum-global-color-gray-200);
  }

  .sidebar-button-content-display:hover {
    background: var(--spectrum-global-color-gray-700);
    border-color: var(--spectrum-global-color-gray-500);
    color: var(--spectrum-global-color-gray-100);
  }

  .sidebar-button-info-display {
    background: var(--spectrum-global-color-gray-900);
    border-color: var(--spectrum-global-color-gray-700);
    color: var(--spectrum-global-color-gray-500);
  }
}

/* ===== 动画效果 ===== */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 活跃状态动画 */
.sidebar-button-active {
  animation: buttonPulse 2s infinite;
}

/* ===== 按钮组布局优化 ===== */
.sidebar-button-group .spectrum-Button {
  margin: 6px 8px; /* 增加按钮间距，提供更好的视觉分离 */
  flex: 0 0 auto;
}

/* 按钮组内的按钮换行处理 */
.sidebar-button-group .spectrum-Button + .spectrum-Button {
  margin-left: 8px; /* 增加相邻按钮的水平间距 */
}

/* 表单字段后的按钮组间距 */
.sidebar-button-group .spectrum-Form + * {
  margin-top: var(
    --spectrum-global-dimension-size-200
  ); /* 增加表单字段后的间距 */
}

/* 按钮组容器内部间距优化 */
.sidebar-button-group {
  padding: 4px; /* 为整个按钮组添加内边距 */
}

/* 按钮行之间的间距 */
.sidebar-button-group br + * {
  margin-top: 8px; /* 换行后的元素增加顶部间距 */
}

/* ===== 禁用状态样式 ===== */

/* 所有按钮的禁用状态通用样式 */
.spectrum-Button[disabled],
.spectrum-Button:disabled,
.spectrum-Button[aria-disabled='true'] {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
  background: var(--spectrum-global-color-gray-300);
  border-color: var(--spectrum-global-color-gray-400);
  color: var(--spectrum-global-color-gray-600);
  transform: none;
  box-shadow: none;
}

/* 特定样式类型的禁用状态 */
.sidebar-button-info-display[disabled],
.sidebar-button-info-display:disabled,
.sidebar-button-info-display[aria-disabled='true'] {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
  background: var(--spectrum-global-color-gray-200);
  border-color: var(--spectrum-global-color-gray-300);
  color: var(--spectrum-global-color-gray-500);
  transform: none;
  box-shadow: none;
}

/* 内容展示按钮的禁用状态 */
.sidebar-button-content-display[disabled],
.sidebar-button-content-display:disabled,
.sidebar-button-content-display[aria-disabled='true'] {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
  background: var(--spectrum-global-color-gray-300);
  border-color: var(--spectrum-global-color-gray-400);
  color: var(--spectrum-global-color-gray-600);
  transform: none;
  box-shadow: none;
}

/* 禁用状态下移除所有hover和active效果 */
.spectrum-Button[disabled]:hover,
.spectrum-Button:disabled:hover,
.spectrum-Button[aria-disabled='true']:hover,
.spectrum-Button[disabled]:active,
.spectrum-Button:disabled:active,
.spectrum-Button[aria-disabled='true']:active {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
  background: var(--spectrum-global-color-gray-300);
  border-color: var(--spectrum-global-color-gray-400);
  color: var(--spectrum-global-color-gray-600);
  transform: none;
  box-shadow: none;
}

/* 内容展示按钮禁用状态下的hover和active效果 */
.sidebar-button-content-display[disabled]:hover,
.sidebar-button-content-display:disabled:hover,
.sidebar-button-content-display[aria-disabled='true']:hover,
.sidebar-button-content-display[disabled]:active,
.sidebar-button-content-display:disabled:active,
.sidebar-button-content-display[aria-disabled='true']:active {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
  background: var(--spectrum-global-color-gray-300);
  border-color: var(--spectrum-global-color-gray-400);
  color: var(--spectrum-global-color-gray-600);
  transform: none;
  box-shadow: none;
}

/* 深色主题下的禁用状态 */
@media (prefers-color-scheme: dark) {
  .spectrum-Button[disabled],
  .spectrum-Button:disabled,
  .spectrum-Button[aria-disabled='true'] {
    background: var(--spectrum-global-color-gray-700);
    border-color: var(--spectrum-global-color-gray-600);
    color: var(--spectrum-global-color-gray-500);
  }
}
