// React 核心依赖导入
import React, { useState, useMemo, useEffect, useCallback } from "react";
// Adobe React Spectrum UI 组件库导入
import { View, Heading, Text } from "@adobe/react-spectrum";
// 音频播放器由ContentArea统一管理，此组件不再直接使用
// 导入类型
import type { ProcessedRulesIntroductionItem } from "../services/api/types";

/**
 * RuleIntroContent 组件的 Props 接口
 */
export interface RuleIntroContentProps {
	/** 规则介绍数据项 */
	rulesItem: ProcessedRulesIntroductionItem;
	/** 当前页码（从外部控制） */
	currentPage?: number;
	/** 页面切换回调 */
	onPageChange?: (page: number) => void;
	/** 图片加载错误回调 */
	onImageError?: (error: string) => void;
	/** 日志记录回调 */
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
	/** 自定义CSS类名 */
	className?: string;
	/** React Spectrum 样式 */
	UNSAFE_style?: React.CSSProperties;
}

/**
 * 分页内容处理：按 --- 分割内容
 */
const splitContentIntoPages = (content: string): string[] => {
	// 使用 --- 作为分页分隔符
	const pages = content.split('---');
	// 过滤掉空页面并去除首尾空白
	return pages.filter(page => page.trim().length > 0).map(page => page.trim());
};

/**
 * 处理Markdown格式的文本渲染（支持分页）
 * 支持 **加粗** 格式和 <br> 换行标签，加粗内容使用更深的颜色
 */
const renderMarkdownText = (text: string): React.ReactNode => {
	// 分割文本，处理 **加粗** 格式
	const parts = text.split(/(\*\*[^*]+\*\*)/g);

	return parts.map((part, index) => {
		// 检查是否为加粗格式
		if (part.startsWith('**') && part.endsWith('**')) {
			const boldText = part.slice(2, -2); // 移除 ** 标记
			return (
				<Text key={index} UNSAFE_style={{
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-800)' // 加粗内容使用更深的颜色
				}}>
					{boldText}
				</Text>
			);
		}

		// 处理 <br> 标签和换行符
		if (part.includes('<br>') || part.includes('\n')) {
			// 先处理 <br> 标签，再处理换行符
			const processedPart = part.replace(/<br\s*\/?>/gi, '\n');

			return processedPart.split('\n').map((line, lineIndex) => (
				<React.Fragment key={`${index}-${lineIndex}`}>
					{line}
					{lineIndex < processedPart.split('\n').length - 1 && <br />}
				</React.Fragment>
			));
		}

		return part;
	});
};

/**
 * RuleIntroContent - 规则介绍内容渲染组件
 *
 * 功能特性：
 * - A1 区域：显示规则标题
 * - B1 区域：显示规则内容（支持Markdown格式）
 * - 音频播放：自动播放规则介绍音频
 * - 支持 **加粗** 格式的Markdown渲染
 * - 遵循Adobe React Spectrum设计规范
 * - 支持响应式布局和无障碍访问
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export const RuleIntroContent: React.FC<RuleIntroContentProps> = ({
	rulesItem,
	currentPage = 1,
	onPageChange,
	onLog,
	className,
	UNSAFE_style,
}) => {
	// 分页处理：将内容按 `` 分割成多个页面
	const pages = useMemo(() => splitContentIntoPages(rulesItem.content), [rulesItem.content]);
	const totalPages = pages.length;

	// 内部页面状态管理（如果外部没有提供currentPage控制）
	const [internalCurrentPage, setInternalCurrentPage] = useState(1);
	const actualCurrentPage = currentPage || internalCurrentPage;

	// 确保当前页面在有效范围内
	const validCurrentPage = Math.max(1, Math.min(actualCurrentPage, totalPages));

	// 获取当前页面内容
	const currentPageContent = pages[validCurrentPage - 1] || rulesItem.content;

	// 页面切换处理
	const handlePageChange = useCallback((newPage: number) => {
		const validPage = Math.max(1, Math.min(newPage, totalPages));

		if (onPageChange) {
			onPageChange(validPage);
		} else {
			setInternalCurrentPage(validPage);
		}

		// 记录分页切换事件
		onLog?.('info', '规则介绍页面切换', {
			ruleTitle: rulesItem.title,
			fromPage: validCurrentPage,
			toPage: validPage,
			totalPages,
			timestamp: Date.now(),
			action: 'rule_intro_page_change'
		});
	}, [onPageChange, totalPages, validCurrentPage, rulesItem.title, onLog]);

	// 监听外部页面变化
	useEffect(() => {
		if (currentPage && currentPage !== validCurrentPage) {
			handlePageChange(currentPage);
		}
	}, [currentPage, validCurrentPage, handlePageChange]);

	// 记录组件渲染日志
	React.useEffect(() => {
		onLog?.('info', '规则介绍内容组件已渲染', {
			ruleTitle: rulesItem.title,
			hasAudio: rulesItem.audioUrls.length > 0,
			audioCount: rulesItem.audioUrls.length,
			timestamp: Date.now(),
			action: 'rule_intro_content_rendered'
		});
	}, [rulesItem.title, rulesItem.audioUrls.length, onLog]);

	return (
		<View
			backgroundColor="gray-100"
			borderRadius="medium"
			padding="size-300"
			height="100%"
			UNSAFE_className={className}
			UNSAFE_style={UNSAFE_style}
		>
			{/* A1 区域：规则标题 */}
			<View
				backgroundColor="gray-300"
				borderRadius="small"
				height="size-600"
				marginBottom="size-400"
				UNSAFE_style={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					padding: '0 16px'
				}}
			>
				<Heading level={2} UNSAFE_style={{
					fontSize: '1.5rem',
					fontWeight: 'bold',
					color: 'var(--spectrum-global-color-gray-900)',
					textAlign: 'center',
					margin: 0
				}}>
					{rulesItem.title}
				</Heading>
			</View>

			{/* B1 区域：规则内容（支持分页） */}
			<View
				marginBottom="size-100"
				backgroundColor="gray-300"
				borderRadius="small"
				UNSAFE_style={{
					padding: '24px',
					minHeight: 'size-3000',
					position: 'relative'
				}}
			>
				{/* 当前页面内容 */}
				<Text UNSAFE_style={{
					fontSize: '1rem',
					lineHeight: '1.6',
					color: 'var(--spectrum-global-color-gray-600)',
					whiteSpace: 'pre-wrap', // 保持换行格式
					textAlign: 'center' // 居中对齐
				}}>
					{renderMarkdownText(currentPageContent)}
				</Text>

				{/* 页面指示器（只在多页时显示） */}
				{totalPages > 1 && (
					<View UNSAFE_style={{
						position: 'absolute',
						bottom: '12px',
						right: '16px',
						backgroundColor: 'rgba(0, 0, 0, 0.1)',
						borderRadius: '12px',
						padding: '4px 8px'
					}}>
						<Text UNSAFE_style={{
							fontSize: '12px',
							color: 'var(--spectrum-global-color-gray-700)',
							fontWeight: 'bold'
						}}>
							{validCurrentPage}/{totalPages}
						</Text>
					</View>
				)}
			</View>

			{/* 音频播放器由ContentArea统一管理，此处不再渲染 */}
		</View>
	);
};

export default RuleIntroContent;
