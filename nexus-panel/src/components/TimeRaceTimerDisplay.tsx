import React, { useEffect } from 'react';
import { globalTimerManager } from '../utils/globalTimerManager';

/**
 * 争分夺秒计时器显示组件
 * 
 * 监听全局计时器状态变化并更新显示
 */
export const TimeRaceTimerDisplay: React.FC = () => {
  console.log('[TimeRaceTimerDisplay] 组件被渲染');

  useEffect(() => {
    console.log('[TimeRaceTimerDisplay] useEffect 开始执行');

    const updateDisplay = () => {
      const state = globalTimerManager.getState();
      console.log('[TimeRaceTimerDisplay] updateDisplay 被调用', state);

      // 更新DOM中的计时器显示
      const timerElements = document.querySelectorAll('.timer-display');
      timerElements.forEach(element => {
        if (element.textContent !== globalTimerManager.getFormattedTime()) {
          element.textContent = globalTimerManager.getFormattedTime();

          // 添加视觉反馈
          const time = state.time;
          element.classList.remove('warning', 'danger');

          if (time <= 30 && time > 10) {
            element.classList.add('warning');
          } else if (time <= 10) {
            element.classList.add('danger');
          }
        }
      });

      // 更新开始/暂停按钮文本
      const startButtons = document.querySelectorAll('button');
      startButtons.forEach(button => {
        if (button.textContent === '比赛开始' || button.textContent === '暂停') {
          button.textContent = state.isRunning ? '暂停' : '比赛开始';
        }
      });
    };

    // 添加监听器
    console.log('[TimeRaceTimerDisplay] 添加监听器');
    globalTimerManager.addListener(updateDisplay);

    // 初始更新
    console.log('[TimeRaceTimerDisplay] 执行初始更新');
    updateDisplay();

    // 清理函数
    return () => {
      globalTimerManager.removeListener(updateDisplay);
    };
  }, []);

  // 这个组件不渲染任何内容，只是用于监听状态变化
  return null;
};
