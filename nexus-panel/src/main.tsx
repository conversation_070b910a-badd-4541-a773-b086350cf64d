// React 核心依赖和样式导入
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
// 导入主应用组件
import App from "./App";
// 导入用户交互Context Provider
import { UserInteractionProvider } from "./contexts/UserInteractionContext";

// 应用程序根节点渲染
createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<UserInteractionProvider>
			<App />
		</UserInteractionProvider>
	</StrictMode>,
);
