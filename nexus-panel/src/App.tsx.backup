// React 核心依赖导入
import React, { useState, useRef, useCallback, useEffect, useMemo } from "react";
// Adobe React Spectrum UI 组件库导入
import {
	defaultTheme,
	Flex,
	Grid,
	Provider,
	View,
	DialogContainer,
	AlertDialog,
} from "@adobe/react-spectrum";

// Motion动画导入
import { MotionConfig, AnimatePresence } from "motion/react";
import { globalMotionConfig } from "./config/motionConfig";
// 导入类型定义以支持 window.ultimatePKControls
import "./types/ultimatePK.ts";



// 常用样式配置常量
const COMMON_STYLES = {
	centerFlex: {
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		padding: "0 20px",
	} as React.CSSProperties,
	flexEndAlign: {
		display: "flex",
		alignItems: "center",
		justifyContent: "flex-end",
		padding: "0 20px",
	} as React.CSSProperties,
	flexColumn: {
		display: "flex",
		flexDirection: "column",
	} as React.CSSProperties,
	flexStart: {
		display: "flex",
		justifyContent: "flex-start",
	} as React.CSSProperties,
	flexEnd: {
		display: "flex",
		justifyContent: "flex-end",
	} as React.CSSProperties,
} as const;
// 导入组件
import { ConsolePanel } from "./components/ConsolePanel";
import { SidebarButtonGroup, SidebarActionGroup } from "./components/SidebarButtonGroup";
import { DynamicComponentRenderer } from "./components/common";
// 导入 Picker 自定义样式
import "./components/PickerStyles.css";

// 导入 MQTT 集成模块
import { useMQTTIntegration } from "./hooks/useMQTTIntegration";
// 导入赛事 API 模块
import { RaceApiProvider, useSharedRaceApi } from "./hooks/useRaceApi/singleton";
import { initializeApiService, type ApiError } from "./services/api";
// 导入配置相关模块
import { useHomeConfiguration } from "./hooks/useHomeConfiguration";
import { generateHomeButtonGroups, createQuestionButtonGroups, createSafetyPublicClassButtonGroupsWithRealData } from "./config/buttonGroupConfigurations";
import { useQuestionNavigation } from "./hooks/useQuestionNavigation";
import { useAppStateManager } from "./hooks/useAppStateManager";
// 导入布局组件
import { HeaderSection, ContentArea, NavigationSidebar, FooterSection } from "./components/layout";
// 导入计时器组件 - 暂时禁用
// import { TimeRaceTimerDisplay } from "./components/TimeRaceTimerDisplay";
// 导入工具函数
import { findNodeInTree } from "./utils/rankingUtils";



// 主应用组件 - 管理全局状态
export function App() {
	// 日志ID计数器 - 确保日志ID唯一性（初始值为3，因为初始日志使用了0、1、2）
	const logIdCounter = useRef(3);

	// 管理项目选择器的选中状态
	const [selectedProject, setSelectedProject] = useState<string | null>(null);

	// 管理导航模式选择 - 全局指令/定向指令切换
	const [contentType, setContentType] = useState<string>("global");

	// 管理当前选中的导航节点
	const [selectedNavigationKey, setSelectedNavigationKey] = useState<
		string | null
	>(null);

	// 排名分页状态管理
	const [currentRankingPage, setCurrentRankingPage] = useState(1);
	const pageSize = 8; // 每页显示8名选手

	// 管理当前显示类型（首页专用）
	const [currentDisplayType, setCurrentDisplayType] = useState<
		'leader' | 'player' | 'award' | null
	>(null);



	// Picker重置控制状态（用于实现互斥选择行为）
	const [pickerResetControl, setPickerResetControl] = useState<{
		timestamp: number;
		selectedType: 'leader' | 'player' | 'award' | null;
	}>({
		timestamp: 0,
		selectedType: null,
	});

	// 管理当前选中的规则介绍项（规则页面专用）
	const [selectedRulesIntroductionItem, setSelectedRulesIntroductionItem] = useState<
		import('./services/api/types').ProcessedRulesIntroductionItem | null
	>(null);

	// 管理规则页面的当前页码
	const [rulesCurrentPage, setRulesCurrentPage] = useState<number>(1);

	// 题目相关状态管理
	const [showAnswer, setShowAnswer] = useState<boolean>(true);
	const [showExplanation, setShowExplanation] = useState<boolean>(false);

	// 终极PK阶段更新触发器（用于强制重新渲染按钮组）
	const [ultimatePKStageUpdateTrigger, setUltimatePKStageUpdateTrigger] = useState<number>(0);

	// 题包切换确认对话框状态
	const [packageChangeDialog, setPackageChangeDialog] = useState<{
		isOpen: boolean;
		packageId: string;
		packageName: string;
	}>({
		isOpen: false,
		packageId: "",
		packageName: ""
	});

	// 管理控台面板日志
	const [consoleLogs, setConsoleLogs] = useState<
		Array<{
			id: string;
			timestamp: Date;
			level: "info" | "warning" | "error" | "success" | "send" | "get";
			message: string;
			details?: string;
		}>
	>([]);

	// 获取日志数量的回调函数
	const getLogCount = useCallback(() => consoleLogs.length, [consoleLogs.length]);

	// 内存统计更新回调函数
	const onMemoryStatsUpdate = useCallback(() => {
		// 触发重新渲染以更新内存统计显示
	}, []);

	// 清理控制台日志函数（用于内存管理）
	const cleanupConsoleLogs = useCallback((count: number) => {
		setConsoleLogs(prev => {
			const newLogs = prev.slice(0, prev.length - count);

			// 避免在清理过程中调用 addConsoleLog，防止循环依赖
			// 直接添加清理日志到结果中
			const cleanupLog = {
				id: `cleanup-${Date.now()}-${logIdCounter.current++}`,
				timestamp: new Date(),
				level: 'info' as const,
				message: `已清理 ${count} 条历史日志`,
				details: JSON.stringify({
					清理前数量: prev.length,
					清理后数量: newLogs.length
				})
			};

			return [cleanupLog, ...newLogs];
		});
	}, []);

	// 过滤 details 中的动态字段，用于去重比较
	const filterDetailsForDeduplication = useCallback((details: unknown): string | undefined => {
		if (!details) return undefined;

		try {
			const parsed = typeof details === 'string' ? JSON.parse(details) : details;
			if (typeof parsed !== 'object' || parsed === null) {
				return JSON.stringify(parsed);
			}

			// 创建副本并移除动态字段
			const filtered = { ...parsed };
			delete filtered.timestamp;
			delete filtered.action;

			// 如果过滤后对象为空，返回 undefined
			const filteredKeys = Object.keys(filtered);
			if (filteredKeys.length === 0) {
				return undefined;
			}

			return JSON.stringify(filtered);
		} catch {
			// 如果解析失败，返回原始字符串
			return typeof details === 'string' ? details : JSON.stringify(details);
		}
	}, []);

	// 添加日志函数（带智能去重保护）
	const addConsoleLog = useCallback((level: 'info' | 'warning' | 'error' | 'success' | 'send' | 'get', message: string, details?: unknown) => {
		const detailsStr = details ? JSON.stringify(details) : undefined;
		const filteredDetailsStr = filterDetailsForDeduplication(details);

		// 智能去重检查：检查最近10条日志中是否有相同的消息
		setConsoleLogs(prev => {
			// 检查是否为API相关日志（包含source字段或action字段包含api/fetch）
			const isApiLog = details && typeof details === 'object' && (
				('source' in details && typeof details.source === 'string' && details.source.includes('api')) ||
				('action' in details && typeof details.action === 'string' && (
					details.action.includes('fetch') ||
					details.action.includes('api') ||
					details.action.includes('race')
				))
			);

			// 对于API日志，使用更宽松的去重策略
			const isDuplicate = prev.slice(0, 10).some(log => {
				const logFilteredDetails = filterDetailsForDeduplication(log.details);
				const timeDiff = Date.now() - new Date(log.timestamp).getTime();

				// API日志：只有在1秒内且消息完全相同才去重
				if (isApiLog) {
					return (
						log.level === level &&
						log.message === message &&
						timeDiff < 1000 // API日志只在1秒内去重
					);
				}

				// 非API日志：使用原有的3秒去重策略
				return (
					log.level === level &&
					log.message === message &&
					logFilteredDetails === filteredDetailsStr &&
					timeDiff < 3000
				);
			});

			if (isDuplicate) {
				return prev; // 跳过重复消息
			}

			const newLog = {
				id: `${Date.now()}-${logIdCounter.current++}`,
				timestamp: new Date(),
				level,
				message,
				details: detailsStr
			};

			const newLogs = [newLog, ...prev.slice(0, 99)]; // 保持最新100条

			// 主动触发内存统计更新，确保日志数量实时反映
			onMemoryStatsUpdate();

			return newLogs;
		});
	}, [onMemoryStatsUpdate, filterDetailsForDeduplication]);

	// 初始化 API 服务
	useEffect(() => {
		initializeApiService(process.env.NODE_ENV === 'development' ? 'development' : 'production');
	}, []);

	const handleRaceApiError = useCallback((error: ApiError) => {
		addConsoleLog('error', '加载赛事数据失败', {
			error: error.message,
			code: error.code
		});
	}, [addConsoleLog]);

	// 内部App组件，使用共享的RaceApi实例
	return <AppContent
		selectedProject={selectedProject}
		setSelectedProject={setSelectedProject}
		contentType={contentType}
		setContentType={setContentType}
		selectedNavigationKey={selectedNavigationKey}
		setSelectedNavigationKey={setSelectedNavigationKey}
		currentRankingPage={currentRankingPage}
		setCurrentRankingPage={setCurrentRankingPage}
		currentDisplayType={currentDisplayType}
		setCurrentDisplayType={setCurrentDisplayType}
		pickerResetControl={pickerResetControl}
		setPickerResetControl={setPickerResetControl}
		selectedRulesIntroductionItem={selectedRulesIntroductionItem}
		setSelectedRulesIntroductionItem={setSelectedRulesIntroductionItem}
		rulesCurrentPage={rulesCurrentPage}
		setRulesCurrentPage={setRulesCurrentPage}
		showAnswer={showAnswer}
		setShowAnswer={setShowAnswer}
		showExplanation={showExplanation}
		setShowExplanation={setShowExplanation}
		ultimatePKStageUpdateTrigger={ultimatePKStageUpdateTrigger}
		setUltimatePKStageUpdateTrigger={setUltimatePKStageUpdateTrigger}
		packageChangeDialog={packageChangeDialog}
		setPackageChangeDialog={setPackageChangeDialog}
		consoleLogs={consoleLogs}
		setConsoleLogs={setConsoleLogs}
		addConsoleLog={addConsoleLog}
		handleRaceApiError={handleRaceApiError}
		onMemoryStatsUpdate={onMemoryStatsUpdate}
		cleanupConsoleLogs={cleanupConsoleLogs}
		getLogCount={getLogCount}
		pageSize={pageSize}
	/>;
}

// 内部App内容组件
function AppContent({
	selectedProject,
	setSelectedProject,
	contentType,
	setContentType,
	selectedNavigationKey,
	setSelectedNavigationKey,
	currentRankingPage,
	setCurrentRankingPage,
	currentDisplayType,
	setCurrentDisplayType,
	pickerResetControl,
	setPickerResetControl,
	selectedRulesIntroductionItem,
	setSelectedRulesIntroductionItem,
	rulesCurrentPage,
	setRulesCurrentPage,
	showAnswer,
	setShowAnswer,
	showExplanation,
	setShowExplanation,
	ultimatePKStageUpdateTrigger,
	setUltimatePKStageUpdateTrigger,
	packageChangeDialog,
	setPackageChangeDialog,
	consoleLogs,
	setConsoleLogs,
	addConsoleLog,
	handleRaceApiError,
	onMemoryStatsUpdate,
	cleanupConsoleLogs,
	getLogCount,
	pageSize
}: any) {
	// 使用共享的赛事 API Hook
	const raceApi = useSharedRaceApi();

	// 排名数据状态（从raceApi中获取）
	const rankingData = raceApi.rankingData;
	const rankingLoading = raceApi.rankingLoading;
	const rankingError = raceApi.rankingError;
	const rankingProgress = raceApi.rankingProgress;

	// 排名数据刷新回调
	const handleRankingRefresh = useCallback(() => {
		if (selectedProject) {
			addConsoleLog('info', '用户手动刷新排名数据', {
				selectedProject,
				timestamp: Date.now(),
				action: 'ranking_manual_refresh'
			});

			raceApi.fetchRankingData(selectedProject).catch((error) => {
				addConsoleLog('error', '手动刷新排名数据失败', {
					error: error.message,
					selectedProject,
					timestamp: Date.now(),
					action: 'ranking_manual_refresh_failed'
				});
			});
		}
	}, [selectedProject, raceApi, addConsoleLog]);

	// 当排名数据更新时，重置到第一页
	useEffect(() => {
		if (raceApi.rankingData) {
			setCurrentRankingPage(1);
		}
	}, [raceApi.rankingData]);

	// 计算分页后的排名数据
	const paginatedRankingData = useMemo(() => {
		if (!raceApi.rankingData) return null;

		const totalPages = Math.ceil(raceApi.rankingData.totalPlayers / pageSize);
		const hasMultiplePages = raceApi.rankingData.totalPlayers > pageSize;

		// 确保当前页在有效范围内
		const validCurrentPage = Math.min(Math.max(1, currentRankingPage), totalPages || 1);

		// 如果当前页被调整了，更新状态
		if (validCurrentPage !== currentRankingPage) {
			setCurrentRankingPage(validCurrentPage);
		}

		const startIndex = (validCurrentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;

		return {
			...raceApi.rankingData,
			players: raceApi.rankingData.players.slice(startIndex, endIndex),
			paginationInfo: {
				currentPage: validCurrentPage,
				totalPages,
				pageSize,
				hasMultiplePages
			}
		};
	}, [raceApi.rankingData, currentRankingPage, pageSize]);

	// 分页切换处理器
	const handleRankingPageChange = useCallback((page: number) => {
		if (paginatedRankingData && page >= 1 && page <= paginatedRankingData.paginationInfo.totalPages) {
			setCurrentRankingPage(page);
			addConsoleLog('info', `切换到排名第${page}页`, {
				page,
				totalPages: paginatedRankingData.paginationInfo.totalPages,
				timestamp: Date.now(),
				action: 'ranking_page_change'
			});
		}
	}, [paginatedRankingData, setCurrentRankingPage, addConsoleLog]);

	// 题目导航Hook - 必须在 raceApi 定义之后
	const questionNavigation = useQuestionNavigation({
		questions: raceApi.questionData,
		onQuestionChange: (questionNumber, question) => {
			// 题目切换时重置答案状态为默认（未激活）
			setShowAnswer(false);

			addConsoleLog('info', `题目切换到第${questionNumber}题`, {
				questionNumber,
				questionId: question?.id,
				questionType: question?.questionType,
			});
		},
		onBoundaryError: (type, currentNumber) => {
			const message = type === 'first' ? '已经是第一题' : '已经是最后一题';
			addConsoleLog('warning', message, { currentNumber });
		},
		onLog: addConsoleLog,
	});



	// 使用首页配置 Hook
	const homeConfiguration = useHomeConfiguration({
		configurationData: raceApi.configurationData,
		configurationLoading: raceApi.configurationLoading,
		configurationError: raceApi.configurationError,
		onLog: addConsoleLog,
		currentDisplayType,
		setCurrentDisplayType,
		pickerResetControl,
		setPickerResetControl,
		addConsoleLog,
	});

	// 使用 MQTT 集成 Hook
	const mqttIntegration = useMQTTIntegration({
		onLog: addConsoleLog,
		onMemoryStatsUpdate,
		getLogCount,
		onLogCleanup: cleanupConsoleLogs,
		raceApi,
		debug: process.env.NODE_ENV === 'development'
	});

	// 使用App状态管理 Hook
	const appStateManager = useAppStateManager({
		raceApi,
		questionNavigation,
		stateSetters: {
			setSelectedProject,
			setSelectedNavigationKey,
			setCurrentDisplayType,
			setSelectedRulesIntroductionItem,
			setRulesCurrentPage,
			setShowAnswer,
			setShowExplanation,
		},
		onLog: addConsoleLog,
		onMemoryStatsUpdate,
		mqttIntegration,
	});

	// 判断节点是否为排名节点的工具函数
	const isRankingNode = useCallback((nodeKey: string, navigationData: import('./services/api/types').NavigationNode[] | null): boolean => {
		if (!navigationData) return false;

		// 递归查找节点
		const findNode = (nodes: import('./services/api/types').NavigationNode[]): import('./services/api/types').NavigationNode | null => {
			for (const node of nodes) {
				if (node.id === nodeKey) {
					return node;
				}
				if (node.children && node.children.length > 0) {
					const found = findNode(node.children);
					if (found) return found;
				}
			}
			return null;
		};

		const node = findNode(navigationData);
		return !!(node && node.contentType === "排名");
	}, []);

	// 处理导航选择变化
	const handleNavigationSelectionChange = useCallback((selectedKey: string | null) => {
		setSelectedNavigationKey(selectedKey);

		// 使用appStateManager统一重置导航相关状态
		appStateManager.resetNavigationState();

		// 如果选中的是排名节点，触发排名数据获取
		if (selectedKey && isRankingNode(selectedKey, raceApi.navigationData)) {
			if (selectedProject) {
				addConsoleLog('info', '检测到排名节点，开始获取排名数据', {
					selectedKey,
					selectedProject,
					timestamp: Date.now(),
					action: 'ranking_node_detected'
				});

				raceApi.fetchRankingData(selectedProject).catch((error) => {
					addConsoleLog('error', '获取排名数据失败', {
						error: error.message,
						selectedKey,
						selectedProject,
						timestamp: Date.now(),
						action: 'ranking_fetch_failed'
					});
				});
			} else {
				addConsoleLog('warning', '未选择项目，无法获取排名数据', {
					selectedKey,
					timestamp: Date.now(),
					action: 'ranking_no_project'
				});
			}
		}

		// 检查是否为规则介绍相关的导航
		if (selectedKey && raceApi.navigationData) {
			// 调试：检查导航数据的完整性
			addConsoleLog('info', '当前导航数据状态', {
				navigationDataExists: !!raceApi.navigationData,
				navigationDataLength: raceApi.navigationData?.length || 0,
				selectedKey,
				timestamp: Date.now(),
				action: 'navigation_data_debug'
			});

			// 查找当前选中的节点
			const findNodeInTree = (nodes: import('./services/api/types').NavigationNode[], targetId: string): import('./services/api/types').NavigationNode | null => {
				for (const node of nodes) {
					if (node.id === targetId) return node;
					if (node.children) {
						const found = findNodeInTree(node.children, targetId);
						if (found) return found;
					}
				}
				return null;
			};

			const currentNode = findNodeInTree(raceApi.navigationData, selectedKey);

			// 调试：显示找到的节点信息
			if (currentNode) {
				addConsoleLog('info', '找到的导航节点信息', {
					nodeId: currentNode.id,
					nodeName: currentNode.name,
					nodeType: currentNode.type,
					contentType: currentNode.contentType,
					initialStage: currentNode.initialStage,
					hasInitialStage: !!currentNode.initialStage,
					initialStageType: typeof currentNode.initialStage,
					timestamp: Date.now(),
					action: 'found_navigation_node'
				});
			} else {
				addConsoleLog('warning', '未找到导航节点', {
					selectedKey,
					availableNodes: raceApi.navigationData?.map(n => ({ id: n.id, name: n.name })),
					timestamp: Date.now(),
					action: 'navigation_node_not_found'
				});
			}

			// 检查是否为规则介绍的子节点
			if (currentNode && currentNode.parentId) {
				const parentNode = findNodeInTree(raceApi.navigationData, currentNode.parentId);
				if (parentNode && parentNode.name === "规则介绍" && selectedProject) {

					// 如果已有规则数据，直接匹配
					if (raceApi.rulesIntroductionData && !raceApi.rulesIntroductionLoading) {
						const matchedRule = raceApi.findRulesIntroductionByTitle(currentNode.name);
						if (matchedRule) {
							setSelectedRulesIntroductionItem(matchedRule);
							addConsoleLog('info', '已匹配规则介绍内容', {
								nodeTitle: currentNode.name,
								ruleTitle: matchedRule.title,
								timestamp: Date.now(),
								action: 'rules_introduction_matched'
							});
						} else {
							addConsoleLog('warning', '未找到匹配的规则介绍内容', {
								nodeTitle: currentNode.name,
								availableRules: raceApi.rulesIntroductionData.map(r => r.title),
								timestamp: Date.now(),
								action: 'rules_introduction_not_matched'
							});
						}
					} else if (!raceApi.rulesIntroductionLoading) {
						// 如果还没有规则数据且不在加载中，先获取数据
						addConsoleLog('info', '开始获取规则介绍数据', {
							nodeTitle: currentNode.name,
							timestamp: Date.now(),
							action: 'start_fetch_rules_introduction'
						});

						raceApi.fetchRulesIntroductionData(selectedProject).then(() => {
							// 数据获取完成后，重新匹配规则
							const matchedRule = raceApi.findRulesIntroductionByTitle(currentNode.name);
							if (matchedRule) {
								setSelectedRulesIntroductionItem(matchedRule);
								addConsoleLog('success', '规则介绍数据获取并匹配成功', {
									nodeTitle: currentNode.name,
									ruleTitle: matchedRule.title,
									timestamp: Date.now(),
									action: 'rules_introduction_fetch_and_match_success'
								});
							} else {
								addConsoleLog('warning', '规则介绍数据获取成功但未找到匹配项', {
									nodeTitle: currentNode.name,
									availableRules: raceApi.rulesIntroductionData?.map(r => r.title) || [],
									timestamp: Date.now(),
									action: 'rules_introduction_fetch_success_no_match'
								});
							}
						}).catch((error) => {
							addConsoleLog('error', '获取规则介绍数据失败', {
								nodeTitle: currentNode.name,
								error: error.message,
								timestamp: Date.now(),
								action: 'fetch_rules_introduction_error'
							});
						});
					} else {
						// 正在加载中，等待加载完成
						addConsoleLog('info', '规则介绍数据正在加载中，等待完成', {
							nodeTitle: currentNode.name,
							timestamp: Date.now(),
							action: 'rules_introduction_loading_wait'
						});
					}
				}
			}

			// 检查是否为题目相关的节点

			// 检查条件：仅根据API的"内容类型"字段匹配
			const isQuestionNode = currentNode && (
				currentNode.contentType === '题目' ||
				currentNode.contentType?.startsWith('快答')
			);



			if (isQuestionNode && selectedProject) {
				// 获取初始阶段信息
				const initialStage = currentNode.initialStage;

				// 调试：检查节点的完整信息
				addConsoleLog('info', '导航节点详细信息', {
					nodeId: currentNode.id,
					nodeName: currentNode.name,
					nodeType: currentNode.type,
					contentType: currentNode.contentType,
					initialStage: currentNode.initialStage,
					hasInitialStage: !!currentNode.initialStage,
					timestamp: Date.now(),
					action: 'navigation_node_debug'
				});

				// 设置当前阶段状态
				if (initialStage) {
					raceApi.setCurrentStage(initialStage);
				}

				// 获取题目数据
				addConsoleLog('info', `开始获取题目数据: ${currentNode.name}`, {
					sectionName: currentNode.name,
					contentType: currentNode.contentType,
					initialStage,
					willUseStageFilter: !!initialStage,
					apiMethod: initialStage ? 'fetchQuestionDataWithStage' : 'fetchQuestionData',
					timestamp: Date.now(),
					action: 'fetch_question_data_start'
				});

				// 如果有初始阶段，使用阶段感知的API调用
				if (initialStage) {
					addConsoleLog('info', '使用阶段感知API调用', {
						method: 'fetchQuestionDataWithStage',
						sectionName: currentNode.name,
						initialStage,
						selectedProject,
						timestamp: Date.now(),
						action: 'using_stage_aware_api'
					});
				} else {
					addConsoleLog('info', '使用标准API调用', {
						method: 'fetchQuestionData',
						sectionName: currentNode.name,
						selectedProject,
						reason: 'no_initial_stage',
						timestamp: Date.now(),
						action: 'using_standard_api'
					});
				}

				// 注意：initialStage可能是内容类型（如"快答"），会在fetchQuestionDataWithStage中进行映射

				const fetchPromise = initialStage
					? raceApi.fetchQuestionDataWithStage(selectedProject, currentNode.name, initialStage)
					: raceApi.fetchQuestionData(selectedProject, currentNode.name);

				fetchPromise
					.then(() => {
						addConsoleLog('success', '题目数据获取成功', {
							sectionName: currentNode.name,
							questionCount: raceApi.questionData?.length || 0,
							initialStage,
							timestamp: Date.now(),
							action: 'fetch_question_data_success'
						});
					})
					.catch((error) => {
						addConsoleLog('error', '获取题目数据失败', {
							sectionName: currentNode.name,
							error: error.message,
							initialStage,
							timestamp: Date.now(),
							action: 'fetch_question_data_error'
						});
						// 获取失败时重置阶段状态
						raceApi.setCurrentStage(null);
					});
			}
		}

		// 使用 MQTT 集成 Hook 发送导航消息
		mqttIntegration.handleNavigationChange(selectedKey);
	}, [mqttIntegration, raceApi, selectedProject, addConsoleLog, appStateManager, isRankingNode]);

	// 监听规则介绍数据变化，确保数据加载完成后能自动匹配当前选中的节点
	React.useEffect(() => {
		if (selectedNavigationKey &&
			raceApi.rulesIntroductionData &&
			!raceApi.rulesIntroductionLoading &&
			!selectedRulesIntroductionItem &&
			raceApi.navigationData) {

			// 查找当前选中的节点
			const findNodeInTree = (nodes: import('./services/api/types').NavigationNode[], targetId: string): import('./services/api/types').NavigationNode | null => {
				for (const node of nodes) {
					if (node.id === targetId) return node;
					if (node.children) {
						const found = findNodeInTree(node.children, targetId);
						if (found) return found;
					}
				}
				return null;
			};

			const currentNode = findNodeInTree(raceApi.navigationData, selectedNavigationKey);

			// 检查是否为规则介绍的子节点
			if (currentNode && currentNode.parentId) {
				const parentNode = findNodeInTree(raceApi.navigationData, currentNode.parentId);
				if (parentNode && parentNode.name === "规则介绍") {
					const matchedRule = raceApi.findRulesIntroductionByTitle(currentNode.name);
					if (matchedRule) {
						setSelectedRulesIntroductionItem(matchedRule);
						addConsoleLog('success', '规则介绍数据加载完成后自动匹配成功', {
							nodeTitle: currentNode.name,
							ruleTitle: matchedRule.title,
							timestamp: Date.now(),
							action: 'auto_match_after_data_loaded'
						});
					}
				}
			}
		}
	}, [selectedNavigationKey, raceApi.rulesIntroductionData, raceApi.rulesIntroductionLoading, selectedRulesIntroductionItem, raceApi.navigationData, raceApi.findRulesIntroductionByTitle, addConsoleLog, raceApi]);

	// 处理规则页面切换
	const handleRulesPageChange = useCallback((page: number) => {
		setRulesCurrentPage(page);
		addConsoleLog('info', '规则页面切换', {
			page,
			ruleTitle: selectedRulesIntroductionItem?.title,
			timestamp: Date.now(),
			action: 'rules_page_change'
		});
	}, [selectedRulesIntroductionItem?.title, addConsoleLog]);









	// 监听配置数据状态变化，记录日志
	useEffect(() => {
		if (selectedNavigationKey === 'home') {
			if (raceApi.configurationLoading) {
				addConsoleLog('info', '配置数据加载中...', {
					timestamp: Date.now(),
					action: 'configuration_loading'
				});
			} else if (raceApi.configurationError) {
				addConsoleLog('error', '配置数据加载失败，使用静态配置', {
					error: raceApi.configurationError.message,
					timestamp: Date.now(),
					action: 'configuration_error_fallback'
				});
			} else if (raceApi.configurationData) {
				addConsoleLog('success', '配置数据加载成功，已生成动态按钮组', {
					leaderCount: raceApi.configurationData.leaderDisplay.length,
					playerCount: raceApi.configurationData.playerDisplay.length,
					awardCount: raceApi.configurationData.awardDisplay.length,
					timestamp: Date.now(),
					action: 'configuration_success'
				});
			}
		}
	}, [selectedNavigationKey, raceApi.configurationLoading, raceApi.configurationError, raceApi.configurationData, addConsoleLog]);

	// 记录配置数据状态变化（避免在渲染过程中调用setState）
	useEffect(() => {
		if (selectedNavigationKey === 'home') {
			if (raceApi.configurationData && !raceApi.configurationLoading && !raceApi.configurationError) {
				addConsoleLog('info', '✅ 使用API配置数据生成首页按钮组', {
					leaderCount: raceApi.configurationData.leaderDisplay.length,
					playerCount: raceApi.configurationData.playerDisplay.length,
					awardCount: raceApi.configurationData.awardDisplay.length,
					timestamp: Date.now(),
					action: 'generate_dynamic_button_groups'
				});
			} else if (raceApi.configurationLoading) {
				addConsoleLog('info', '⏳ 配置数据加载中，等待API响应', {
					timestamp: Date.now(),
					action: 'waiting_for_configuration_data'
				});
			} else if (raceApi.configurationError) {
				addConsoleLog('error', '❌ 配置数据加载失败，无法显示按钮组', {
					error: raceApi.configurationError.message,
					timestamp: Date.now(),
					action: 'configuration_data_error'
				});
			} else if (!raceApi.configurationData) {
				addConsoleLog('warning', '⚠️ 无配置数据，等待赛事选择', {
					timestamp: Date.now(),
					action: 'no_configuration_data'
				});
			}
		}
	}, [selectedNavigationKey, raceApi.configurationData, raceApi.configurationLoading, raceApi.configurationError, addConsoleLog]);

	// 安全公开课页面数据加载逻辑
	useEffect(() => {
		// 检查是否为安全公开课页面
		if (!selectedNavigationKey || !selectedProject || !raceApi.navigationData) return;

		const currentNode = findNodeInTree(raceApi.navigationData, selectedNavigationKey);
		if (!currentNode || currentNode.contentType !== '评分') return;

		// 确保在项目选择后加载必要的配置数据（包含节目显示和选手显示信息）
		if (!raceApi.configurationData && !raceApi.configurationLoading) {
			addConsoleLog('info', '[安全公开课] 配置数据未加载，开始获取', {
				selectedProject,
				selectedNavigationKey,
				nodeName: currentNode.name,
				timestamp: Date.now(),
				action: 'fetch_configuration_for_safety_class'
			});
			raceApi.fetchConfigurationData(selectedProject);
		}
	}, [
		selectedNavigationKey,
		selectedProject,
		raceApi.navigationData,
		raceApi.configurationData,
		raceApi.configurationLoading,
		raceApi.fetchConfigurationData,
		addConsoleLog
	]);

	// 简化的终极PK状态检查（仅在终极PK页面时运行）
	useEffect(() => {
		if (selectedNavigationKey !== 'switch-finalpk') return;

		let lastStage: string | null = null;

		const checkStageChange = () => {
			const controls = window.ultimatePKControls;
			const currentStage = controls?.getCurrentStage?.();

			if (currentStage && currentStage !== lastStage) {
				lastStage = currentStage;
				setUltimatePKStageUpdateTrigger(Date.now());
			}
		};

		// 每1秒检查一次阶段变化，仅在终极PK页面
		const interval = setInterval(checkStageChange, 1000);

		return () => {
			clearInterval(interval);
		};
	}, [selectedNavigationKey]);

	// 处理答案显示切换
	const handleToggleAnswer = useCallback(() => {
		const newShowAnswer = !showAnswer;
		setShowAnswer(newShowAnswer);

		addConsoleLog('info', `答案显示状态已切换为${newShowAnswer ? '激活' : '未激活'}`, {
			showAnswer: newShowAnswer,
			currentQuestionNumber: questionNavigation.currentQuestionNumber,
			timestamp: Date.now(),
			action: 'toggle_answer_display'
		});
	}, [showAnswer, addConsoleLog, questionNavigation.currentQuestionNumber]);

	// 处理题包切换确认后的直接执行（绕过全局确认对话框）
	const handlePackageChangeConfirmed = useCallback((packageId: string, packageName: string) => {
		// 直接实现题包切换的核心逻辑，绕过全局确认对话框机制

		// 预检查：验证项目和导航选择
		if (!selectedProject || !selectedNavigationKey) {
			addConsoleLog('warning', '无法切换题包：未选择项目或导航', {
				selectedProject,
				selectedNavigationKey,
				packageId,
				packageName,
				timestamp: Date.now(),
				action: 'package_change_validation_failed'
			});
			return;
		}

		// 查找当前导航节点的辅助函数
		const findNodeInTree = (nodes: import('./services/api/types').NavigationNode[], targetId: string): import('./services/api/types').NavigationNode | null => {
			for (const node of nodes) {
				if (node.id === targetId) return node;
				if (node.children) {
					const found = findNodeInTree(node.children, targetId);
					if (found) return found;
				}
			}
			return null;
		};

		const currentNode = raceApi.navigationData ? findNodeInTree(raceApi.navigationData, selectedNavigationKey) : null;

		if (!currentNode) {
			addConsoleLog('warning', '无法切换题包：未找到当前导航节点', {
				selectedNavigationKey,
				packageId,
				packageName,
				timestamp: Date.now(),
				action: 'package_change_node_not_found'
			});
			return;
		}

		// 更新当前题包状态
		raceApi.setCurrentPackage(packageId);

		// 获取指定题包的题目数据
		addConsoleLog('info', `开始切换到题包: ${packageName}`, {
			packageId,
			packageName,
			sectionName: currentNode.name,
			currentStage: raceApi.currentStage,
			selectedProject,
			timestamp: Date.now(),
			action: 'package_change_start'
		});

		// 如果有当前阶段，使用阶段+题包的组合查询；否则只使用题包查询
		// 注意：raceApi.currentStage可能是内容类型（如"快答"），会在fetchQuestionDataWithStage中进行映射
		const fetchPromise = raceApi.currentStage && selectedProject
			? raceApi.fetchQuestionDataWithStage(selectedProject, currentNode.name, raceApi.currentStage, packageId)
			: selectedProject
				? raceApi.fetchQuestionData(selectedProject, currentNode.name, packageId)
				: Promise.reject(new Error('No selected project'));

		fetchPromise
			.then(() => {
				addConsoleLog('success', `题包切换成功: ${packageName}`, {
					packageId,
					packageName,
					sectionName: currentNode.name,
					currentStage: raceApi.currentStage,
					questionCount: raceApi.questionData?.length || 0,
					timestamp: Date.now(),
					action: 'package_change_success'
				});
			})
			.catch((error) => {
				addConsoleLog('error', `题包切换失败: ${packageName}`, {
					packageId,
					packageName,
					sectionName: currentNode.name,
					currentStage: raceApi.currentStage,
					error: error.message,
					timestamp: Date.now(),
					action: 'package_change_error'
				});
				// 切换失败时重置题包状态
				raceApi.setCurrentPackage("1");
			});

		// 在确认后重置题目导航
		questionNavigation.resetToInitial();
	}, [selectedProject, selectedNavigationKey, raceApi, questionNavigation, addConsoleLog]);









	// 图片加载错误处理函数（避免在渲染期间创建）
	const handleImageError = useCallback((error: string) => {
		addConsoleLog('error', '图片加载失败', {
			error,
			timestamp: Date.now(),
			action: 'image_load_error'
		});
	}, [addConsoleLog]);




	// 清空控台日志
	const handleClearConsoleLogs = useCallback(() => {
		setConsoleLogs([]);

		// 主动触发内存统计更新，确保日志数量立即归零
		onMemoryStatsUpdate();
	}, [onMemoryStatsUpdate]);

	// 刷新控台状态
	const handleRefreshConsoleStatus = useCallback(() => {
		const newLog = {
			id: `${Date.now()}-${logIdCounter.current++}`,
			timestamp: new Date(),
			level: "info" as const,
			message: "状态已刷新",
			details: "所有系统状态已重新检查",
		};
		setConsoleLogs((prev) => [newLog, ...prev]);

		// 主动触发内存统计更新
		onMemoryStatsUpdate();
	}, [onMemoryStatsUpdate]);

	return (
		<>
			{/* Motion全局配置 */}
			<MotionConfig
				transition={globalMotionConfig.transition}
				reducedMotion={globalMotionConfig.reducedMotion}
			>
				{/* Adobe Spectrum 主题提供者 - 使用深色主题 */}
				<Provider theme={defaultTheme} colorScheme="dark">
					{/* 带背景色的容器 */}
					<View backgroundColor="gray-50" height="100vh" width="100vw">
						{/* 响应式网格布局容器 */}
						<Grid
							height="100%"
							width="100%"
							areas={{
								// 移动端布局 (base): 垂直堆叠
								base: ["header", "nav", "content", "footer"],
								// 中等屏幕 (M): 左侧导航 + 右侧内容
								M: [
									"header   header",
									"nav      content",
									"nav      content",
									"footer   footer",
								],
								// 大屏幕 (L): 三栏布局 (导航 + 内容 + 目录)
								L: [
									"header header  header",
									"nav    content toc",
									"nav    content toc",
									"footer footer  footer",
								],
							}}
							rows={{
								base: ["auto", "auto", "1fr", "auto"], // 移动端行高设置
								M: ["auto", "1fr", "auto"], // 中等屏幕行高设置
								L: ["auto", "1fr", "auto"], // 大屏幕行高设置
							}}
							columns={{
								M: ["size-2400", "1fr"], // 中等屏幕列宽: 固定导航宽度 + 自适应内容
								L: ["size-2400", "2fr", "1fr"], // 大屏幕列宽: 固定导航 + 自适应内容 + 自适应目录
							}}
							gap="size-10" // 网格间距
						>
							{/* 页面头部区域 */}
							<HeaderSection
								contentType={contentType}
								onContentTypeChange={setContentType}
								onRefresh={appStateManager.handlers.handleRefresh}
								raceApi={raceApi}
								selectedProject={selectedProject}
								onProjectSelectionChange={appStateManager.handlers.handleProjectSelectionChange}
							/>
							{/* 侧边导航栏区域 */}
							<NavigationSidebar
								navigationData={raceApi.navigationData}
								loading={raceApi.navigationLoading}
								onSelectionChange={handleNavigationSelectionChange}
								configurationDataReady={raceApi.allDataReady}
							/>
							{/* 主内容区域 */}
							<View
								backgroundColor="gray-75"
								gridArea="content"
								UNSAFE_style={{
									...COMMON_STYLES.flexColumn,
									height: "100%",
									overflow: "hidden",
								}}
							>
								{/* 内容区域组件 - 使用 DynamicComponentRenderer */}
								<DynamicComponentRenderer
									selectedNavigationKey={selectedNavigationKey}
									navigationData={raceApi.navigationData}
								>
									{({ componentConfig, breadcrumbItems }) => {
										// 递归查找导航节点函数
										const findNodeInTree = (nodes: import('./services/api/types').NavigationNode[], targetId: string): import('./services/api/types').NavigationNode | null => {
											for (const node of nodes) {
												if (node.id === targetId) return node;
												if (node.children) {
													const found = findNodeInTree(node.children, targetId);
													if (found) return found;
												}
											}
											return null;
										};

										// 获取当前导航节点的初始阶段信息
										const currentNode = selectedNavigationKey && raceApi.navigationData
											? findNodeInTree(raceApi.navigationData, selectedNavigationKey)
											: null;
										const initialStage = currentNode?.initialStage;

										return (
											<ContentArea
												selectedNavigationKey={selectedNavigationKey}
												componentConfig={componentConfig}
												breadcrumbItems={breadcrumbItems}
												configurationData={raceApi.configurationData}
												selectedConfigurationItems={homeConfiguration.selectedItems}
												currentDisplayType={currentDisplayType}
												rulesIntroductionData={raceApi.rulesIntroductionData}
												selectedRulesIntroductionItem={selectedRulesIntroductionItem}
												rulesIntroductionLoading={raceApi.rulesIntroductionLoading}
												rulesCurrentPage={rulesCurrentPage}
												onRulesPageChange={handleRulesPageChange}
												questionData={raceApi.questionData}
												currentQuestion={questionNavigation.currentQuestion}
												currentQuestionNumber={questionNavigation.currentQuestionNumber}
												currentStage={raceApi.currentStage || undefined}
												initialStage={initialStage || undefined}
												showAnswer={showAnswer}
												showExplanation={showExplanation}
												rankingData={paginatedRankingData || rankingData}
												rankingLoading={rankingLoading}
												rankingError={rankingError}
												rankingProgress={rankingProgress}
												onRankingRefresh={handleRankingRefresh}
												onImageError={handleImageError}
												onLog={addConsoleLog}
												contentType={currentNode?.contentType}
												nodeName={currentNode?.name}
												selectedProject={selectedProject}
											/>
										);
									}}
								</DynamicComponentRenderer>

								{/* 控台面板 - 固定在底部，限制在 content 区域内 */}
								<View
									UNSAFE_style={{
										flexShrink: 0, // 防止被压缩
										position: "relative",
										/* 优化顶部间距，配合新的分割线设计 */
										marginTop: "12px", // 增加间距，为分割线和阴影留出空间
										/* 添加左右间距，与内容区域保持一致的视觉边距 */
										marginLeft: "16px",
										marginRight: "16px",
										/* 确保分割线和阴影不被裁剪 */
										paddingTop: "4px",
									}}
								>
									<ConsolePanel
										defaultExpanded={false}
										defaultHeight={250}
										minHeight={100}
										maxHeight={400}
										logs={consoleLogs}
										onClearLogs={handleClearConsoleLogs}
										onRefreshStatus={handleRefreshConsoleStatus}
									/>
								</View>
							</View>

							{/* 目录/侧边栏区域 - 仅在大屏幕显示 */}
							<View
								backgroundColor="gray-75"
								gridArea="toc"
								minHeight="size-1000"
								isHidden={{ base: true, L: false }} // 移动端和中等屏幕隐藏，大屏幕显示
							>
								<Flex
									gap={"size-200"}
									direction="column"
									UNSAFE_className="sidebar-scroll-container"
									UNSAFE_style={{
										padding: "24px",
										overflow: "auto",
										maxHeight: "calc(100vh - 200px)",
									}}
								>
									{/* 使用SidebarButtonGroup组件渲染所有侧边栏按钮组 - 支持动态配置 */}
									<DynamicComponentRenderer
										selectedNavigationKey={selectedNavigationKey}
										navigationData={raceApi.navigationData}
										onLog={addConsoleLog}
										handlers={{
											onPackageChange: raceApi.handlers.handlePackageChange,
											onPackageChangeConfirmed: handlePackageChangeConfirmed,
											onQuestionPrevious: questionNavigation.handlers.handlePrevious,
											onQuestionJump: questionNavigation.handlers.handleJump,
											onQuestionNext: questionNavigation.handlers.handleNext,
											currentQuestionNumber: questionNavigation.currentQuestionNumber,
											totalQuestions: questionNavigation.totalQuestions,
											// 安全公开课相关数据
											configurationData: raceApi.configurationData,
											playerInfoData: raceApi.playerListData || undefined
										}}
									>
										{({ buttonGroups }) => {

											// 处理不同页面的按钮组配置
											let finalButtonGroups = buttonGroups;



											if (selectedNavigationKey === 'home') {
												// 只有在配置数据完全加载完成且没有错误时才显示按钮组
												if (raceApi.configurationData && !raceApi.configurationLoading && !raceApi.configurationError) {
													// 配置数据加载成功，使用动态生成的按钮组
													finalButtonGroups = generateHomeButtonGroups(
														raceApi.configurationData,
														(key) => homeConfiguration.handlers.handleConfigurationSelectionChange('leader', key),
														(key) => homeConfiguration.handlers.handleConfigurationSelectionChange('player', key),
														(key) => homeConfiguration.handlers.handleConfigurationSelectionChange('award', key),
														homeConfiguration.handlers.handleResetToMainScreen,
														homeConfiguration.handlers.handleShowContentWithValidation
													);
												} else {
													// 加载中、错误或无数据时不显示按钮组，等待API数据
													// 清空按钮组，不使用静态配置
													finalButtonGroups = [];
												}
											} else if (selectedNavigationKey && raceApi.navigationData) {
												// 递归查找导航节点（支持嵌套结构）
												const findNodeInTree = (nodes: import('./services/api/types').NavigationNode[], targetId: string): import('./services/api/types').NavigationNode | null => {
													for (const node of nodes) {
														if (node.id === targetId) return node;
														if (node.children) {
															const found = findNodeInTree(node.children, targetId);
															if (found) return found;
														}
													}
													return null;
												};

												// 检查是否为题目相关的页面 - 仅根据API的"内容类型"字段匹配
												const selectedNode = findNodeInTree(raceApi.navigationData, selectedNavigationKey);

												// 精确匹配：只有"内容类型"为"题目"或"快答"的节点才显示题目按钮组
												const isQuestionPage = selectedNode && (
													selectedNode.contentType === '题目' ||
													selectedNode.contentType === '快答'
												);

												if (isQuestionPage) {
													// 检查是否应该显示阶段切换按钮组
													const shouldShowStageButtons = selectedNode.initialStage && selectedNode.initialStage !== '通用题';

													// 题目页面，使用动态生成的题目按钮组
													finalButtonGroups = createQuestionButtonGroups(
														questionNavigation.handlers.handlePrevious,
														questionNavigation.handlers.handleJump,
														questionNavigation.handlers.handleNext,
														shouldShowStageButtons ? (stageName: string) => {
															raceApi.handlers.handleStageChange(stageName);
															// 在阶段切换后重置题目导航
															questionNavigation.resetToInitial();
														} : undefined,
														raceApi.handlers.handlePackageChange,
														handlePackageChangeConfirmed, // 新增：确认后的直接执行回调
														handleToggleAnswer,
														questionNavigation.currentQuestionNumber,
														questionNavigation.totalQuestions
													);
												} else if (selectedNode && selectedNode.contentType === '评分') {
													// 安全公开课（评分）页面，使用动态生成的安全公开课按钮组
													finalButtonGroups = createSafetyPublicClassButtonGroupsWithRealData(
														selectedNode.name,
														addConsoleLog,
														raceApi.configurationData // 配置数据包含节目显示和选手显示信息
													);
												}
											}

											return (
												<AnimatePresence mode="wait">
													{finalButtonGroups.length > 0 ? (
														<div
															key={`button-groups-${selectedNavigationKey}-stable`}
															style={{
																display: 'flex',
																flexDirection: 'column',
																gap: 'var(--spectrum-global-dimension-size-200)'
															}}
														>
															{finalButtonGroups.map((config, index) => {
																// 修复重置逻辑：每个组件根据当前选择的类型判断是否需要被重置
																let resetFieldName: string | undefined;
																let resetTimestamp: number | undefined;

																const { timestamp, selectedType } = pickerResetControl;

																if (config.title === "领导显示" && selectedType !== 'leader' && selectedType !== null) {
																	// 当选手或奖项被选择时，领导显示需要被重置
																	resetFieldName = "leaderSelection";
																	resetTimestamp = timestamp;
																} else if (config.title === "选手显示" && selectedType !== 'player' && selectedType !== null) {
																	// 当领导或奖项被选择时，选手显示需要被重置
																	resetFieldName = "playerSelection";
																	resetTimestamp = timestamp;
																} else if (config.title === "奖项显示" && selectedType !== 'award' && selectedType !== null) {
																	// 当领导或选手被选择时，奖项显示需要被重置
																	resetFieldName = "awardSelection";
																	resetTimestamp = timestamp;
																}

																// 优化key策略：题目按钮组使用稳定的key，避免不必要的重新渲染
																const stableKey = config.title === "题目切换"
																	? `sidebar-group-${selectedNavigationKey}-${index}-question-stable`
																	: (config.title === "正方控制" || config.title === "反方控制")
																		? `sidebar-group-${selectedNavigationKey}-${index}-${config.title}-${ultimatePKStageUpdateTrigger}`
																		: `sidebar-group-${selectedNavigationKey}-${index}-${selectedRulesIntroductionItem?.title || 'no-item'}-${raceApi.rulesIntroductionLoading ? 'loading' : 'loaded'}`;

																// 检查配置类型并使用相应的组件
																if (config.type === 'actionGroup') {
																	// 使用 ActionGroup 组件，传递与传统按钮组相同的动画属性
																	return (
																		<SidebarActionGroup
																			key={stableKey}
																			config={config}
																			animated={true}
																			duration={undefined}
																			transition={undefined}
																			reduceMotion={false}
																			groupSwitchAnimation={{ enabled: true, direction: 'right' }}
																		/>
																	);
																}

																// 为规则页面和题目页面的按钮添加特殊处理
																// 只有传统按钮组才需要处理 buttons 和 formFields

																// 传统按钮组的处理逻辑
																let processedButtons = config.buttons;
																let processedFormFields = config.formFields;

																if (config.title === "页面切换") {
																	// 检查规则介绍数据是否可用（与ContentArea渲染条件保持一致）
																	if (selectedRulesIntroductionItem && !raceApi.rulesIntroductionLoading) {
																		// 计算总页数
																		const pages = selectedRulesIntroductionItem.content.split('---').filter(page => page.trim().length > 0);
																		const totalPages = pages.length;

																		// 只有多页时才启用按钮
																		if (totalPages > 1) {
																			processedButtons = config.buttons.map((button, buttonIndex) => ({
																				...button,
																				isDisabled: false,
																				onPress: () => {
																					const targetPage = buttonIndex + 1; // 第一页=1, 第二页=2
																					if (targetPage <= totalPages) {
																						handleRulesPageChange(targetPage);
																					}
																				}
																			}));
																		} else {
																			// 单页时禁用所有按钮
																			processedButtons = config.buttons.map(button => ({
																				...button,
																				isDisabled: true
																			}));
																		}
																	} else {
																		// 数据未准备好时（正在加载或未选中项），禁用所有按钮
																		processedButtons = config.buttons.map(button => ({
																			...button,
																			isDisabled: true
																		}));
																	}
																} else if (config.title === "正方控制" || config.title === "反方控制") {
																	// 终极PK按钮组使用按钮配置中的getter方法处理禁用逻辑
																	processedButtons = config.buttons;
																} else if (config.title === "题目切换") {
																	// 题目按钮组的智能状态管理
																	const hasQuestionData = raceApi.questionData && raceApi.questionData.length > 0;
																	const isQuestionLoading = raceApi.questionLoading;
																	const {
																		currentQuestionNumber,
																		isInitialState,
																		isFirstQuestion,
																		isLastQuestion
																	} = questionNavigation;

																	// 检查题目数据是否可用
																	if (hasQuestionData && !isQuestionLoading) {
																		// 题目数据可用，根据导航状态智能设置按钮状态
																		processedButtons = config.buttons.map((button) => {
																			let isDisabled = false;

																			// 根据按钮文本和当前状态决定是否禁用
																			if (button.text === "上一题") {
																				// 在初始状态或第一题时禁用上一题按钮
																				isDisabled = isInitialState || isFirstQuestion;
																			} else if (button.text === "下一题") {
																				// 在最后一题时禁用下一题按钮
																				isDisabled = isLastQuestion;
																			} else if (button.text === "跳转") {
																				// 跳转按钮在有题目数据时总是启用
																				isDisabled = false;
																			}

																			return {
																				...button,
																				isDisabled
																			};
																		});

																		// 同步表单字段默认值为当前题目序号
																		if (processedFormFields) {
																			processedFormFields = processedFormFields.map(field => {
																				if (field.name === "questionNumber") {
																					return {
																						...field,
																						defaultValue: currentQuestionNumber
																					};
																				}
																				return field;
																			});
																		}


																	} else {
																		// 题目数据不可用或正在加载时，禁用所有按钮
																		processedButtons = config.buttons.map(button => ({
																			...button,
																			isDisabled: true
																		}));


																	}
																} else if (config.title === "数据切换") {
																	// 排名按钮组的分页处理逻辑
																	const hasRankingData = paginatedRankingData && !raceApi.rankingLoading && !raceApi.rankingError;

																	if (hasRankingData) {
																		const { paginationInfo } = paginatedRankingData;

																		processedButtons = config.buttons.map((button) => {
																			// 保持"成绩赋分"按钮不变
																			if (button.text === "成绩赋分") {
																				return button;
																			}

																			// 处理分页按钮
																			if (button.text === "第一页") {
																				return {
																					...button,
																					isDisabled: !paginationInfo.hasMultiplePages || paginationInfo.currentPage === 1,
																					onPress: () => handleRankingPageChange(1)
																				};
																			} else if (button.text === "第二页") {
																				return {
																					...button,
																					isDisabled: !paginationInfo.hasMultiplePages || paginationInfo.currentPage === 2 || paginationInfo.totalPages < 2,
																					onPress: () => handleRankingPageChange(2)
																				};
																			}

																			return button;
																		});
																	} else {
																		// 排名数据不可用时，禁用分页按钮
																		processedButtons = config.buttons.map(button => {
																			if (button.text === "成绩赋分") {
																				return button; // 保持成绩赋分按钮状态不变
																			}
																			return {
																				...button,
																				isDisabled: true
																			};
																		});
																	}
																}

																// 使用传统按钮组组件
																{
																	// 使用传统按钮组组件
																	return (
																		<SidebarButtonGroup
																			key={stableKey}
																			title={config.title}
																			tooltipContent={config.tooltipContent}
																			buttons={processedButtons}
																			formFields={processedFormFields || []}
																			additionalButtons={config.additionalButtons || []}
																			renderOrder={config.renderOrder}
																			showBreakBeforeButtons={config.showBreakBeforeButtons}
																			showBreakBeforeAdditionalButtons={
																				config.showBreakBeforeAdditionalButtons
																			}
																			resetFieldName={resetFieldName}
																			resetTimestamp={resetTimestamp}
																		/>
																	);
																}
															})}
														</div>
													) : null}
												</AnimatePresence>
											);
										}}
									</DynamicComponentRenderer>
								</Flex>
							</View>
							{/* 页面底部区域 */}
							<FooterSection
								mqttIntegration={mqttIntegration}
								raceApi={raceApi}
								version="0.2"
								showUpdateLog={true}
							/>
						</Grid>

						{/* 题包切换确认对话框 */}
						<DialogContainer onDismiss={raceApi.handlers.handlePackageChangeCancel}>
							{packageChangeDialog.isOpen && (
								<AlertDialog
									title="题包切换确认"
									variant="confirmation"
									primaryActionLabel="确认"
									secondaryActionLabel="取消"
									onPrimaryAction={() => {
										raceApi.handlers.handlePackageChangeConfirm();
										// 在确认后重置题目导航
										questionNavigation.resetToInitial();
									}}
									onSecondaryAction={raceApi.handlers.handlePackageChangeCancel}
								>
									即将切换到：{packageChangeDialog.packageName}
								</AlertDialog>
							)}
						</DialogContainer>

						{/* 计时器状态监听组件 - 暂时禁用，使用按钮组配置中的直接DOM更新 */}
						{/* <TimeRaceTimerDisplay /> */}
					</View>
				</Provider>
			</MotionConfig>
		</>
	);
}

// 导出包装了RaceApiProvider的App组件
export default function AppWithProvider() {
	return (
		<RaceApiProvider>
			<App />
		</RaceApiProvider>
	);
}
