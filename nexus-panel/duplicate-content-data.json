{"exactDuplicates": [], "similarContent": [{"files": ["SIMPLE_UPDATE_LOG.md", "UPDATE_LOG_v0.2.md"], "similarities": {"jaccard": 0.8928571428571429, "cosine": 0.9671528397231823, "levenshtein": 0.7358490566037736}, "avgSimilarity": 87, "reason": "High word overlap, Similar word patterns"}], "duplicateHeadings": [{"heading": "问题描述", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 2, "text": "问题描述"}, {"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "问题描述"}, {"file": "RACE_API_SINGLETON_ARCHITECTURE.md", "level": 3, "text": "问题描述"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "问题描述"}], "count": 4}, {"heading": "解决方案", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 2, "text": "解决方案"}, {"file": "MEMORY_STATS_EXPLANATION.md", "level": 3, "text": "解决方案"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "解决方案"}, {"file": "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "level": 2, "text": "解决方案"}], "count": 4}, {"heading": "监控建议", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "监控建议"}, {"file": "MEMORY_STATS_EXPLANATION.md", "level": 2, "text": "监控建议"}], "count": 2}, {"heading": "修复前的问题架构", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 4, "text": "修复前的问题架构"}, {"file": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "修复前的问题架构"}], "count": 2}, {"heading": "修复后的优化架构", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 4, "text": "修复后的优化架构"}, {"file": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "修复后的优化架构"}], "count": 2}, {"heading": "🐛 调试信息", "occurrences": [{"file": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "🐛 调试信息"}, {"file": "HOOK_FIXES_SUMMARY.md", "level": 2, "text": "🐛 调试信息"}], "count": 2}, {"heading": "目录", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 2, "text": "目录"}, {"file": "API_GUIDE.md", "level": 2, "text": "目录"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "目录"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "目录"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "目录"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "目录"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "目录"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "目录"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "目录"}], "count": 9}, {"heading": "核心设计原则", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "核心设计原则"}, {"file": "API_GUIDE.md", "level": 3, "text": "核心设计原则"}], "count": 2}, {"heading": "基础配置", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "基础配置"}, {"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "基础配置"}, {"file": "API_GUIDE.md", "level": 3, "text": "基础配置"}, {"file": "API_GUIDE.md", "level": 3, "text": "基础配置"}], "count": 4}, {"heading": "1. 赛事管理 api", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "1. 赛事管理 API"}, {"file": "API_GUIDE.md", "level": 4, "text": "1. 赛事管理 API"}], "count": 2}, {"heading": "2. 导航数据 api", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "2. 导航数据 API"}, {"file": "API_GUIDE.md", "level": 4, "text": "2. 导航数据 API"}], "count": 2}, {"heading": "3. 题目数据 api", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "3. 题目数据 API"}, {"file": "API_GUIDE.md", "level": 4, "text": "3. 题目数据 API"}], "count": 2}, {"heading": "topic 结构规范", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "Topic 结构规范"}, {"file": "API_GUIDE.md", "level": 3, "text": "Topic 结构规范"}], "count": 2}, {"heading": "域 (domain)", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "域 (Domain)"}, {"file": "API_GUIDE.md", "level": 4, "text": "域 (Domain)"}], "count": 2}, {"heading": "上下文 (context)", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "上下文 (Context)"}, {"file": "API_GUIDE.md", "level": 4, "text": "上下文 (Context)"}], "count": 2}, {"heading": "目标 (target)", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "目标 (Target)"}, {"file": "API_GUIDE.md", "level": 4, "text": "目标 (Target)"}], "count": 2}, {"heading": "动作 (action)", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "动作 (Action)"}, {"file": "API_GUIDE.md", "level": 4, "text": "动作 (Action)"}], "count": 2}, {"heading": "常用 topic 示例", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "常用 Topic 示例"}, {"file": "API_GUIDE.md", "level": 3, "text": "常用 Topic 示例"}], "count": 2}, {"heading": "内部服务 api", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 2, "text": "内部服务 API"}, {"file": "API_GUIDE.md", "level": 2, "text": "内部服务 API"}], "count": 2}, {"heading": "useraceapi hook", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "useRaceApi Hook"}, {"file": "API_GUIDE.md", "level": 4, "text": "useRaceApi Hook"}], "count": 2}, {"heading": "useapi hook", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "useApi Hook"}, {"file": "API_GUIDE.md", "level": 4, "text": "useApi Hook"}], "count": 2}, {"heading": "http client", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "HTTP Client"}, {"file": "API_GUIDE.md", "level": 4, "text": "HTTP Client"}], "count": 2}, {"heading": "mqtt service", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "MQTT Service"}, {"file": "API_GUIDE.md", "level": 4, "text": "MQTT Service"}], "count": 2}, {"heading": "依赖关系图", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "依赖关系图"}, {"file": "MQTT_COMPONENT_ANALYSIS.md", "level": 3, "text": "依赖关系图"}], "count": 2}, {"heading": "总结", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 2, "text": "总结"}, {"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "总结"}, {"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "总结"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "总结"}, {"file": "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "level": 2, "text": "总结"}, {"file": "TimeRaceRanking_Refactor_Comparison.md", "level": 2, "text": "总结"}, {"file": "USERACEAPI_HOOK_FIX.md", "level": 2, "text": "总结"}], "count": 7}, {"heading": "mqtt 数据结构", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "MQTT 数据结构"}, {"file": "API_GUIDE.md", "level": 3, "text": "MQTT 数据结构"}], "count": 2}, {"heading": "sessiondata", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "SessionData"}, {"file": "API_GUIDE.md", "level": 4, "text": "SessionData"}], "count": 2}, {"heading": "rankdata", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "RankData"}, {"file": "API_GUIDE.md", "level": 4, "text": "RankData"}], "count": 2}, {"heading": "playerdata", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "PlayerData"}, {"file": "API_GUIDE.md", "level": 4, "text": "PlayerData"}], "count": 2}, {"heading": "http api 错误处理", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "HTTP API 错误处理"}, {"file": "API_GUIDE.md", "level": 3, "text": "HTTP API 错误处理"}], "count": 2}, {"heading": "常见状态码", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "常见状态码"}, {"file": "API_GUIDE.md", "level": 4, "text": "常见状态码"}], "count": 2}, {"heading": "错误响应格式", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "错误响应格式"}, {"file": "API_GUIDE.md", "level": 4, "text": "错误响应格式"}], "count": 2}, {"heading": "mqtt 连接状态", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 3, "text": "MQTT 连接状态"}, {"file": "API_GUIDE.md", "level": 3, "text": "MQTT 连接状态"}], "count": 2}, {"heading": "连接状态枚举", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "连接状态枚举"}, {"file": "API_GUIDE.md", "level": 4, "text": "连接状态枚举"}], "count": 2}, {"heading": "错误处理策略", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "level": 4, "text": "错误处理策略"}, {"file": "API_GUIDE.md", "level": 4, "text": "错误处理策略"}, {"file": "API_GUIDE.md", "level": 4, "text": "错误处理策略"}], "count": 3}, {"heading": "概述", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "概述"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "概述"}, {"file": "AudioPlayer.md", "level": 2, "text": "概述"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "概述"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "概述"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "概述"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "概述"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "概述"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "概述"}, {"file": "README.md", "level": 2, "text": "概述"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "概述"}, {"file": "SidebarButtonStyles.md", "level": 2, "text": "概述"}, {"file": "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "level": 2, "text": "概述"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "概述"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "概述"}, {"file": "TreeView-CSS-Analysis.md", "level": 2, "text": "概述"}], "count": 16}, {"heading": "架构设计", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "架构设计"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "架构设计"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "架构设计"}], "count": 3}, {"heading": "系统架构", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "系统架构"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "系统架构"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "系统架构"}], "count": 3}, {"heading": "数据流", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "数据流"}, {"file": "ranking_pagination_task_progress.md", "level": 3, "text": "数据流"}], "count": 2}, {"heading": "性能优化", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "性能优化"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "性能优化"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "USERACEAPI_HOOK_FIX.md", "level": 3, "text": "性能优化"}], "count": 7}, {"heading": "性能监控", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "性能监控"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "性能监控"}, {"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 3, "text": "性能监控"}], "count": 3}, {"heading": "错误处理", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "错误处理"}, {"file": "Integration_Verification.md", "level": 3, "text": "错误处理"}, {"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 3, "text": "错误处理"}], "count": 3}, {"heading": "故障排除", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "故障排除"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "故障排除"}, {"file": "MEMORY_STATS_EXPLANATION.md", "level": 2, "text": "故障排除"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "故障排除"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "故障排除"}, {"file": "SidebarButtonStyles.md", "level": 2, "text": "故障排除"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "故障排除"}], "count": 7}, {"heading": "最佳实践", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "最佳实践"}, {"file": "AudioPlayer.md", "level": 2, "text": "最佳实践"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "最佳实践"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "最佳实践"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "最佳实践"}, {"file": "SidebarButtonStyles.md", "level": 2, "text": "最佳实践"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "最佳实践"}], "count": 7}, {"heading": "测试指南", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "测试指南"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "测试指南"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "测试指南"}], "count": 3}, {"heading": "测试步骤", "occurrences": [{"file": "API_GUIDE.md", "level": 4, "text": "测试步骤"}, {"file": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "测试步骤"}], "count": 2}, {"heading": "获取缓存统计信息", "occurrences": [{"file": "API_GUIDE.md", "level": 4, "text": "获取缓存统计信息"}, {"file": "API_REQUEST_TESTING_GUIDE.md", "level": 3, "text": "获取缓存统计信息"}], "count": 2}, {"heading": "手动清理缓存", "occurrences": [{"file": "API_GUIDE.md", "level": 4, "text": "手动清理缓存"}, {"file": "API_REQUEST_TESTING_GUIDE.md", "level": 3, "text": "手动清理缓存"}], "count": 2}, {"heading": "成功标准", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "成功标准"}, {"file": "API_REQUEST_TESTING_GUIDE.md", "level": 3, "text": "成功标准"}, {"file": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 3, "text": "成功标准"}], "count": 3}, {"heading": "相关文档", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "相关文档"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "相关文档"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "相关文档"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "相关文档"}], "count": 8}, {"heading": "更新历史", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "更新历史"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "更新历史"}, {"file": "README.md", "level": 2, "text": "更新历史"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "更新历史"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "更新历史"}], "count": 11}, {"heading": "技术栈", "occurrences": [{"file": "ARCHITECTURE.md", "level": 2, "text": "技术栈"}, {"file": "README.md", "level": 3, "text": "技术栈"}], "count": 2}, {"heading": "组件架构", "occurrences": [{"file": "ARCHITECTURE.md", "level": 2, "text": "组件架构"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "组件架构"}], "count": 2}, {"heading": "渲染优化", "occurrences": [{"file": "ARCHITECTURE.md", "level": 3, "text": "渲染优化"}, {"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "渲染优化"}], "count": 2}, {"heading": "开发环境", "occurrences": [{"file": "ARCHITECTURE.md", "level": 1, "text": "开发环境"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "开发环境"}], "count": 2}, {"heading": "快速开始", "occurrences": [{"file": "AUDIO_PLAYER_RELEASE.md", "level": 3, "text": "快速开始"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "快速开始"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "快速开始"}, {"file": "README.md", "level": 2, "text": "快速开始"}], "count": 4}, {"heading": "🔧 自定义样式", "occurrences": [{"file": "AUDIO_PLAYER_RELEASE.md", "level": 2, "text": "🔧 自定义样式"}, {"file": "SidebarButtonStyles-QuickReference.md", "level": 2, "text": "🔧 自定义样式"}], "count": 2}, {"heading": "📞 技术支持", "occurrences": [{"file": "AUDIO_PLAYER_RELEASE.md", "level": 2, "text": "📞 技术支持"}, {"file": "RELEASE_NOTES.md", "level": 2, "text": "📞 技术支持"}, {"file": "SidebarButtonStyles-QuickReference.md", "level": 2, "text": "📞 技术支持"}], "count": 3}, {"heading": "功能特性", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "功能特性"}], "count": 7}, {"heading": "基础用法", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "基础用法"}, {"file": "RACE_API_SINGLETON_ARCHITECTURE.md", "level": 3, "text": "基础用法"}, {"file": "SidebarButtonStyles-QuickReference.md", "level": 3, "text": "基础用法"}], "count": 3}, {"heading": "api 参考", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "API 参考"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "API 参考"}], "count": 2}, {"heading": "props 接口", "occurrences": [{"file": "AudioPlayer.md", "level": 3, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}], "count": 6}, {"heading": "样式定制", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "样式定制"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "样式定制"}], "count": 2}, {"heading": "支持的音频格式", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "支持的音频格式"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "支持的音频格式"}], "count": 2}, {"heading": "浏览器兼容性", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "浏览器兼容性"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "浏览器兼容性"}], "count": 2}, {"heading": "2. 错误处理", "occurrences": [{"file": "AudioPlayer.md", "level": 3, "text": "2. 错误处理"}, {"file": "MQTT_DEVELOPER_GUIDE.md", "level": 3, "text": "2. 错误处理"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "2. 错误处理"}, {"file": "RACE_API_SINGLETON_ARCHITECTURE.md", "level": 3, "text": "2. 错误处理"}], "count": 4}, {"heading": "3. 性能优化", "occurrences": [{"file": "AudioPlayer.md", "level": 3, "text": "3. 性能优化"}, {"file": "MQTT_DEVELOPER_GUIDE.md", "level": 3, "text": "3. 性能优化"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "3. 性能优化"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 3, "text": "3. 性能优化"}], "count": 4}, {"heading": "常见问题", "occurrences": [{"file": "AudioPlayer.md", "level": 2, "text": "常见问题"}, {"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "常见问题"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "常见问题"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "常见问题"}, {"file": "README.md", "level": 3, "text": "常见问题"}, {"file": "TimeRaceRanking_Usage.md", "level": 3, "text": "常见问题"}], "count": 6}, {"heading": "设计原则", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "设计原则"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 3, "text": "设计原则"}], "count": 2}, {"heading": "目录结构", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "目录结构"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "目录结构"}], "count": 2}, {"heading": "核心组件", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "核心组件"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "核心组件"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 3, "text": "核心组件"}], "count": 3}, {"heading": "使用示例", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}], "count": 7}, {"heading": "自动样式推荐", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "自动样式推荐"}, {"file": "SidebarButtonStyles.md", "level": 3, "text": "自动样式推荐"}], "count": 2}, {"heading": "typescript 规范", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "TypeScript 规范"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 3, "text": "TypeScript 规范"}], "count": 2}, {"heading": "单元测试", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "单元测试"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "单元测试"}, {"file": "Integration_Verification.md", "level": 3, "text": "单元测试"}], "count": 3}, {"heading": "集成测试", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "集成测试"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "集成测试"}, {"file": "Integration_Verification.md", "level": 3, "text": "集成测试"}], "count": 3}, {"heading": "调试技巧", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "调试技巧"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "调试技巧"}, {"file": "INFINITE_LOOP_FIX_V2.md", "level": 3, "text": "调试技巧"}, {"file": "TimeRaceRanking_Usage.md", "level": 3, "text": "调试技巧"}], "count": 4}, {"heading": "克隆项目", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "克隆项目"}, {"file": "README.md", "level": 1, "text": "克隆项目"}], "count": 2}, {"heading": "安装依赖", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "安装依赖"}, {"file": "README.md", "level": 1, "text": "安装依赖"}], "count": 2}, {"heading": "启动开发服务器", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "启动开发服务器"}, {"file": "README.md", "level": 1, "text": "启动开发服务器"}], "count": 2}, {"heading": "构建生产版本", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "构建生产版本"}, {"file": "README.md", "level": 1, "text": "构建生产版本"}], "count": 2}, {"heading": "项目结构", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "项目结构"}, {"file": "README.md", "level": 2, "text": "项目结构"}], "count": 2}, {"heading": "文件命名规范", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 3, "text": "文件命名规范"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 3, "text": "文件命名规范"}], "count": 2}, {"heading": "开发流程", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "开发流程"}, {"file": "README.md", "level": 3, "text": "开发流程"}], "count": 2}, {"heading": "api 请求优化", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "API 请求优化"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "API 请求优化"}], "count": 2}, {"heading": "修复概述", "occurrences": [{"file": "HOOK_FIXES_SUMMARY.md", "level": 2, "text": "修复概述"}, {"file": "USERACEAPI_HOOK_FIX.md", "level": 2, "text": "修复概述"}], "count": 2}, {"heading": "修复效果", "occurrences": [{"file": "HOOK_FIXES_SUMMARY.md", "level": 2, "text": "修复效果"}, {"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "修复效果"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "修复效果"}], "count": 3}, {"heading": "验证方法", "occurrences": [{"file": "HOOK_FIXES_SUMMARY.md", "level": 2, "text": "验证方法"}, {"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "验证方法"}, {"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "验证方法"}, {"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 3, "text": "验证方法"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "验证方法"}, {"file": "TimeRaceRanking_Performance_Test.md", "level": 2, "text": "验证方法"}], "count": 6}, {"heading": "代码质量", "occurrences": [{"file": "ICON_UPDATE_SUMMARY.md", "level": 3, "text": "代码质量"}, {"file": "Integration_Verification.md", "level": 3, "text": "代码质量"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "代码质量"}], "count": 3}, {"heading": "问题背景", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "问题背景"}, {"file": "MEMORY_STATS_EXPLANATION.md", "level": 3, "text": "问题背景"}], "count": 2}, {"heading": "问题根因分析", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "问题根因分析"}, {"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "问题根因分析"}], "count": 2}, {"heading": "修复方案", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "修复方案"}, {"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "修复方案"}], "count": 2}, {"heading": "架构改进", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "架构改进"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "架构改进"}], "count": 2}, {"heading": "状态管理策略", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 3, "text": "状态管理策略"}, {"file": "MEMORY_LEAK_FIX.md", "level": 3, "text": "状态管理策略"}], "count": 2}, {"heading": "1. 构建验证", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 3, "text": "1. 构建验证"}, {"file": "MEMORY_LEAK_FIX.md", "level": 3, "text": "1. 构建验证"}], "count": 2}, {"heading": "2. 运行时验证", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 3, "text": "2. 运行时验证"}, {"file": "MEMORY_LEAK_FIX.md", "level": 3, "text": "2. 运行时验证"}], "count": 2}, {"heading": "经验总结", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "level": 2, "text": "经验总结"}, {"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "经验总结"}], "count": 2}, {"heading": "网络优化", "occurrences": [{"file": "Integration_Verification.md", "level": 3, "text": "网络优化"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 3, "text": "网络优化"}], "count": 2}, {"heading": "性能影响", "occurrences": [{"file": "MEMORY_LEAK_FIX.md", "level": 2, "text": "性能影响"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "性能影响"}], "count": 2}, {"heading": "健康状态评估", "occurrences": [{"file": "MEMORY_STATS_EXPLANATION.md", "level": 2, "text": "健康状态评估"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "健康状态评估"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 3, "text": "健康状态评估"}], "count": 3}, {"heading": "清理策略", "occurrences": [{"file": "MEMORY_STATS_EXPLANATION.md", "level": 2, "text": "清理策略"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "清理策略"}], "count": 2}, {"heading": "性能问题", "occurrences": [{"file": "MEMORY_STATS_EXPLANATION.md", "level": 3, "text": "性能问题"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "性能问题"}], "count": 2}, {"heading": "📋 概述", "occurrences": [{"file": "MQTT_APP_INTEGRATION_EXAMPLE.md", "level": 2, "text": "📋 概述"}, {"file": "MQTT_COMPONENT_ANALYSIS.md", "level": 2, "text": "📋 概述"}], "count": 2}, {"heading": "连接性能", "occurrences": [{"file": "MQTT_CODE_QUALITY_REPORT.md", "level": 3, "text": "连接性能"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "连接性能"}], "count": 2}, {"heading": "消息处理性能", "occurrences": [{"file": "MQTT_CODE_QUALITY_REPORT.md", "level": 3, "text": "消息处理性能"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "消息处理性能"}], "count": 2}, {"heading": "优化建议", "occurrences": [{"file": "MQTT_CODE_QUALITY_REPORT.md", "level": 3, "text": "优化建议"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "优化建议"}], "count": 2}, {"heading": "当前状态", "occurrences": [{"file": "MQTT_CODE_QUALITY_REPORT.md", "level": 3, "text": "当前状态"}, {"file": "MQTT_COMPONENT_ANALYSIS.md", "level": 3, "text": "当前状态"}, {"file": "MQTT_DOCUMENTATION_INDEX.md", "level": 3, "text": "当前状态"}], "count": 3}, {"heading": "数据流向图", "occurrences": [{"file": "MQTT_COMPONENT_ANALYSIS.md", "level": 3, "text": "数据流向图"}, {"file": "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "level": 2, "text": "数据流向图"}], "count": 2}, {"heading": "1. 基础导入", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "level": 3, "text": "1. 基础导入"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "1. 基础导入"}], "count": 2}, {"heading": "2. 基础使用", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "level": 3, "text": "2. 基础使用"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "2. 基础使用"}], "count": 2}, {"heading": "🎯 最佳实践", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "level": 2, "text": "🎯 最佳实践"}, {"file": "SidebarButtonStyles-QuickReference.md", "level": 2, "text": "🎯 最佳实践"}], "count": 2}, {"heading": "1. 连接管理", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "level": 3, "text": "1. 连接管理"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "1. 连接管理"}], "count": 2}, {"heading": "改进建议", "occurrences": [{"file": "MQTT_DOCUMENTATION_INDEX.md", "level": 3, "text": "改进建议"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "改进建议"}], "count": 2}, {"heading": "内存管理", "occurrences": [{"file": "MQTT_GUIDE.md", "level": 2, "text": "内存管理"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 3, "text": "内存管理"}, {"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 3, "text": "内存管理"}], "count": 3}, {"heading": "核心功能", "occurrences": [{"file": "MQTT_GUIDE.md", "level": 3, "text": "核心功能"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "核心功能"}], "count": 2}, {"heading": "使用方法", "occurrences": [{"file": "MQTT_GUIDE.md", "level": 3, "text": "使用方法"}, {"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "使用方法"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "使用方法"}], "count": 3}, {"heading": "配置选项", "occurrences": [{"file": "MQTT_MEMORY_MANAGEMENT.md", "level": 2, "text": "配置选项"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "配置选项"}], "count": 2}, {"heading": "性能测试场景", "occurrences": [{"file": "PERFORMANCE_OPTIMIZATION.md", "level": 3, "text": "性能测试场景"}, {"file": "TimeRaceRanking_Performance_Test.md", "level": 2, "text": "性能测试场景"}], "count": 2}, {"heading": "🧪 测试验证", "occurrences": [{"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 2, "text": "🧪 测试验证"}, {"file": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "level": 2, "text": "🧪 测试验证"}], "count": 2}, {"heading": "🚨 注意事项", "occurrences": [{"file": "PLAYER_DATA_AUTO_FETCH.md", "level": 2, "text": "🚨 注意事项"}, {"file": "RACE_API_SINGLETON_ARCHITECTURE.md", "level": 2, "text": "🚨 注意事项"}], "count": 2}, {"heading": "性能指标", "occurrences": [{"file": "RACE_API_SINGLETON_ARCHITECTURE.md", "level": 3, "text": "性能指标"}, {"file": "TimeRaceRanking_Performance_Test.md", "level": 2, "text": "性能指标"}], "count": 2}, {"heading": "根本原因分析", "occurrences": [{"file": "REALTIME_LOG_COUNT_FIX.md", "level": 2, "text": "根本原因分析"}, {"file": "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "level": 2, "text": "根本原因分析"}], "count": 2}, {"heading": "📋 版本信息", "occurrences": [{"file": "RELEASE_NOTES.md", "level": 2, "text": "📋 版本信息"}, {"file": "SIMPLE_UPDATE_LOG.md", "level": 2, "text": "📋 版本信息"}, {"file": "UPDATE_LOG_v0.2.md", "level": 2, "text": "📋 版本信息"}], "count": 3}, {"heading": "🔧 技术改进", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 2, "text": "🔧 技术改进"}, {"file": "UPDATE_LOG_v0.2.md", "level": 2, "text": "🔧 技术改进"}], "count": 2}, {"heading": "🎯 用户体验提升", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 2, "text": "🎯 用户体验提升"}, {"file": "UPDATE_LOG_v0.2.md", "level": 2, "text": "🎯 用户体验提升"}], "count": 2}, {"heading": "界面布局优化", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 3, "text": "界面布局优化"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "界面布局优化"}], "count": 2}, {"heading": "操作体验改进", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 3, "text": "操作体验改进"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "操作体验改进"}], "count": 2}, {"heading": "设备适配", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 3, "text": "设备适配"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "设备适配"}], "count": 2}, {"heading": "音频播放器使用", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 3, "text": "音频播放器使用"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "音频播放器使用"}], "count": 2}, {"heading": "按钮样式识别", "occurrences": [{"file": "SIMPLE_UPDATE_LOG.md", "level": 3, "text": "按钮样式识别"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "按钮样式识别"}], "count": 2}, {"heading": "混合使用", "occurrences": [{"file": "SidebarButtonStyles-QuickReference.md", "level": 3, "text": "混合使用"}, {"file": "SidebarButtonStyles.md", "level": 3, "text": "混合使用"}], "count": 2}, {"heading": "渐进式迁移", "occurrences": [{"file": "SidebarButtonStyles-QuickReference.md", "level": 3, "text": "渐进式迁移"}, {"file": "SidebarButtonStyles.md", "level": 3, "text": "渐进式迁移"}, {"file": "TimeRaceRanking_Refactor_Comparison.md", "level": 3, "text": "渐进式迁移"}], "count": 3}, {"heading": "样式不生效", "occurrences": [{"file": "SidebarButtonStyles-QuickReference.md", "level": 3, "text": "样式不生效"}, {"file": "SidebarButtonStyles.md", "level": 3, "text": "样式不生效"}], "count": 2}, {"heading": "迁移指南", "occurrences": [{"file": "SidebarButtonStyles.md", "level": 2, "text": "迁移指南"}, {"file": "TimeRaceRanking_Refactor_Comparison.md", "level": 2, "text": "迁移指南"}], "count": 2}, {"heading": "注意事项", "occurrences": [{"file": "TimeRaceRanking_Performance_Test.md", "level": 2, "text": "注意事项"}, {"file": "TimeRaceRanking_Usage.md", "level": 2, "text": "注意事项"}, {"file": "UPDATE_LOG_v0.2.md", "level": 3, "text": "注意事项"}], "count": 3}], "duplicateCodeBlocks": [{"hash": "-2c262b2d", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "unknown", "code": "┌─────────────────────────────────────────────────────────────┐\n│                    前端应用层 (React)  ..."}, {"file": "API_GUIDE.md", "language": "unknown", "code": "┌─────────────────────────────────────────────────────────────┐\n│                    前端应用层 (React)  ..."}], "count": 2}, {"hash": "1571b4a1", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records\n..."}, {"file": "API_ENDPOINTS_ANALYSIS.md", "language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records\n..."}], "count": 2}, {"hash": "174626b3", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}, {"file": "API_GUIDE.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}, {"file": "MQTT_DEVELOPER_GUIDE.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}, {"file": "MQTT_GUIDE.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}], "count": 4}, {"hash": "-4215f19c", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "class MQTTService {\n  connect(): Promise<void>;\n  disconnect(): Promise<void>;\n  publish(topic: stri..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "class MQTTService {\n  connect(): Promise<void>;\n  disconnect(): Promise<void>;\n  publish(topic: stri..."}], "count": 2}, {"hash": "-4e3bb0ec", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "mermaid", "code": "graph TD\n    A[前端应用] --> B[Hook 层]\n    B --> C[服务层]\n    C --> D[HTTP Client]\n    C --> E[MQTT Servic..."}, {"file": "API_GUIDE.md", "language": "mermaid", "code": "graph TD\n    A[前端应用] --> B[Hook 层]\n    B --> C[服务层]\n    C --> D[HTTP Client]\n    C --> E[MQTT Servic..."}], "count": 2}, {"hash": "-112bcb4c", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "interface SessionData {\n  sessionType: string;\n  sessionId: number;\n  sessionName: string;\n  config:..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "interface SessionData {\n  sessionType: string;\n  sessionId: number;\n  sessionName: string;\n  config:..."}], "count": 2}, {"hash": "36669443", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "interface RankData {\n  rankType: 'general' | 'speedrun' | 'speedrun-plus';\n}\n..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "interface RankData {\n  rankType: 'general' | 'speedrun' | 'speedrun-plus';\n}\n..."}], "count": 2}, {"hash": "-25571227", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "interface PlayerData {\n  playerId: number;\n  playerName: string;\n  position: number;\n  status: 'acti..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "interface PlayerData {\n  playerId: number;\n  playerName: string;\n  position: number;\n  status: 'acti..."}], "count": 2}, {"hash": "1e8e8ac3", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "interface ApiError {\n  message: string;\n  code?: string | number;\n  status?: number;\n  originalError..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "interface ApiError {\n  message: string;\n  code?: string | number;\n  status?: number;\n  originalError..."}], "count": 2}, {"hash": "-2eda71c8", "occurrences": [{"file": "API_ENDPOINTS_ANALYSIS.md", "language": "typescript", "code": "enum MQTTConnectionStatus {\n  DISCONNECTED = 'disconnected',\n  CONNECTING = 'connecting',\n  CONNECTE..."}, {"file": "API_GUIDE.md", "language": "typescript", "code": "enum MQTTConnectionStatus {\n  DISCONNECTED = 'disconnected',\n  CONNECTING = 'connecting',\n  CONNECTE..."}], "count": 2}, {"hash": "-4c8e2ea6", "occurrences": [{"file": "API_GUIDE.md", "language": "unknown", "code": "   [GlobalRequestDeduplicator] 🔍 请求去重检查\n   [GlobalTableStructureCache] 🔍 请求表结构数据 (请求#1)\n   [Naviga..."}, {"file": "API_REQUEST_TESTING_GUIDE.md", "language": "unknown", "code": "[GlobalRequestDeduplicator] 🔍 请求去重检查\n[GlobalTableStructureCache] 🔍 请求表结构数据 (请求#1)\n[NavigationApiSe..."}], "count": 2}, {"hash": "-726db29a", "occurrences": [{"file": "API_GUIDE.md", "language": "unknown", "code": "   [GlobalTableStructureCache] 🔍 请求表结构数据 (请求#2)\n   [GlobalTableStructureCache] ✅ 全局缓存命中 (请求#2)\n   ..."}, {"file": "API_REQUEST_TESTING_GUIDE.md", "language": "unknown", "code": "[GlobalTableStructureCache] 🔍 请求表结构数据 (请求#2)\n[GlobalTableStructureCache] ✅ 全局缓存命中 (请求#2)\n..."}], "count": 2}, {"hash": "-e6199e4", "occurrences": [{"file": "API_GUIDE.md", "language": "javascript", "code": "// 清理所有缓存\nimport(\"./src/services/api/tableStructureCache.js\").then((module) => {\n  module.GlobalTabl..."}, {"file": "API_REQUEST_TESTING_GUIDE.md", "language": "javascript", "code": "// 清理所有缓存\nimport(\"./src/services/api/tableStructureCache.js\").then((module) => {\n  module.GlobalTabl..."}], "count": 2}, {"hash": "-1136b566", "occurrences": [{"file": "AUDIO_PLAYER_RELEASE.md", "language": "css", "code": ".audio-player                    /* 主容器 */\n.audio-player-controls          /* 控件容器 */\n.audio-player-..."}, {"file": "AudioPlayer.md", "language": "css", "code": ".audio-player                    /* 主容器 */\n.audio-player-controls          /* 控件容器 */\n.audio-player-..."}, {"file": "COMPONENTS_GUIDE.md", "language": "css", "code": ".audio-player                    /* 主容器 */\n.audio-player-controls          /* 控件容器 */\n.audio-player-..."}], "count": 3}, {"hash": "4b26816", "occurrences": [{"file": "AudioPlayer.md", "language": "typescript", "code": "interface AudioPlayerProps {\n  /** 音频源路径 - 支持本地文件路径或外部 HTTPS URL */\n  audioSrc: string;\n  /** 播放事件回调..."}, {"file": "COMPONENTS_GUIDE.md", "language": "typescript", "code": "interface AudioPlayerProps {\n  /** 音频源路径 - 支持本地文件路径或外部 HTTPS URL */\n  audioSrc: string;\n  /** 播放事件回调..."}], "count": 2}, {"hash": "23e748bb", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "language": "tsx", "code": "import { SidebarButtonGroup, ButtonConfig } from './components/SidebarButtonGroup';\nimport { ButtonS..."}, {"file": "SidebarButtonStyles.md", "language": "typescript", "code": "import { SidebarButtonGroup, ButtonConfig } from './components/SidebarButtonGroup';\nimport { ButtonS..."}], "count": 2}, {"hash": "b6bd87c", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "language": "typescript", "code": "import { suggestButtonStyleType } from './components/SidebarButtonStyles';\n\nconst buttonTexts = ['上一..."}, {"file": "SidebarButtonStyles.md", "language": "typescript", "code": "import { suggestButtonStyleType } from './components/SidebarButtonStyles';\n\nconst buttonTexts = ['上一..."}], "count": 2}, {"hash": "-6ed8dc24", "occurrences": [{"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "language": "markdown", "code": "**版本：** v1.0\n**最后更新：** YYYY-MM-DD\n**维护者：** [负责人]\n**类别：** [category]\n..."}, {"file": "STRUCTURE_OVERVIEW.md", "language": "markdown", "code": "**版本：** v1.0\n**最后更新：** YYYY-MM-DD\n**维护者：** [负责人]\n**类别：** [category]\n..."}], "count": 2}, {"hash": "-76c0301c", "occurrences": [{"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "import VolumeThree from '@spectrum-icons/workflow/VolumeThree';\nimport VolumeMute from '@spectrum-ic..."}, {"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "   import VolumeThree from '@spectrum-icons/workflow/VolumeThree';\n   import VolumeMute from '@spect..."}], "count": 2}, {"hash": "-3f5d982b", "occurrences": [{"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "{isPlaying ? <Pause /> : <Play />}\n..."}, {"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "   {isPlaying ? <Pause /> : <Play />}\n   ..."}], "count": 2}, {"hash": "5f323521", "occurrences": [{"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "{isMuted ? <VolumeMute /> : <VolumeThree />}\n..."}, {"file": "ICON_UPDATE_SUMMARY.md", "language": "typescript", "code": "   {isMuted ? <VolumeMute /> : <VolumeThree />}\n   ..."}], "count": 2}, {"hash": "-53f9ebea", "occurrences": [{"file": "INFINITE_LOOP_FIX_V2.md", "language": "unknown", "code": "Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls set..."}, {"file": "MEMORY_LEAK_FIX.md", "language": "unknown", "code": "Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls set..."}], "count": 2}, {"hash": "-581d6b45", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "language": "typescript", "code": "import {\n  useMQTT,\n  MQTTUtils,\n  MQTTPresets,\n  MQTTDomain,\n  MQTTContext,\n  MQTTAction,\n  MQTTTar..."}, {"file": "MQTT_GUIDE.md", "language": "typescript", "code": "import {\n  useMQTT,\n  MQTTUtils,\n  MQTTPresets,\n  MQTTDomain,\n  MQTTContext,\n  MQTTAction,\n  MQTTTar..."}], "count": 2}, {"hash": "6965b186", "occurrences": [{"file": "MQTT_DEVELOPER_GUIDE.md", "language": "typescript", "code": "// 推荐：使用自动连接\nconst mqtt = useMQTT({\n  config,\n  autoConnect: true,\n});\n\n// 手动连接控制\nuseEffect(() => {\n..."}, {"file": "MQTT_GUIDE.md", "language": "typescript", "code": "// 推荐：使用自动连接\nconst mqtt = useMQTT({\n  config,\n  autoConnect: true,\n});\n\n// 手动连接控制\nuseEffect(() => {\n..."}], "count": 2}]}