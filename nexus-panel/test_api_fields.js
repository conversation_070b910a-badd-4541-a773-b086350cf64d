/**
 * 临时测试脚本 - 验证NocoDB API实际使用的字段名
 * 
 * 这个脚本用于验证我们的假设是否正确
 */

const https = require('https');

// API配置
const API_CONFIG = {
    baseUrl: 'https://noco.ohvfx.com/api/v2',
    token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp'
};

// 测试用的赛事表ID
const RACE_TABLE_ID = 'm19dww1xfzsfipk';

/**
 * 发起HTTP请求
 */
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, {
            method: 'GET',
            headers: {
                'xc-token': API_CONFIG.token,
                'Content-Type': 'application/json',
                ...options.headers
            }
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (error) {
                    reject(new Error(`JSON解析失败: ${error.message}`));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.end();
    });
}

/**
 * 测试赛事表的字段名
 */
async function testRaceTableFields() {
    try {
        console.log('正在测试赛事表字段名...');
        
        const url = `${API_CONFIG.baseUrl}/tables/${RACE_TABLE_ID}/records?limit=1`;
        const response = await makeRequest(url);
        
        if (response.list && response.list.length > 0) {
            const firstRecord = response.list[0];
            const fieldNames = Object.keys(firstRecord);
            
            console.log('\n=== 赛事表实际字段名 ===');
            fieldNames.forEach((field, index) => {
                console.log(`${index + 1}. ${field}`);
            });
            
            console.log('\n=== 示例数据 ===');
            console.log(JSON.stringify(firstRecord, null, 2));
            
            // 检查我们假设的字段名是否存在
            const expectedFields = ['赛事 ID', '赛事名称', '是否显示', '参赛人数'];
            const schemaFields = ['race_id', 'race_name', 'is_visible', 'participant_count'];
            
            console.log('\n=== 字段名验证 ===');
            console.log('当前代码使用的字段名:');
            expectedFields.forEach(field => {
                const exists = fieldNames.includes(field);
                console.log(`  ${field}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
            });
            
            console.log('\nSchema定义的字段名:');
            schemaFields.forEach(field => {
                const exists = fieldNames.includes(field);
                console.log(`  ${field}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
            });
            
        } else {
            console.log('赛事表没有数据');
        }
        
    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

/**
 * 获取项目的表结构
 */
async function getProjectTables(baseId) {
    try {
        console.log(`\n正在获取项目 ${baseId} 的表结构...`);
        
        const url = `${API_CONFIG.baseUrl}/meta/bases/${baseId}/tables`;
        const response = await makeRequest(url);
        
        if (response.list) {
            console.log('\n=== 项目表列表 ===');
            response.list.forEach((table, index) => {
                console.log(`${index + 1}. ${table.title} (ID: ${table.id})`);
            });
            
            return response.list;
        }
        
    } catch (error) {
        console.error('获取表结构失败:', error.message);
        return [];
    }
}

/**
 * 测试特定表的字段
 */
async function testTableFields(tableId, tableName) {
    try {
        console.log(`\n正在测试 ${tableName} 的字段...`);
        
        const url = `${API_CONFIG.baseUrl}/tables/${tableId}/records?limit=1`;
        const response = await makeRequest(url);
        
        if (response.list && response.list.length > 0) {
            const firstRecord = response.list[0];
            const fieldNames = Object.keys(firstRecord);
            
            console.log(`\n=== ${tableName} 字段名 ===`);
            fieldNames.forEach((field, index) => {
                console.log(`${index + 1}. "${field}"`);
            });
            
            return fieldNames;
        } else {
            console.log(`${tableName} 没有数据`);
            return [];
        }
        
    } catch (error) {
        console.error(`测试 ${tableName} 失败:`, error.message);
        return [];
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('开始API字段验证测试...\n');
    
    // 首先测试赛事表
    await testRaceTableFields();
    
    // 如果需要测试其他表，可以先获取项目的表结构
    // 这里需要一个实际的baseId
    console.log('\n如果需要测试其他表，请提供项目的baseId');
    console.log('例如: node test_api_fields.js <baseId>');
    
    const baseId = process.argv[2];
    if (baseId) {
        const tables = await getProjectTables(baseId);
        
        // 测试几个关键表
        const keyTables = ['题目表', '选手表', '答题记录表', '配置信息表'];
        
        for (const table of tables) {
            if (keyTables.some(key => table.title.includes(key))) {
                await testTableFields(table.id, table.title);
            }
        }
    }
}

// 运行测试
main().catch(console.error);
