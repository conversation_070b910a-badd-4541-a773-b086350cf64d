import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
	plugins: [
		react({
			// 启用 React Compiler
			babel: {
				plugins: [
					["babel-plugin-react-compiler", {
						// React Compiler 配置选项
						compilationMode: "annotation", // 可选：'annotation' | 'all'
						panicThreshold: "all_errors", // 可选：遇到错误时的处理方式
					}]
				]
			}
		})
	],
	define: {
		// 为 Adobe React Spectrum 提供 process 对象的 polyfill
		global: "globalThis",
		"process.env": {},
	},
});
