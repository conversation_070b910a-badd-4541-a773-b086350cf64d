# Nexus Panel

Nexus Panel 是一个为互动问答/竞赛活动构建的实时竞赛管理仪表板。该应用程序为竞技游戏场景提供实时监控、参与者管理和动态内容交付。

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 📚 文档

完整的项目文档位于 `../docs/` 目录：

### 核心文档
- [项目概览](../docs/README.md) - 项目总览和快速开始
- [系统架构](../docs/ARCHITECTURE.md) - 系统整体架构和设计原则
- [开发指南](../docs/DEVELOPMENT_GUIDE.md) - 开发环境搭建和开发规范

### 技术指南
- [API 使用指南](../docs/API_GUIDE.md) - API 接口文档和使用示例
- [组件开发指南](../docs/COMPONENTS_GUIDE.md) - UI 组件开发和使用指南
- [MQTT 集成指南](../docs/MQTT_GUIDE.md) - MQTT 实时通信完整指南

### 运维指南
- [性能优化指南](../docs/PERFORMANCE_OPTIMIZATION.md) - 性能优化策略和最佳实践
- [故障排除指南](../docs/TROUBLESHOOTING.md) - 常见问题和解决方案

## 🛠 技术栈

- **React** 19.1.0 + **TypeScript** 5.8.3
- **Vite** 6.3.5 - 快速构建工具
- **Adobe React Spectrum** - UI 设计系统
- **MQTT** 5.13.1 - 实时通信

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config({
  plugins: {
    // Add the react-x and react-dom plugins
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    // other rules...
    // Enable its recommended typescript rules
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
})
```
