{"name": "nexus-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "docs:validate": "node ../scripts/validate-docs.js", "docs:check-links": "node ../scripts/validate-docs.js", "docs:verify-code-refs": "node ../scripts/validate-docs.js", "docs:check-duplicates": "node ../scripts/check-duplicate-content.js", "docs:test-suite": "node ../scripts/docs-test-suite.js", "docs:analyze": "node ../scripts/analyze-docs.js", "docs:full-check": "npm run docs:check-links && npm run docs:verify-code-refs && npm run docs:check-duplicates", "docs:lint": "echo 'Markdown linting not configured yet'", "docs:maintenance-init": "node ../scripts/docs-maintenance-scheduler.js init", "docs:maintenance-daily": "node ../scripts/docs-maintenance-scheduler.js daily", "docs:maintenance-status": "node ../scripts/docs-maintenance-scheduler.js status", "docs:maintenance-check": "node ../scripts/docs-maintenance-scheduler.js check", "docs:generate-quality-report": "echo 'Quality report generation not implemented yet'", "docs:generate-weekly-reminder": "node ../scripts/docs-maintenance-scheduler.js weekly-reminder", "docs:generate-monthly-plan": "node ../scripts/docs-maintenance-scheduler.js monthly-plan"}, "dependencies": {"@adobe/react-spectrum": "^3.42.1", "@react-spectrum/tree": "^3.1.3", "@spectrum-icons/illustrations": "^3.6.23", "@spectrum-icons/workflow": "^4.2.22", "@types/mqtt": "^0.0.34", "@types/uuid": "^10.0.0", "babel-plugin-react-compiler": "^19.1.0-rc.2", "motion": "^12.23.0", "mqtt": "^5.13.1", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.6.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}