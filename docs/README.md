# Nexus Panel 文档

**版本：** v1.0  
**最后更新：** 2025-01-15  
**维护者：** 开发团队  
**类别：** development

## 概述

欢迎使用 Nexus Panel 文档系统。本文档集合为互动问答/竞赛活动管理仪表板提供完整的开发和使用指南。

## 快速开始

### 项目概述

Nexus Panel 是一个基于 React + TypeScript + Vite 构建的实时竞赛管理仪表板，支持：

- 实时竞赛监控和参与者管理
- 动态题目管理和多种题目类型
- MQTT 实时通信集成
- 基于 Adobe React Spectrum 的交互界面
- 多阶段竞赛支持（快答、终极 PK 等）
- 音频集成和多媒体内容支持

### 技术栈

- **前端**: React 19.1.0 + TypeScript 5.8.3
- **构建工具**: Vite 6.3.5
- **UI 框架**: Adobe React Spectrum 3.42.1
- **实时通信**: MQTT 5.13.1

## 🚀 快速导航

### 新开发者入门
1. [开发指南](./DEVELOPMENT_GUIDE.md) - 环境搭建和开发流程
2. [架构文档](./ARCHITECTURE.md) - 系统架构和设计理念
3. [项目结构](./STRUCTURE_OVERVIEW.md) - 代码组织和目录结构

### 功能开发
4. [API 指南](./API_GUIDE.md) - API 使用和数据管理
5. [组件指南](./COMPONENTS_GUIDE.md) - UI 组件开发规范
6. [MQTT 指南](./MQTT_GUIDE.md) - 实时通信集成

### 问题解决
7. [故障排除](./TROUBLESHOOTING.md) - 常见问题和解决方案
8. [性能优化](./PERFORMANCE_OPTIMIZATION.md) - 性能调优指南

## 文档导航

### 核心指南

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [系统架构](./ARCHITECTURE.md) | 系统整体架构和设计原则 | 架构师、高级开发者 |
| [开发指南](./DEVELOPMENT_GUIDE.md) | 开发环境搭建和开发规范 | 所有开发者 |
| [API 使用指南](./API_GUIDE.md) | API 接口文档和使用示例 | 前端开发者 |
| [组件开发指南](./COMPONENTS_GUIDE.md) | UI 组件开发和使用指南 | 前端开发者 |

### 专项指南

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [MQTT 集成指南](./MQTT_GUIDE.md) | MQTT 实时通信完整指南 | 后端开发者、集成工程师 |
| [性能优化指南](./PERFORMANCE_OPTIMIZATION.md) | 性能优化策略和最佳实践 | 高级开发者 |
| [故障排除指南](./TROUBLESHOOTING.md) | 常见问题和解决方案 | 所有开发者 |

### 项目管理

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [发布说明](./RELEASE_NOTES.md) | 版本更新和变更记录 | 所有用户 |
| [文档维护指南](./DOCUMENTATION_MAINTENANCE_GUIDE.md) | 文档维护流程和规范 | 文档维护者 |

## 开发环境搭建

### 前置要求

- Node.js 18+ 
- npm 或 yarn
- Git

### 快速启动

```bash
# 克隆项目
git clone [repository-url]
cd nexus-panel

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 开发工具

```bash
# 代码检查
npm run lint

# 文档验证和维护
npm run docs:validate          # 完整文档验证
npm run docs:check-links       # 检查文档链接
npm run docs:verify-code-refs  # 验证代码引用
npm run docs:check-duplicates  # 检测重复内容
npm run docs:validate-structure # 验证文档结构

# 运行测试
npm test
```

## 项目结构

```
nexus-panel/
├── src/                        # 源代码
│   ├── components/             # React 组件
│   ├── hooks/                  # 自定义 Hooks
│   ├── services/               # 服务层 (API, MQTT)
│   ├── config/                 # 配置文件
│   ├── types/                  # TypeScript 类型定义
│   └── utils/                  # 工具函数
├── public/                     # 静态资源
├── docs/                       # 文档 (本目录)
└── scripts/                    # 构建和维护脚本
```

## 贡献指南

### 开发流程

1. 创建功能分支
2. 开发和测试
3. 更新相关文档
4. 提交 Pull Request
5. 代码审查和合并

### 文档贡献

1. 使用适当的[文档模板](./templates/)
2. 遵循[文档维护指南](./DOCUMENTATION_MAINTENANCE_GUIDE.md)
3. 运行文档验证：`npm run docs:validate`
4. 更新版本号和日期

## 获取帮助

### 常见问题

首先查看[故障排除指南](./TROUBLESHOOTING.md)中的常见问题解决方案。

### 技术支持

- 查看相关技术文档
- 搜索项目 Issues
- 联系开发团队

## 文档模板

在 `templates/` 目录下提供了标准文档模板：

- [标准模板](./templates/STANDARD_TEMPLATE.md) - 通用文档模板
- [API 指南模板](./templates/API_GUIDE_TEMPLATE.md) - API 文档模板  
- [组件指南模板](./templates/COMPONENT_GUIDE_TEMPLATE.md) - 组件文档模板
- [元数据规范](./templates/metadata-schema.json) - 文档元数据标准

## 更新历史

- v1.0 (2025-01-15): 初始版本，建立新的文档结构和模板系统