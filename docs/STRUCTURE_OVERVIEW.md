# 文档结构概览

**版本：** v1.0  
**最后更新：** 2025-01-15  
**维护者：** 开发团队  
**类别：** development

## 概述

本文档描述了 Nexus Panel 项目的新文档结构，包括目录组织、文件分类和维护规范。

## 目录结构

### 当前结构（过渡期）

```
docs/
├── README.md                           # 📖 项目文档入口
├── DOCUMENTATION_MAINTENANCE_GUIDE.md  # 📋 文档维护指南
├── STRUCTURE_OVERVIEW.md              # 📁 本文档
├── templates/                          # 📝 文档模板
│   ├── STANDARD_TEMPLATE.md           #   └─ 标准文档模板
│   ├── API_GUIDE_TEMPLATE.md          #   └─ API 指南模板
│   ├── COMPONENT_GUIDE_TEMPLATE.md    #   └─ 组件指南模板
│   └── metadata-schema.json           #   └─ 元数据规范

└── [现有文档文件...]                   # 🔄 待整合的现有文档
```

### 目标结构（整合后）

```
docs/
├── README.md                           # 📖 项目总览和快速开始
├── ARCHITECTURE.md                     # 🏗️ 系统架构文档
├── API_GUIDE.md                        # 🔌 API 使用指南
├── COMPONENTS_GUIDE.md                 # 🧩 组件开发指南
├── MQTT_GUIDE.md                       # 📡 MQTT 完整指南
├── TROUBLESHOOTING.md                  # 🔧 故障排除指南
├── PERFORMANCE_OPTIMIZATION.md         # ⚡ 性能优化指南
├── DEVELOPMENT_GUIDE.md                # 👨‍💻 开发指南和最佳实践
├── RELEASE_NOTES.md                    # 📋 发布说明
├── DOCUMENTATION_MAINTENANCE_GUIDE.md  # 📋 文档维护指南
├── templates/                          # 📝 文档模板系统

```

## 文档分类

### 核心指南 (Core Guides)

| 文档 | 状态 | 描述 |
|------|------|------|
| `README.md` | ✅ 已创建 | 项目入口，快速开始指南 |
| `ARCHITECTURE.md` | 🔄 待创建 | 系统架构和设计原则 |
| `DEVELOPMENT_GUIDE.md` | 🔄 待创建 | 开发环境和规范 |

### 技术指南 (Technical Guides)

| 文档 | 状态 | 整合来源 |
|------|------|----------|
| `API_GUIDE.md` | 🔄 待整合 | API_*_ANALYSIS.md, RACE_API_*.md 等 |
| `COMPONENTS_GUIDE.md` | 🔄 待整合 | AudioPlayer.md, SidebarButtonStyles.md 等 |
| `MQTT_GUIDE.md` | 🔄 待整合 | MQTT_*.md 系列文档 |

### 运维指南 (Operations Guides)

| 文档 | 状态 | 整合来源 |
|------|------|----------|
| `TROUBLESHOOTING.md` | 🔄 待整合 | *_FIX*.md, *_ISSUE*.md 等 |
| `PERFORMANCE_OPTIMIZATION.md` | 🔄 待整合 | Performance_Test.md, MEMORY_*.md 等 |

### 项目管理 (Project Management)

| 文档 | 状态 | 描述 |
|------|------|------|
| `RELEASE_NOTES.md` | 🔄 需更新 | 版本发布记录 |
| `DOCUMENTATION_MAINTENANCE_GUIDE.md` | ✅ 已创建 | 文档维护流程 |

## 模板系统

### 可用模板

1. **标准模板** (`STANDARD_TEMPLATE.md`)
   - 适用于通用文档
   - 包含标准章节结构
   - 元数据规范完整

2. **API 指南模板** (`API_GUIDE_TEMPLATE.md`)
   - 专门用于 API 文档
   - 包含端点、参数、示例等章节
   - 适合技术接口文档

3. **组件指南模板** (`COMPONENT_GUIDE_TEMPLATE.md`)
   - 用于 UI 组件文档
   - 包含 Props、样式、使用示例
   - 适合前端组件说明

### 元数据规范

所有文档必须包含以下元数据：

```markdown
**版本：** v1.0
**最后更新：** YYYY-MM-DD
**维护者：** [负责人]
**类别：** [category]
```

支持的类别：
- `api` - API 相关文档
- `components` - 组件开发文档
- `mqtt` - MQTT 集成文档
- `architecture` - 架构设计文档
- `troubleshooting` - 故障排除文档
- `performance` - 性能优化文档
- `development` - 开发指南文档

## 维护工具

### 验证脚本

```bash
# 完整文档验证
npm run docs:validate

# 检查链接有效性
npm run docs:check-links

# 验证代码引用
npm run docs:verify-code-refs

# 检测重复内容
npm run docs:check-duplicates

# 验证文档结构
npm run docs:validate-structure
```

### 验证内容

- ✅ 文档结构和目录组织
- ✅ 元数据完整性和格式
- ✅ 内部链接有效性
- ✅ 重复内容检测
- 🔄 代码引用验证（待实现）
- 🔄 外部链接检查（待实现）

## 迁移计划

### 阶段 1: 基础设施 ✅
- [x] 创建目录结构
- [x] 建立模板系统
- [x] 实现验证工具
- [x] 制定维护规范

### 阶段 2: 内容整合 🔄
- [ ] 整合 API 相关文档
- [ ] 整合 MQTT 相关文档
- [ ] 整合组件相关文档
- [ ] 整合故障排除文档

### 阶段 3: 清理归档 🔄
- [ ] 移动过时文档到归档
- [ ] 删除重复文档
- [ ] 更新所有交叉引用

### 阶段 4: 质量保证 🔄
- [ ] 完整验证测试
- [ ] 用户体验测试
- [ ] 最终调整优化

## 使用指南

### 创建新文档

1. 选择合适的模板
2. 复制模板文件
3. 填写元数据信息
4. 编写文档内容
5. 运行验证检查

### 更新现有文档

1. 修改文档内容
2. 更新版本号
3. 更新最后修改日期
4. 记录更新历史
5. 运行验证检查

### 质量检查

使用文档维护指南中的[质量检查清单](./DOCUMENTATION_MAINTENANCE_GUIDE.md#📋-内容质量检查清单)确保文档质量。

## 相关文档

- [文档维护指南](./DOCUMENTATION_MAINTENANCE_GUIDE.md)
- [项目 README](./README.md)
- [模板目录](./templates/)

## 更新历史

- v1.0 (2025-01-15): 初始版本，建立文档结构概览