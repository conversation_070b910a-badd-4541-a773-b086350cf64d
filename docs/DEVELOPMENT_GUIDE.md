# 开发指南

**版本：** v1.0  
**最后更新：** 2025-01-15  
**维护者：** 开发团队  
**类别：** development

## 概述

本文档为 Nexus Panel 项目的完整开发指南，涵盖开发环境搭建、代码规范、开发流程、测试策略和最佳实践。无论你是新加入的开发者还是项目维护者，都可以通过本指南快速上手项目开发。

## 目录

- [快速开始](#快速开始)
- [开发环境](#开发环境)
- [项目结构](#项目结构)
- [代码规范](#代码规范)
- [开发流程](#开发流程)
- [测试指南](#测试指南)
- [调试技巧](#调试技巧)
- [性能优化](#性能优化)
- [常见问题](#常见问题)

## 快速开始

### 环境要求

- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本 (推荐使用 npm)
- **Git**: 2.30.0 或更高版本
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 克隆和安装

```bash
# 克隆项目
git clone [repository-url]
cd nexus-panel

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 在浏览器中打开 http://localhost:5173
```

### 验证安装

```bash
# 检查代码质量
npm run lint

# 运行测试 (如果有)
npm test

# 构建生产版本
npm run build
```

## 开发环境

### 推荐的开发工具

#### 代码编辑器
- **VS Code** (推荐)
  - 安装 TypeScript 扩展
  - 安装 ESLint 扩展
  - 安装 Prettier 扩展
  - 安装 React 相关扩展

#### 浏览器扩展
- **React Developer Tools**: React 组件调试
- **Redux DevTools**: 状态管理调试 (如果使用)
- **MQTT.fx** 或 **MQTTX**: MQTT 消息调试

### 开发服务器配置

**Vite 配置** (`vite.config.ts`):
```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: true, // 允许外部访问
    open: true, // 自动打开浏览器
  },
  build: {
    target: 'es2020',
    outDir: 'dist',
  },
});
```

**开发环境变量**:
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000
VITE_MQTT_BROKER_URL=wss://ws.ohvfx.com:8084/mqtt
VITE_DEBUG_MODE=true
```

### TypeScript 配置

**严格模式配置** (`tsconfig.json`):
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 项目结构

### 目录组织原则

```
nexus-panel/
├── src/                        # 源代码目录
│   ├── components/             # React 组件
│   │   ├── common/            # 通用组件
│   │   ├── layout/            # 布局组件
│   │   ├── question/          # 业务组件
│   │   └── [Feature]/         # 功能模块组件
│   ├── hooks/                  # 自定义 Hooks
│   │   ├── useApi.ts          # API 相关 Hooks
│   │   ├── useMQTT*.ts        # MQTT 相关 Hooks
│   │   └── use[Feature].ts    # 功能特定 Hooks
│   ├── services/               # 服务层
│   │   ├── api/               # HTTP API 服务
│   │   └── mqtt/              # MQTT 服务
│   ├── config/                 # 配置文件
│   ├── types/                  # 类型定义
│   ├── utils/                  # 工具函数
│   └── contexts/               # React Context
├── public/                     # 静态资源
├── docs/                       # 项目文档
└── scripts/                    # 构建和维护脚本
```

### 文件命名规范

| 类型 | 命名规范 | 示例 |
|------|----------|------|
| React 组件 | PascalCase | `TimeRaceTimer.tsx` |
| Hooks | camelCase + use前缀 | `useRaceApi.ts` |
| 服务类 | PascalCase | `MQTTService.ts` |
| 工具函数 | camelCase | `rankingUtils.ts` |
| 类型定义 | PascalCase | `ApiResponse` |
| 常量 | UPPER_SNAKE_CASE | `API_BASE_URL` |
| CSS 文件 | 匹配组件名 | `TimeRaceTimer.css` |

## 代码规范

### TypeScript 规范

#### 类型定义

```typescript
// ✅ 推荐：明确的接口定义
interface UserData {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

// ✅ 推荐：使用联合类型
type Status = 'loading' | 'success' | 'error';

// ✅ 推荐：泛型约束
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// ❌ 避免：使用 any
const userData: any = fetchUser(); // 不推荐

// ✅ 推荐：使用 unknown 或具体类型
const userData: UserData = await fetchUser();
```

#### 函数定义

```typescript
// ✅ 推荐：明确的参数和返回类型
const calculateScore = (
  answers: Answer[],
  totalQuestions: number
): ScoreResult => {
  // 实现逻辑
  return {
    score: calculatedScore,
    percentage: (calculatedScore / totalQuestions) * 100,
  };
};

// ✅ 推荐：使用可选参数
const fetchData = async (
  endpoint: string,
  options?: RequestOptions
): Promise<ApiResponse> => {
  // 实现逻辑
};
```

### React 组件规范

#### 函数组件

```typescript
// ✅ 推荐：使用 React.FC 或直接函数声明
interface Props {
  title: string;
  onSubmit: (data: FormData) => void;
  loading?: boolean;
}

const MyComponent: React.FC<Props> = ({ 
  title, 
  onSubmit, 
  loading = false 
}) => {
  // 组件逻辑
  return (
    <div>
      <h1>{title}</h1>
      {/* 组件内容 */}
    </div>
  );
};

export default MyComponent;
```

#### Hooks 使用

```typescript
// ✅ 推荐：自定义 Hook
const useRaceData = (raceId: string) => {
  const [data, setData] = useState<RaceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await getRaceById(raceId);
        setData(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [raceId]);

  return { data, loading, error };
};
```

#### 性能优化

```typescript
// ✅ 推荐：使用 React.memo
const ExpensiveComponent = React.memo<Props>(({ data, onUpdate }) => {
  // 组件实现
});

// ✅ 推荐：使用 useCallback
const MyComponent: React.FC<Props> = ({ items, onItemClick }) => {
  const handleClick = useCallback((id: string) => {
    onItemClick(id);
  }, [onItemClick]);

  // ✅ 推荐：使用 useMemo
  const sortedItems = useMemo(() => {
    return items.sort((a, b) => a.name.localeCompare(b.name));
  }, [items]);

  return (
    <div>
      {sortedItems.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onClick={handleClick}
        />
      ))}
    </div>
  );
};
```

### CSS 规范

#### 样式组织

```css
/* ✅ 推荐：BEM 命名规范 */
.time-race-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-race-timer__display {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

.time-race-timer__controls {
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
}

.time-race-timer__button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.time-race-timer__button--primary {
  background-color: #007bff;
  color: white;
}

.time-race-timer__button--secondary {
  background-color: #6c757d;
  color: white;
}
```

#### CSS 变量使用

```css
/* ✅ 推荐：使用 CSS 变量 */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.25rem;
  
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
}

.component {
  color: var(--primary-color);
  font-size: var(--font-size-base);
  padding: var(--spacing-md);
}
```

## 开发流程

### Git 工作流

#### 分支策略

```bash
# 主分支
main                    # 生产环境代码
develop                 # 开发环境代码

# 功能分支
feature/user-auth      # 新功能开发
feature/mqtt-integration

# 修复分支
hotfix/critical-bug    # 紧急修复
bugfix/minor-issue     # 一般修复

# 发布分支
release/v1.2.0         # 版本发布准备
```

#### 提交规范

```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      修复 bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建工具或辅助工具的变动

# 示例
feat(api): 添加用户认证接口

- 实现 JWT token 验证
- 添加用户登录和注销功能
- 更新 API 文档

Closes #123
```

### 开发工作流程

1. **创建功能分支**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/new-feature
   ```

2. **开发和测试**
   ```bash
   # 开发过程中频繁提交
   git add .
   git commit -m "feat: 实现基础功能"
   
   # 运行测试
   npm run lint
   npm test
   ```

3. **代码审查准备**
   ```bash
   # 整理提交历史
   git rebase -i develop
   
   # 推送到远程
   git push origin feature/new-feature
   ```

4. **创建 Pull Request**
   - 填写详细的 PR 描述
   - 关联相关的 Issue
   - 请求代码审查

5. **合并和清理**
   ```bash
   # 合并后清理本地分支
   git checkout develop
   git pull origin develop
   git branch -d feature/new-feature
   ```

### 代码审查清单

#### 功能性检查
- [ ] 功能是否按需求正确实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

#### 代码质量检查
- [ ] 代码是否遵循项目规范
- [ ] 变量和函数命名是否清晰
- [ ] 是否有重复代码
- [ ] 注释是否充分和准确

#### 安全性检查
- [ ] 输入验证是否充分
- [ ] 敏感信息是否泄露
- [ ] XSS 和其他安全漏洞

#### 测试检查
- [ ] 单元测试是否充分
- [ ] 集成测试是否通过
- [ ] 手动测试是否完成

## 测试指南

### 测试策略

#### 测试金字塔

```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (适量)
/________\  Unit Tests (大量)
```

#### 单元测试

```typescript
// 使用 Jest 和 React Testing Library
import { render, screen, fireEvent } from '@testing-library/react';
import { TimeRaceTimer } from './TimeRaceTimer';

describe('TimeRaceTimer', () => {
  it('应该显示初始时间', () => {
    render(<TimeRaceTimer initialTime={60} />);
    expect(screen.getByText('01:00')).toBeInTheDocument();
  });

  it('应该在点击开始按钮后开始计时', () => {
    render(<TimeRaceTimer initialTime={60} />);
    const startButton = screen.getByText('开始');
    
    fireEvent.click(startButton);
    
    expect(screen.getByText('暂停')).toBeInTheDocument();
  });
});
```

#### 集成测试

```typescript
// API 集成测试
import { getRaceData } from '../services/api/raceApi';

describe('Race API Integration', () => {
  it('应该获取赛事数据', async () => {
    const data = await getRaceData();
    
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);
    expect(data.length).toBeGreaterThan(0);
  });
});
```

#### MQTT 测试

```typescript
// MQTT 服务测试
import { MQTTService } from '../services/mqtt/MQTTService';

describe('MQTT Service', () => {
  let mqttService: MQTTService;

  beforeEach(() => {
    mqttService = new MQTTService();
  });

  it('应该成功连接到 MQTT broker', async () => {
    const config = {
      brokerUrl: 'wss://test-broker.com',
      clientId: 'test-client',
    };

    await expect(mqttService.connect(config)).resolves.not.toThrow();
  });
});
```

### 测试运行

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- TimeRaceTimer.test.tsx

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监听模式运行测试
npm test -- --watch
```

## 调试技巧

### React 调试

#### React Developer Tools

```typescript
// 在组件中添加调试信息
const MyComponent: React.FC<Props> = (props) => {
  // 开发环境下的调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log('MyComponent props:', props);
  }

  return <div>{/* 组件内容 */}</div>;
};

// 使用 displayName 便于调试
MyComponent.displayName = 'MyComponent';
```

#### 性能分析

```typescript
// 使用 React Profiler
import { Profiler } from 'react';

const onRenderCallback = (id, phase, actualDuration) => {
  console.log('Profiler:', { id, phase, actualDuration });
};

<Profiler id="MyComponent" onRender={onRenderCallback}>
  <MyComponent />
</Profiler>
```

### API 调试

#### 网络请求调试

```typescript
// 在 HTTP 客户端中添加调试日志
const httpClient = {
  async get(url: string) {
    console.log('API Request:', url);
    const response = await fetch(url);
    console.log('API Response:', response.status, response.statusText);
    return response;
  }
};
```

#### MQTT 调试

```typescript
// MQTT 消息调试
const mqttService = new MQTTService();

mqttService.on('message', (topic, message) => {
  console.log('MQTT Message:', { topic, message: message.toString() });
});

mqttService.on('connect', () => {
  console.log('MQTT Connected');
});

mqttService.on('error', (error) => {
  console.error('MQTT Error:', error);
});
```

### 浏览器调试工具

#### 控制台调试

```typescript
// 条件断点
if (someCondition) {
  debugger; // 只在特定条件下暂停
}

// 性能标记
console.time('expensive-operation');
performExpensiveOperation();
console.timeEnd('expensive-operation');

// 分组日志
console.group('API Calls');
console.log('Request 1');
console.log('Request 2');
console.groupEnd();
```

## 性能优化

### 渲染性能

#### 避免不必要的重渲染

```typescript
// ✅ 使用 React.memo
const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <div>{/* 复杂的渲染逻辑 */}</div>;
});

// ✅ 使用 useMemo 缓存计算结果
const MyComponent: React.FC<Props> = ({ items }) => {
  const expensiveValue = useMemo(() => {
    return items.reduce((sum, item) => sum + item.value, 0);
  }, [items]);

  return <div>{expensiveValue}</div>;
};

// ✅ 使用 useCallback 缓存函数
const MyComponent: React.FC<Props> = ({ onItemClick }) => {
  const handleClick = useCallback((id: string) => {
    onItemClick(id);
  }, [onItemClick]);

  return <button onClick={() => handleClick('123')}>Click</button>;
};
```

#### 列表渲染优化

```typescript
// ✅ 使用稳定的 key
const ItemList: React.FC<{ items: Item[] }> = ({ items }) => {
  return (
    <div>
      {items.map(item => (
        <ItemComponent 
          key={item.id} // 使用稳定的 ID 作为 key
          data={item} 
        />
      ))}
    </div>
  );
};

// ✅ 虚拟滚动（对于大列表）
import { FixedSizeList as List } from 'react-window';

const VirtualizedList: React.FC<{ items: Item[] }> = ({ items }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <ItemComponent data={items[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={items.length}
      itemSize={50}
    >
      {Row}
    </List>
  );
};
```

### 网络性能

#### API 请求优化

```typescript
// ✅ 请求去重
const requestCache = new Map();

const fetchWithDeduplication = async (url: string) => {
  if (requestCache.has(url)) {
    return requestCache.get(url);
  }

  const promise = fetch(url).then(res => res.json());
  requestCache.set(url, promise);

  try {
    const result = await promise;
    return result;
  } finally {
    // 清理缓存
    setTimeout(() => requestCache.delete(url), 5000);
  }
};

// ✅ 并行请求
const fetchMultipleData = async () => {
  const [races, questions, rankings] = await Promise.all([
    fetchRaces(),
    fetchQuestions(),
    fetchRankings(),
  ]);

  return { races, questions, rankings };
};
```

#### 资源加载优化

```typescript
// ✅ 懒加载组件
const LazyComponent = React.lazy(() => import('./ExpensiveComponent'));

const App: React.FC = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
};

// ✅ 图片懒加载
const LazyImage: React.FC<{ src: string; alt: string }> = ({ src, alt }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    });

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <img
      ref={imgRef}
      src={isInView ? src : undefined}
      alt={alt}
      onLoad={() => setIsLoaded(true)}
      style={{ opacity: isLoaded ? 1 : 0 }}
    />
  );
};
```

## 常见问题

### 开发环境问题

#### 端口冲突

```bash
# 问题：端口 5173 已被占用
Error: Port 5173 is already in use

# 解决方案 1：使用不同端口
npm run dev -- --port 5174

# 解决方案 2：杀死占用端口的进程
lsof -ti:5173 | xargs kill -9
```

#### 依赖安装问题

```bash
# 问题：npm install 失败
# 解决方案：清理缓存重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### TypeScript 问题

#### 类型错误

```typescript
// 问题：Property 'xxx' does not exist on type 'yyy'
// 解决方案：正确定义类型
interface ApiResponse {
  data: any; // ❌ 避免使用 any
}

interface ApiResponse<T> {
  data: T; // ✅ 使用泛型
  status: number;
  message: string;
}
```

#### 模块导入问题

```typescript
// 问题：Cannot find module 'xxx'
// 解决方案：检查路径和文件扩展名
import { MyComponent } from './MyComponent'; // ✅ 正确
import { MyComponent } from './MyComponent.tsx'; // ❌ 不需要扩展名
```

### React 问题

#### 状态更新问题

```typescript
// 问题：状态更新不生效
const [count, setCount] = useState(0);

const handleClick = () => {
  setCount(count + 1); // ❌ 可能使用过期的状态
  setCount(count + 1); // 这里 count 仍然是 0
};

// 解决方案：使用函数式更新
const handleClick = () => {
  setCount(prev => prev + 1); // ✅ 使用最新状态
  setCount(prev => prev + 1); // 正确累加
};
```

#### 无限循环问题

```typescript
// 问题：useEffect 无限循环
useEffect(() => {
  fetchData();
}, [data]); // ❌ data 在每次渲染时都是新对象

// 解决方案：正确的依赖数组
useEffect(() => {
  fetchData();
}, [data.id]); // ✅ 使用稳定的值

// 或者使用 useMemo
const memoizedData = useMemo(() => data, [data.id]);
useEffect(() => {
  fetchData();
}, [memoizedData]);
```

### MQTT 连接问题

#### 连接失败

```typescript
// 问题：MQTT 连接失败
// 解决方案：检查配置和网络
const config = {
  brokerUrl: 'wss://ws.ohvfx.com:8084/mqtt', // 确保 URL 正确
  username: '1001', // 确保认证信息正确
  password: '1001',
  keepalive: 60,
  reconnectPeriod: 10000, // 增加重连间隔
  connectTimeout: 30000, // 增加连接超时
};
```

#### 内存泄漏

```typescript
// 问题：MQTT 消息导致内存泄漏
// 解决方案：正确清理订阅
useEffect(() => {
  const unsubscribe = mqttService.subscribe(topic, handler);
  
  return () => {
    unsubscribe(); // ✅ 清理订阅
  };
}, [topic]);
```

## 相关文档

- [系统架构](./ARCHITECTURE.md) - 系统整体架构设计
- [API 使用指南](./API_GUIDE.md) - API 接口使用说明
- [组件开发指南](./COMPONENTS_GUIDE.md) - UI 组件开发规范
- [MQTT 集成指南](./MQTT_GUIDE.md) - MQTT 实时通信集成
- [故障排除指南](./TROUBLESHOOTING.md) - 常见问题解决方案
- [性能优化指南](./PERFORMANCE_OPTIMIZATION.md) - 性能优化策略

## 更新历史

- v1.0 (2025-01-15): 初始版本，建立完整的开发指南