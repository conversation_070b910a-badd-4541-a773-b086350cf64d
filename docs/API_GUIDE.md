# API 使用指南

**版本：** v1.0
**最后更新：** 2025-01-21

## 变更历史
- **2025-01-21**: 数据库字段映射更新 - 选手信息相关字段从中文更新为英文（"选手ID" → "userId", "选手名称" → "userName"）
**维护者：** Nexus Panel 开发团队  
**类别：** api

## 概述

Nexus Panel 是一个为互动问答/竞赛活动构建的实时竞赛管理仪表板。本文档提供了完整的 API 使用指南，包括 HTTP REST API、MQTT WebSocket API 和内部服务 API 的详细信息，以及性能优化、故障排除和最佳实践。

**技术栈：** React + TypeScript + Vite + MQTT + NocoDB

## 目录

- [概述](#概述)
- [架构设计](#架构设计)
- [HTTP REST API](#http-rest-api)
- [MQTT WebSocket API](#mqtt-websocket-api)
- [内部服务 API](#内部服务-api)
- [认证和授权](#认证和授权)
- [性能优化](#性能优化)
- [错误处理](#错误处理)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)
- [代码示例](#代码示例)
- [测试指南](#测试指南)
- [相关文档](#相关文档)
- [更新历史](#更新历史)

## 架构设计

### 系统架构

Nexus Panel 采用**多层 API 架构**设计：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用层 (React)                        │
├─────────────────────────────────────────────────────────────┤
│                    Hook 抽象层                              │
│  useRaceApi | useApi | useMQTTIntegration | useNavigation   │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Services)                        │
│     HTTP Client    │    MQTT Service    │   Memory Manager  │
├─────────────────────────────────────────────────────────────┤
│                    协议层 (Protocols)                       │
│      HTTP REST     │    MQTT/WebSocket  │   Internal APIs   │
├─────────────────────────────────────────────────────────────┤
│                    外部服务层                               │
│      NocoDB API    │    MQTT Broker     │   File System     │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

- **分层解耦**: 每层职责明确，便于维护和扩展
- **协议分离**: HTTP 用于数据获取，MQTT 用于实时通信
- **统一抽象**: Hook 层提供统一的 API 调用接口
- **错误处理**: 完整的错误处理和重试机制
- **内存管理**: MQTT 消息的智能内存管理
- **单例模式**: Race API 采用单例架构，避免重复请求

### 数据流

```mermaid
graph TD
    A[前端应用] --> B[Hook 层]
    B --> C[服务层]
    C --> D[HTTP Client]
    C --> E[MQTT Service]
    C --> F[Memory Manager]

    D --> G[NocoDB API]
    E --> H[MQTT Broker]
    F --> I[浏览器存储]

    B --> J[useRaceApi]
    B --> K[useApi]
    B --> L[useMQTTIntegration]

    J --> M[赛事数据]
    K --> N[通用数据]
    L --> O[实时消息]
```

## HTTP REST API

### 基础配置

**基础域名**: `https://noco.ohvfx.com`  
**API 版本**: `v2`  
**认证方式**: `xc-token` Header 认证  
**通用 Token**: `bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp`

### 核心 API 端点

#### 1. 赛事管理 API

##### 获取赛事列表

**端点：** `GET /api/v2/tables/m19dww1xfzsfipk/records`  
**用途：** 获取所有可用赛事项目

**请求参数：**
```typescript
interface RaceListParams {
  viewId: string;     // 视图ID: "vwoimmnq6pws8pso"
  limit: number;      // 返回记录数限制: 500
  where?: string;     // 筛选条件 (可选)
}
```

**响应格式：**
```typescript
interface RaceApiResponse {
  list: RaceApiItem[];
  pageInfo: {
    totalRows: number;
    page: number;
    pageSize: number;
    isFirstPage: boolean;
    isLastPage: boolean;
  };
}

interface RaceApiItem {
  Id: number;
  "赛事 ID": string;
  "赛事名称": string;
  "是否显示": string;
  "参赛人数"?: number;
}
```

**使用示例：**
```typescript
import { getVisibleRaces } from '@/services/api';

const races = await getVisibleRaces();
console.log('可用赛事:', races);
```

##### 健康检查

**端点：** `GET /api/v2/tables/m19dww1xfzsfipk/records?limit=1`  
**用途：** 检查赛事服务可用性

#### 2. 导航数据 API

##### 获取表结构

**端点：** `GET /api/v2/meta/bases/{baseId}/tables`  
**用途：** 获取数据库表结构信息

**响应格式：**
```typescript
interface TableStructureResponse {
  list: TableStructureItem[];
}

interface TableStructureItem {
  id: string;
  title: string;
  type?: string;
  [key: string]: unknown;
}
```

##### 获取环节数据

**端点：** `GET /api/v2/tables/{tableId}/records`  
**用途：** 获取竞赛环节数据

**请求参数：**
```typescript
interface SectionDataParams {
  limit: number;      // 返回记录数限制: 500
  where?: string;     // 筛选条件 (可选)
  sort?: string;      // 排序规则 (可选)
}
```

**响应格式：**
```typescript
interface SectionDataResponse {
  list: SectionDataItem[];
  pageInfo: PaginationInfo;
}

interface SectionDataItem {
  Id: number;
  "环节名称": string;
  "显示顺序": number;
  "环节类型": string;
  "环节图标": string;
  "内容类型"?: string;
  [key: string]: unknown;
}
```

#### 3. 题目数据 API

##### 获取题目数据

**端点：** `GET /api/v2/tables/{tableId}/records`  
**用途：** 获取题目数据

**请求参数：**
```typescript
interface QuestionDataParams {
  limit: number;      // 返回记录数限制: 500
  where: string;      // 筛选条件: "(所属环节,eq,{环节名称})~and(题包编号,eq,{题包编号})"
  stage?: string;     // 阶段筛选: "~and(所属阶段,eq,{阶段名称})" (可选)
}
```

**响应格式：**
```typescript
interface QuestionApiResponse {
  list: QuestionApiItem[];
  pageInfo: PaginationInfo;
}

interface QuestionApiItem {
  Id: number;
  "题号"?: number | null;
  "题型"?: string | null;
  "分值"?: number | null;
  "题干"?: string | null;
  "选项"?: string | null;
  "答案"?: string | null;
  "解析"?: string | null;
  "所属环节": string;
  "题包编号": string;
  "附件"?: AttachmentItem[] | null;
  "所属阶段"?: string | null;
}
```

#### 4. 排名数据 API

##### 获取答题记录

**端点：** `GET /api/v2/tables/{tableId}/records`  
**用途：** 获取选手答题记录

**响应格式：**
```typescript
interface AnswerRecordApiResponse {
  list: AnswerRecordApiItem[];
  pageInfo: PaginationInfo;
}

interface AnswerRecordApiItem {
  Id: number;
  "选手答案": string;
  "选手正误": number;
  "所属环节": string;
  "提交时间": string;
  "关联题目": { Id: number; "题号": number; };
  "分值": number;
  "关联选手": { Id: number; "userId": string; };
}
```

##### 获取选手信息

**端点：** `GET /api/v2/tables/{tableId}/records`  
**用途：** 获取选手基本信息

**响应格式：**
```typescript
interface PlayerInfoApiResponse {
  list: PlayerInfoApiItem[];
  pageInfo: PaginationInfo;
}

interface PlayerInfoApiItem {
  Id: number;
  "userId": string;
  "userName": string;
  "关联得分": { Id: number; "userId": string; };
}
```

## MQTT WebSocket API

### 基础配置

**Broker URL**: `wss://ws.ohvfx.com:8084/mqtt`  
**客户端ID**: `Nexus-Panel`  
**认证信息**:
- Username: `1001`
- Password: `1001`

### Topic 结构规范

MQTT Topic 采用四层结构：

```
{domain}/{context}/{target}/{action}
```

#### 域 (Domain)
- `quiz`: 竞赛相关
- `display`: 显示控制
- `system`: 系统控制

#### 上下文 (Context)
- `session`: 环节管理
- `rank`: 排行榜
- `player`: 选手信息
- `rule`: 规则显示
- `client`: 客户端控制

#### 目标 (Target)
- `all`: 所有客户端
- `screen`: 显示屏
- `panel`: 控制面板
- 具体ID: 特定目标

#### 动作 (Action)
- `start`: 开始
- `stop`: 停止
- `show`: 显示
- `hide`: 隐藏
- `refresh`: 刷新
- `update`: 更新

### 常用 Topic 示例

#### 环节控制
```
quiz/session/all/start    # 开始环节
quiz/session/all/stop     # 停止环节
quiz/session/all/pause    # 暂停环节
quiz/session/all/resume   # 恢复环节
```

#### 显示控制
```
display/rank/screen/show     # 显示排行榜
display/rank/screen/hide     # 隐藏排行榜
display/player/screen/show   # 显示选手信息
display/player/screen/hide   # 隐藏选手信息
display/rule/screen/show     # 显示规则
display/rule/screen/hide     # 隐藏规则
```

#### 系统控制
```
system/client/all/refresh    # 刷新所有客户端
system/client/panel/update   # 更新控制面板
```

### MQTT 数据结构

#### SessionData
```typescript
interface SessionData {
  sessionType: string;
  sessionId: number;
  sessionName: string;
  config: Record<string, unknown>;
}
```

#### RankData
```typescript
interface RankData {
  rankType: 'general' | 'speedrun' | 'speedrun-plus';
}
```

#### PlayerData
```typescript
interface PlayerData {
  playerId: number;
  playerName: string;
  position: number;
  status: 'active' | 'inactive';
}
```

## 内部服务 API

### Hook 层 API

#### useRaceApi Hook

Race API 采用单例架构，确保整个应用只有一个实例，避免重复请求和数据不一致。

```typescript
// 使用共享的 RaceApi 实例
import { useSharedRaceApi } from '@/hooks/useRaceApi/singleton';

function MyComponent() {
  const {
    races,
    loading,
    error,
    navigationData,
    configurationData,
    fetchAllProjectData,
    // ... 其他方法
  } = useSharedRaceApi();

  // 使用 API
}
```

**接口定义：**
```typescript
interface UseRaceApiReturn {
  races: ProcessedRaceItem[];
  loading: boolean;
  error: ApiError | null;
  status: ApiStatus;
  execute: () => Promise<void>;
  retry: () => Promise<void>;
  // ... 更多方法
}
```

#### useApi Hook

通用 API Hook，用于处理各种 HTTP 请求：

```typescript
interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
  status: ApiStatus;
  execute: () => Promise<void>;
  reset: () => void;
  refetch: () => Promise<void>;
}
```

### 服务层 API

#### HTTP Client

```typescript
class HttpClient {
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>>;
  async post<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;
  async put<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;
  async delete<T>(endpoint: string): Promise<ApiResponse<T>>;
  async patch<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;
}
```

#### MQTT Service

```typescript
class MQTTService {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  publish(topic: string, message: any): Promise<void>;
  subscribe(topic: string, handler: MQTTMessageHandler): void;
  unsubscribe(topic: string): void;
}
```

## 认证和授权

### HTTP API 安全

- **认证方式**: Token-based 认证
- **Token 位置**: `xc-token` Header
- **Token 管理**: 硬编码 (开发环境)
- **HTTPS**: 强制使用 TLS 加密

### MQTT API 安全

- **认证方式**: 用户名密码认证
- **传输加密**: WSS (WebSocket Secure)
- **客户端ID**: 固定标识符
- **Topic 权限**: 基于用户权限控制

### 内部 API 安全

- **类型安全**: TypeScript 编译时检查
- **运行时验证**: 数据格式验证
- **错误边界**: 异常捕获和处理

## 性能优化

### 重复请求问题解决

系统实现了完整的四层防护机制来解决 API 重复请求问题：

#### 1. 单例模式
在最顶层确保只有一个 RaceApi 实例：

```typescript
// 应用根部配置 RaceApiProvider
function App() {
  return (
    <RaceApiProvider>
      <AppContent />
    </RaceApiProvider>
  );
}
```

#### 2. 全局请求去重器
在最底层防止重复的 HTTP 请求：

```typescript
// 自动去重相同的 API 请求
const result = await GlobalRequestDeduplicator.execute(
  `table_structure_${baseId}`,
  () => httpClient.get(`/api/v2/meta/bases/${baseId}/tables`)
);
```

#### 3. 全局缓存管理
在中间层提供数据缓存和复用：

```typescript
// 表结构数据全局缓存
const tableStructure = await GlobalTableStructureCache.getTableStructure(baseId);
```

#### 4. Hook 层优化
在最上层提供智能的数据复用策略。

### 缓存策略

#### HTTP API 缓存
- **表结构缓存**: 缓存不经常变化的表结构数据
- **请求合并**: 合并多个相关请求
- **数据完整性保障**: 所有 NocoDB API 调用添加 `limit=500` 参数，避免默认分页导致的数据截断
- **条件请求**: 使用 ETag 和 Last-Modified

#### MQTT 缓存
- **消息去重**: 避免重复消息处理
- **内存管理**: 智能清理历史消息
- **连接复用**: 单一连接多 Topic 订阅
- **QoS 控制**: 根据重要性设置服务质量

### 性能监控

#### 缓存性能统计
```typescript
interface CachePerformanceStats {
  cacheSize: number;
  totalRequests: number;
  hits: number;
  misses: number;
  hitRate: number;
  efficiency: 'good' | 'fair' | 'poor';
}
```

#### 性能提升效果

| 指标 | 修复前 | 修复后 | 改善幅度 |
|------|--------|--------|----------|
| useRaceApi 实例数 | 3-4个 | 1个 | **75%** ↓ |
| 表结构请求次数 | 4次 | 1次 | **75%** ↓ |
| 学生表请求次数 | 2次 | 1次 | **50%** ↓ |
| 答题记录请求次数 | 2次 | 1次 | **50%** ↓ |
| 数据一致性 | 不一致 | 完全一致 | **100%** ↑ |

## 错误处理

### HTTP API 错误处理

#### 常见状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

#### 错误响应格式
```typescript
interface ApiError {
  message: string;
  code?: string | number;
  status?: number;
  originalError?: Error;
}
```

#### 错误处理策略
```typescript
try {
  const data = await apiCall();
  return data;
} catch (error) {
  const apiError = ApiErrorHandler.createApiError(error);
  
  // 根据错误类型进行处理
  switch (apiError.status) {
    case 401:
      // 处理认证错误
      break;
    case 500:
      // 处理服务器错误
      break;
    default:
      // 处理其他错误
      break;
  }
  
  throw apiError;
}
```

### MQTT 连接状态

#### 连接状态枚举
```typescript
enum MQTTConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}
```

#### 错误处理策略
- **自动重连**: 连接断开时自动尝试重连
- **指数退避**: 重连间隔逐渐增加
- **最大重试**: 限制最大重连次数
- **错误回调**: 提供错误处理回调函数

## 故障排除

### 常见问题 1：API 重复请求

**问题描述：** 进入争分夺秒环节时出现多次相同的 API 请求

**解决方案：**
1. 检查是否使用了 `useSharedRaceApi` 而不是 `useRaceApi`
2. 确认 `RaceApiProvider` 在应用根部正确配置
3. 查看控制台日志确认请求去重器工作状态

**调试命令：**
```javascript
// 在浏览器控制台运行
import("./src/services/api/tableStructureCache.ts").then((module) => {
  console.log("缓存统计:", module.GlobalTableStructureCache.getCacheStats());
});
```

### 常见问题 2：MQTT 连接失败

**问题描述：** MQTT 连接无法建立或频繁断开

**解决方案：**
1. 检查网络连接和防火墙设置
2. 验证认证信息是否正确
3. 检查 WebSocket 支持情况
4. 查看 MQTT 服务器状态

### 常见问题 3：数据格式错误

**问题描述：** API 返回的数据格式与预期不符

**解决方案：**
1. 检查 API 响应格式是否变更
2. 验证数据处理函数是否正确
3. 确认类型定义是否最新
4. 查看网络请求的实际响应

### 常见问题 4：缓存数据过期

**问题描述：** 显示的数据不是最新的

**解决方案：**
1. 手动清理缓存：
```javascript
import("./src/services/api/tableStructureCache.ts").then((module) => {
  module.GlobalTableStructureCache.clearCache();
});
```
2. 检查缓存过期策略
3. 确认数据更新机制

## 最佳实践

### 1. API 调用最佳实践

- **使用单例模式**: 优先使用 `useSharedRaceApi` 而不是 `useRaceApi`
- **错误处理**: 始终包含完整的错误处理逻辑
- **类型安全**: 使用 TypeScript 类型定义确保类型安全
- **缓存利用**: 合理利用缓存机制，避免不必要的请求

### 2. MQTT 使用最佳实践

- **连接管理**: 使用单一连接，避免多个连接实例
- **消息处理**: 实现消息去重和错误处理
- **内存管理**: 定期清理历史消息，避免内存泄漏
- **重连策略**: 实现智能重连机制

### 3. 性能优化最佳实践

- **请求合并**: 合并相关的 API 请求
- **分页处理**: 大数据集使用分页加载
- **缓存策略**: 合理设置缓存过期时间
- **监控指标**: 定期检查性能指标

### 4. 开发调试最佳实践

- **日志记录**: 使用结构化日志记录
- **错误监控**: 集成错误监控服务
- **性能分析**: 定期进行性能分析
- **测试覆盖**: 确保充分的测试覆盖

## 代码示例

### 完整集成示例

```typescript
import React, { useEffect } from 'react';
import { RaceApiProvider, useSharedRaceApi } from '@/hooks/useRaceApi/singleton';
import { useMQTTIntegration } from '@/hooks/useMQTTIntegration';

// 应用内容组件
function AppContent() {
  const raceApi = useSharedRaceApi();
  const mqtt = useMQTTIntegration();

  useEffect(() => {
    // 初始化数据加载
    if (raceApi.races.length === 0) {
      raceApi.execute();
    }
  }, [raceApi]);

  useEffect(() => {
    // MQTT 连接
    mqtt.connect();
    
    // 订阅相关 Topic
    mqtt.subscribe('quiz/session/all/start', (message) => {
      console.log('环节开始:', message);
    });

    return () => {
      mqtt.disconnect();
    };
  }, [mqtt]);

  if (raceApi.loading) {
    return <div>加载中...</div>;
  }

  if (raceApi.error) {
    return <div>错误: {raceApi.error.message}</div>;
  }

  return (
    <div>
      <h1>赛事列表</h1>
      {raceApi.races.map(race => (
        <div key={race.id}>
          <h2>{race.name}</h2>
          <p>参赛人数: {race.peopleCount}</p>
        </div>
      ))}
    </div>
  );
}

// 主应用组件
function App() {
  return (
    <RaceApiProvider>
      <AppContent />
    </RaceApiProvider>
  );
}

export default App;
```

### 实际项目中的 useRaceApi 使用示例

```typescript
import React, { useCallback, useEffect } from 'react';
import { useSharedRaceApi } from '@/hooks/useRaceApi/singleton';

function RaceManagementComponent() {
  const {
    races,
    loading,
    error,
    navigationData,
    configurationData,
    questionData,
    rankingData,
    fetchAllProjectData,
    fetchQuestionData,
    fetchSectionRankingData,
    startSectionRankingPolling,
    stopSectionRankingPolling
  } = useSharedRaceApi();

  // 选择项目并加载所有相关数据
  const handleProjectSelect = useCallback(async (projectId: string) => {
    try {
      await fetchAllProjectData(projectId);
      console.log('项目数据加载完成');
    } catch (error) {
      console.error('项目数据加载失败:', error);
    }
  }, [fetchAllProjectData]);

  // 加载特定环节的题目数据
  const handleLoadQuestions = useCallback(async (
    tableId: string, 
    section: string, 
    packageNum: string
  ) => {
    try {
      await fetchQuestionData(tableId, section, packageNum);
      console.log('题目数据加载完成');
    } catch (error) {
      console.error('题目数据加载失败:', error);
    }
  }, [fetchQuestionData]);

  // 开始排名数据轮询
  const handleStartRanking = useCallback((projectId: string, sectionName: string) => {
    startSectionRankingPolling(projectId, sectionName, {
      interval: 5000, // 5秒轮询一次
      onUpdate: (data) => {
        console.log('排名数据更新:', data);
      },
      onError: (error) => {
        console.error('排名数据获取失败:', error);
      }
    });
  }, [startSectionRankingPolling]);

  // 组件卸载时停止轮询
  useEffect(() => {
    return () => {
      stopSectionRankingPolling();
    };
  }, [stopSectionRankingPolling]);

  return (
    <div>
      <h2>赛事管理</h2>
      
      {/* 赛事选择 */}
      <div>
        <h3>选择赛事</h3>
        {races.map(race => (
          <button 
            key={race.id}
            onClick={() => handleProjectSelect(race.id)}
            disabled={loading}
          >
            {race.name}
          </button>
        ))}
      </div>

      {/* 导航数据显示 */}
      {navigationData.length > 0 && (
        <div>
          <h3>环节导航</h3>
          {navigationData.map(node => (
            <div key={node.id}>
              <span>{node.name}</span>
              <button onClick={() => handleLoadQuestions(
                node.tableId || '', 
                node.name, 
                '1'
              )}>
                加载题目
              </button>
              <button onClick={() => handleStartRanking(
                races[0]?.id || '', 
                node.name
              )}>
                开始排名
              </button>
            </div>
          ))}
        </div>
      )}

      {/* 题目数据显示 */}
      {questionData.length > 0 && (
        <div>
          <h3>题目列表 ({questionData.length} 题)</h3>
          {questionData.slice(0, 3).map(question => (
            <div key={question.id}>
              <p><strong>题目 {question.questionNumber}:</strong> {question.stem}</p>
              <p><strong>答案:</strong> {question.answer}</p>
            </div>
          ))}
        </div>
      )}

      {/* 排名数据显示 */}
      {rankingData && (
        <div>
          <h3>实时排名</h3>
          <p>参赛选手: {rankingData.totalPlayers} 人</p>
          <p>最后更新: {new Date(rankingData.lastUpdated).toLocaleTimeString()}</p>
          {rankingData.players.slice(0, 5).map((player, index) => (
            <div key={player.playerId}>
              <span>{index + 1}. {player.playerName} - {player.totalScore} 分</span>
            </div>
          ))}
        </div>
      )}

      {/* 加载状态 */}
      {loading && <div>加载中...</div>}
      
      {/* 错误显示 */}
      {error && <div style={{color: 'red'}}>错误: {error.message}</div>}
    </div>
  );
}

export default RaceManagementComponent;
```

### HTTP API 使用示例

```typescript
import { 
  getVisibleRaces, 
  getNavigationData, 
  getQuestionData,
  getSectionRankingData 
} from '@/services/api';

// 获取赛事列表
async function loadRaces() {
  try {
    const races = await getVisibleRaces();
    console.log('赛事列表:', races);
    return races;
  } catch (error) {
    console.error('获取赛事失败:', error);
    throw error;
  }
}

// 获取导航数据
async function loadNavigation(baseId: string) {
  try {
    const navigation = await getNavigationData(baseId);
    console.log('导航数据:', navigation);
    return navigation;
  } catch (error) {
    console.error('获取导航失败:', error);
    throw error;
  }
}

// 获取题目数据
async function loadQuestions(tableId: string, section: string, packageNum: string) {
  try {
    const questions = await getQuestionData(tableId, section, packageNum);
    console.log('题目数据:', questions);
    return questions;
  } catch (error) {
    console.error('获取题目失败:', error);
    throw error;
  }
}

// 获取排名数据
async function loadRanking(baseId: string, sectionName: string) {
  try {
    const ranking = await getSectionRankingData(baseId, sectionName);
    console.log('排名数据:', ranking);
    return ranking;
  } catch (error) {
    console.error('获取排名失败:', error);
    throw error;
  }
}
```

### MQTT 使用示例

```typescript
import { MQTTService } from '@/services/mqtt';

// 创建 MQTT 服务实例
const mqttService = new MQTTService({
  brokerUrl: 'wss://ws.ohvfx.com:8084/mqtt',
  clientId: 'Nexus-Panel',
  username: '1001',
  password: '1001'
});

// 连接和订阅
async function setupMQTT() {
  try {
    // 连接到 MQTT Broker
    await mqttService.connect();
    console.log('MQTT 连接成功');

    // 订阅环节控制消息
    mqttService.subscribe('quiz/session/+/+', (topic, message) => {
      console.log(`收到环节控制消息 [${topic}]:`, message);
      
      // 根据消息类型处理
      if (topic.endsWith('/start')) {
        handleSessionStart(message);
      } else if (topic.endsWith('/stop')) {
        handleSessionStop(message);
      }
    });

    // 订阅显示控制消息
    mqttService.subscribe('display/+/+/+', (topic, message) => {
      console.log(`收到显示控制消息 [${topic}]:`, message);
      handleDisplayControl(topic, message);
    });

  } catch (error) {
    console.error('MQTT 设置失败:', error);
  }
}

// 发布消息
async function publishMessage(topic: string, message: any) {
  try {
    await mqttService.publish(topic, message);
    console.log(`消息已发布到 ${topic}:`, message);
  } catch (error) {
    console.error('发布消息失败:', error);
  }
}

// 处理环节开始
function handleSessionStart(message: any) {
  console.log('环节开始:', message);
  // 实现环节开始逻辑
}

// 处理环节停止
function handleSessionStop(message: any) {
  console.log('环节停止:', message);
  // 实现环节停止逻辑
}

// 处理显示控制
function handleDisplayControl(topic: string, message: any) {
  console.log('显示控制:', topic, message);
  // 实现显示控制逻辑
}
```

## 测试指南

### API 重复请求测试

#### 测试步骤

1. **准备测试环境**
   - 打开浏览器开发者工具
   - 切换到 Network（网络）面板
   - 清空现有的网络请求记录
   - 在过滤器中输入 `bases` 来只显示表结构 API 请求

2. **执行测试场景**

   **场景 1：进入争分夺秒环节**
   - 选择一个项目
   - 导航到争分夺秒环节
   - 观察网络面板中的请求

   **预期结果：**
   - 只应该看到 **1 次** 表结构请求
   - 只应该看到 **1 次** 选手信息表请求

   **场景 2：进入同分加赛环节**
   - 从争分夺秒切换到同分加赛环节
   - 观察网络面板中的新请求

   **预期结果：**
   - 应该 **没有新的** 表结构请求（使用缓存）
   - 如果选手数据已加载，应该 **没有新的** 选手信息表请求

3. **查看调试日志**

   在浏览器控制台中，你应该看到以下类型的日志：

   **正常情况下的日志：**
   ```
   [GlobalRequestDeduplicator] 🔍 请求去重检查
   [GlobalTableStructureCache] 🔍 请求表结构数据 (请求#1)
   [NavigationApiService] 🚀 发起表结构API请求
   [GlobalTableStructureCache] ✅ 数据已存储到全局缓存 (请求#1)
   ```

   **缓存命中时的日志：**
   ```
   [GlobalTableStructureCache] 🔍 请求表结构数据 (请求#2)
   [GlobalTableStructureCache] ✅ 全局缓存命中 (请求#2)
   ```

### 高级调试

#### 获取缓存统计信息

```javascript
// 获取全局缓存统计
import("./src/services/api/tableStructureCache.ts").then((module) => {
  console.log("缓存统计:", module.GlobalTableStructureCache.getCacheStats());
});

// 获取请求去重统计
import("./src/services/api/requestDeduplicator.ts").then((module) => {
  console.log("去重统计:", module.GlobalRequestDeduplicator.getStats());
});
```

#### 手动清理缓存

```javascript
// 清理所有缓存
import("./src/services/api/tableStructureCache.ts").then((module) => {
  module.GlobalTableStructureCache.clearCache();
});

// 清理请求去重器
import("./src/services/api/requestDeduplicator.ts").then((module) => {
  module.GlobalRequestDeduplicator.clearAll();
});
```

### 成功标准

✅ **完全成功**：所有环节切换只有 1 次表结构请求  
✅ **基本成功**：表结构请求减少到 2 次以内  
❌ **需要进一步优化**：仍有 3 次或以上重复请求

## 相关文档

- [系统架构文档](./ARCHITECTURE.md)
- [组件开发指南](./COMPONENTS_GUIDE.md)
- [MQTT 集成指南](./MQTT_GUIDE.md)
- [故障排除指南](./TROUBLESHOOTING.md)
- [性能优化指南](./PERFORMANCE_OPTIMIZATION.md)

## 更新历史

- v1.0 (2025-01-15): 初始版本，整合所有 API 相关文档
  - 合并 API 重复请求分析报告
  - 合并 API 端点全面分析
  - 合并 API 请求测试指南
  - 合并 Race API 单例架构文档
  - 合并环节数据重复请求分析
  - 添加完整的使用示例和最佳实践
  - 添加性能优化和故障排除指南