{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Documentation Metadata Schema", "description": "Schema for documentation metadata in Nexus Panel project", "type": "object", "properties": {"title": {"type": "string", "description": "Document title"}, "version": {"type": "string", "pattern": "^v\\d+\\.\\d+(\\.\\d+)?$", "description": "Document version (e.g., v1.0, v1.2.3)"}, "lastUpdated": {"type": "string", "format": "date", "description": "Last update date in YYYY-MM-DD format"}, "maintainer": {"type": "string", "description": "Document maintainer name or team"}, "category": {"type": "string", "enum": ["api", "components", "mqtt", "architecture", "troubleshooting", "performance", "development"], "description": "Document category"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Document tags for categorization"}, "relatedDocs": {"type": "array", "items": {"type": "string"}, "description": "List of related document filenames"}, "codeReferences": {"type": "array", "items": {"type": "string"}, "description": "List of referenced code files or functions"}, "status": {"type": "string", "enum": ["draft", "review", "approved", "deprecated"], "description": "Document status"}}, "required": ["title", "version", "lastUpdated", "maintainer", "category"]}