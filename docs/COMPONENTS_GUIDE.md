# 组件开发指南

**版本：** v1.0  
**最后更新：** 2025-01-15  
**维护者：** Nexus Panel 开发团队  
**类别：** components

## 概述

Nexus Panel 采用基于 React + TypeScript + Adobe React Spectrum 的现代化组件架构，为互动问答/竞赛活动提供丰富的 UI 组件库。本指南涵盖了项目中所有核心组件的使用方法、API 文档、样式定制和开发最佳实践。

## 目录

- [概述](#概述)
- [组件架构](#组件架构)
- [核心组件](#核心组件)
  - [音频播放器 (AudioPlayer)](#音频播放器-audioplayer)
  - [导航树视图 (NavigationTreeView)](#导航树视图-navigationtreeview)
  - [侧边栏按钮组 (SidebarButtonGroup)](#侧边栏按钮组-sidebarbuttongroup)
  - [动态组件渲染器 (DynamicComponentRenderer)](#动态组件渲染器-dynamiccomponentrenderer)
  - [智能图片加载 (ImageWithRetry)](#智能图片加载-imagewithretry)
  - [内容切换器 (ContentToggle)](#内容切换器-contenttoggle)
- [布局组件](#布局组件)
- [业务组件](#业务组件)
- [样式系统](#样式系统)
- [开发规范](#开发规范)
- [测试指南](#测试指南)
- [性能优化](#性能优化)
- [故障排除](#故障排除)
- [相关文档](#相关文档)
- [更新历史](#更新历史)

## 组件架构

### 设计原则

1. **数据-UI 分离**：使用自定义 hooks 进行数据管理和业务逻辑，纯展示组件接收 props
2. **配置驱动**：基于配置的动态组件渲染，集中配置文件位于 `src/config/`
3. **Adobe Spectrum 优先**：优先使用 Adobe React Spectrum 组件，确保设计一致性
4. **TypeScript 严格模式**：完整类型安全，严格编译器选项
5. **组件共置**：相关组件按功能分组，样式文件与组件文件共置

### 目录结构

```
src/components/
├── common/                     # 通用工具组件
│   ├── DynamicComponentRenderer.tsx
│   ├── ImageWithRetry.tsx
│   └── index.ts
├── layout/                     # 布局组件
│   ├── HeaderSection.tsx
│   ├── ContentArea.tsx
│   ├── NavigationSidebar.tsx
│   └── FooterSection.tsx
├── question/                   # 题目相关组件
│   ├── QuestionHeader.tsx
│   ├── QuestionBody.tsx
│   ├── QuestionAnswer.tsx
│   └── QuestionExplanation.tsx
├── ContentToggle/              # 功能特定组件组
│   ├── index.tsx
│   └── ContentToggle.css
├── AudioPlayer.tsx             # 独立组件
├── AudioPlayer.css
├── NavigationTreeView.tsx
├── NavigationTreeView.css
├── SidebarButtonGroup.tsx
├── SidebarButtonGroup.css
├── SidebarButtonStyles.ts
└── [其他组件...]
```

### 命名约定

- **组件文件**：PascalCase（例如：`TimeRaceTimer.tsx`）
- **Hooks**：camelCase 带 `use` 前缀（例如：`useRaceApi.ts`）
- **样式文件**：匹配组件名称（例如：`TimeRaceTimer.css`）
- **类型定义**：接口使用 PascalCase，文件使用 camelCase
- **CSS 类名**：kebab-case（例如：`audio-player-controls`）

## 组件示例

本节提供了 Nexus Panel 中主要组件的实际使用示例，帮助开发者快速上手。

### 🎵 音频播放器使用示例

```tsx
import { AudioPlayer } from '../components/AudioPlayer';

// 基本使用
<AudioPlayer 
  src="/audio/question-1.mp3" 
  title="题目配音"
/>

// 带回调的高级使用
<AudioPlayer 
  src="/audio/question-1.mp3"
  title="题目配音"
  onPlay={() => console.log('开始播放')}
  onPause={() => console.log('暂停播放')}
  onEnded={() => console.log('播放结束')}
/>
```

### 🌳 导航树视图使用示例

```tsx
import { NavigationTreeView } from '../components/NavigationTreeView';

const navigationData = {
  sections: [
    {
      id: 'home',
      title: '首页',
      items: [
        { id: 'dashboard', title: '仪表板', path: '/dashboard' }
      ]
    }
  ]
};

<NavigationTreeView 
  data={navigationData}
  onItemSelect={(item) => console.log('选中:', item)}
/>
```

### 🔘 侧边栏按钮组使用示例

```tsx
import { SidebarButtonGroup } from '../components/SidebarButtonGroup';

const buttonConfig = {
  buttons: [
    { id: 'home', label: '首页', icon: 'Home' },
    { id: 'settings', label: '设置', icon: 'Settings' }
  ]
};

<SidebarButtonGroup 
  config={buttonConfig}
  activeButton="home"
  onButtonClick={(id) => console.log('点击按钮:', id)}
/>
```

## 核心组件

### 音频播放器 (AudioPlayer)

专为题目配音播放而设计的高质量音频播放组件，基于 HTML5 Audio API 和 Adobe React Spectrum 设计规范开发。

#### 功能特性

- **播放控制**：播放/暂停、重播、音量控制
- **时间管理**：实时时间显示、可拖拽进度条
- **多音频源支持**：本地文件、HTTPS URL、Data URL
- **响应式设计**：适配不同屏幕尺寸和深色主题
- **无障碍访问**：支持屏幕阅读器和键盘导航

#### Props 接口

```typescript
interface AudioPlayerProps {
  /** 音频源路径 - 支持本地文件路径或外部 HTTPS URL */
  audioSrc: string;
  /** 播放事件回调 */
  onPlay?: () => void;
  /** 暂停事件回调 */
  onPause?: () => void;
  /** 播放结束回调 */
  onEnded?: () => void;
  /** 时间更新回调 */
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  /** 是否禁用 */
  isDisabled?: boolean;
  /** 是否加载中 */
  isLoading?: boolean;
  /** 自定义CSS类名 */
  className?: string;
  /** React Spectrum 样式 */
  UNSAFE_style?: React.CSSProperties;
}
```

#### 使用示例

```tsx
import { AudioPlayer } from './components/AudioPlayer';

function QuestionPage() {
  const handleTimeUpdate = (currentTime: number, duration: number) => {
    console.log(`播放进度: ${currentTime.toFixed(1)}s / ${duration.toFixed(1)}s`);
  };

  return (
    <div>
      <h2>题目配音</h2>
      <AudioPlayer
        audioSrc="/audio/question-1.mp3"
        onPlay={() => console.log('题目配音开始播放')}
        onPause={() => console.log('题目配音暂停')}
        onEnded={() => console.log('题目配音播放完毕')}
        onTimeUpdate={handleTimeUpdate}
        isDisabled={false}
        isLoading={false}
        className="custom-audio-player"
      />
    </div>
  );
}
```

#### 样式定制

```css
.audio-player                    /* 主容器 */
.audio-player-controls          /* 控件容器 */
.audio-player-play-pause        /* 播放/暂停按钮 */
.audio-player-replay            /* 重播按钮 */
.audio-player-time              /* 时间显示 */
.audio-player-progress-container /* 进度条容器 */
.audio-player-progress-bar      /* 进度条 */
.audio-player-progress-fill     /* 进度条填充 */
.audio-player-volume            /* 音量按钮 */
```

#### 支持的音频格式

- **MP3** - 推荐格式，兼容性最佳
- **WAV** - 高质量，文件较大
- **OGG** - 开源格式，部分浏览器支持
- **AAC** - 高压缩比，移动端友好

### 导航树视图 (NavigationTreeView)

赛事导航树形菜单组件，支持动态数据加载和多级导航结构。

#### 功能特性

- **动态数据支持**：支持从 API 加载导航数据
- **多级展开**：父节点整个区域可点击展开/折叠
- **选择管理**：父节点不可选中，子节点可选中
- **图标映射**：支持多种图标类型的自动映射
- **响应式设计**：适配不同屏幕尺寸

#### Props 接口

```typescript
interface NavigationTreeViewProps {
  /** 选择变化回调函数 */
  onSelectionChange?: (selectedKey: string | null) => void;
  /** 可选的自定义样式类 */
  className?: string;
  /** 可选的高度设置，默认为 "100%" */
  height?: string;
  /** 可选的最大宽度设置，默认为 "100%" */
  maxWidth?: string;
  /** 可选的 ARIA 标签，默认为 "导航菜单" */
  ariaLabel?: string;
  /** 动态导航数据 */
  navigationData?: NavigationNode[] | null;
  /** 导航数据加载状态 */
  loading?: boolean;
  /** 配置数据是否就绪 */
  configurationDataReady?: boolean;
}
```

#### 使用示例

```tsx
import { NavigationTreeView } from './components/NavigationTreeView';

function Sidebar() {
  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const [navigationData, setNavigationData] = useState(null);

  return (
    <NavigationTreeView
      navigationData={navigationData}
      loading={false}
      configurationDataReady={true}
      onSelectionChange={setSelectedKey}
      height="100%"
      ariaLabel="主导航菜单"
    />
  );
}
```

#### 图标映射系统

```typescript
const iconMapping = {
  "相册": DocumentFragmentGroup,
  "图像": DocumentFragment,
  "文件夹": Folder,
  "文件": FileTxt,
  "列表": ViewList,
  "首页": Home,
};
```

### 侧边栏按钮组 (SidebarButtonGroup)

统一的按钮样式系统，提供 7 种预定义样式类型，确保界面一致性。

#### 样式类型

| 样式类型 | 用途 | 视觉特征 | 推荐场景 |
|---------|------|----------|----------|
| `PRIMARY_ACTION` | 主要操作 | 🔵 Accent + 阴影 | 跳转、确认、提交 |
| `NAVIGATION` | 导航操作 | ➡️ 渐变 + 位移动画 | 上一题、下一题、翻页 |
| `CONTENT_DISPLAY` | 内容展示 | 📄 简洁边框 | 正确答案、答案解析 |
| `DATA_OPERATION` | 数据操作 | 🟢 绿色 + 缩放动画 | 赋分、统计、导出 |
| `SPECIAL_FUNCTION` | 特殊功能 | 🟣 紫色 + 光泽动画 | 填空放大、全屏 |
| `DANGER_ACTION` | 危险操作 | 🔴 红色 + 警告效果 | 删除、重置、清空 |
| `INFO_DISPLAY` | 信息展示 | ⚪ 虚线边框 + 禁用 | 状态显示、信息提示 |

#### 使用示例

```tsx
import { SidebarButtonGroup, ButtonConfig } from './components/SidebarButtonGroup';
import { ButtonStyleType } from './components/SidebarButtonStyles';

const buttons: ButtonConfig[] = [
  {
    text: '跳转',
    styleType: ButtonStyleType.PRIMARY_ACTION,
    onPress: () => console.log('跳转')
  },
  {
    text: '上一题',
    styleType: ButtonStyleType.NAVIGATION,
    onPress: () => console.log('上一题')
  }
];

<SidebarButtonGroup
  title="题目切换"
  buttons={buttons}
/>
```

#### 自动样式推荐

```typescript
import { suggestButtonStyleType } from './components/SidebarButtonStyles';

const buttonTexts = ['上一题', '跳转', '正确答案', '赋分'];
const autoButtons: ButtonConfig[] = buttonTexts.map(text => ({
  text,
  styleType: suggestButtonStyleType(text),
  onPress: () => console.log(text)
}));
```

### 动态组件渲染器 (DynamicComponentRenderer)

统一管理动态组件渲染逻辑的核心组件，负责组件配置计算、按钮组配置获取和面包屑路径生成。

#### 功能特性

- **配置驱动渲染**：基于导航选择动态渲染组件
- **性能优化**：组件配置缓存和渲染策略优化
- **面包屑生成**：自动生成导航面包屑路径
- **事件处理统一**：集中管理组件事件处理器

#### Props 接口

```typescript
interface DynamicComponentRendererProps {
  /** 当前选中的导航键 */
  selectedNavigationKey: string | null;
  /** 动态导航数据 */
  navigationData: NavigationNode[] | null;
  /** 日志记录回调函数（可选） */
  onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
  /** 事件处理器（可选） */
  handlers?: {
    onPackageChange?: (packageId: string, packageName: string) => void;
    onQuestionJump?: (formState: Record<string, string | number>) => void;
    // ... 其他事件处理器
  };
  /** 渲染函数 - 接收计算后的配置数据 */
  children: (config: {
    componentConfig: ComponentConfig;
    buttonGroups: ButtonGroupConfig[];
    breadcrumbItems: Array<{ id: string; label: string }>;
  }) => React.ReactNode;
}
```

#### 使用示例

```tsx
import { DynamicComponentRenderer } from './components/common/DynamicComponentRenderer';

function MainContent() {
  return (
    <DynamicComponentRenderer
      selectedNavigationKey={selectedKey}
      navigationData={navigationData}
      onLog={handleLog}
      handlers={{
        onQuestionJump: handleQuestionJump,
        onPackageChange: handlePackageChange,
      }}
    >
      {({ componentConfig, buttonGroups, breadcrumbItems }) => (
        <div>
          {/* 渲染面包屑 */}
          <Breadcrumbs items={breadcrumbItems} />
          
          {/* 渲染主要内容 */}
          <ComponentRenderer config={componentConfig} />
          
          {/* 渲染侧边栏按钮 */}
          <SidebarButtons groups={buttonGroups} />
        </div>
      )}
    </DynamicComponentRenderer>
  );
}
```

### 智能图片加载 (ImageWithRetry)

支持重试机制的智能图片加载组件，提供加载状态管理和错误处理。

#### 功能特性

- **自动重试机制**：指数退避重试策略
- **增强型加载检测**：多重验证确保图片真正加载完成
- **渐进式显示**：opacity 过渡和缩放动画
- **状态轮询机制**：后备方案确保加载状态准确
- **内存泄漏防护**：完善的清理机制

#### Props 接口

```typescript
interface ImageWithRetryProps {
  /** 图片URL */
  src: string;
  /** 替代文本 */
  alt?: string;
  /** 自定义CSS类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 最大重试次数，默认为2 */
  maxRetries?: number;
  /** 重试间隔（毫秒），默认为[1000, 2000] */
  retryDelays?: number[];
  /** 加载成功回调 */
  onLoad?: () => void;
  /** 加载失败回调 */
  onError?: (error: string) => void;
  /** 图片宽度 */
  width?: string | number;
  /** 图片高度 */
  height?: string | number;
  /** 是否显示加载状态 */
  showLoadingState?: boolean;
  /** 是否显示错误图标 */
  showErrorIcon?: boolean;
}
```

#### 使用示例

```tsx
import { ImageWithRetry } from './components/common/ImageWithRetry';

function ImageGallery() {
  return (
    <ImageWithRetry
      src="/images/question-diagram.png"
      alt="题目示意图"
      width="100%"
      height="300px"
      maxRetries={3}
      retryDelays={[1000, 2000, 4000]}
      onLoad={() => console.log('图片加载成功')}
      onError={(error) => console.error('图片加载失败:', error)}
      showLoadingState={true}
      showErrorIcon={true}
    />
  );
}
```

### 内容切换器 (ContentToggle)

提供全局指令和定向指令的切换功能，用于 MQTT 消息发送的目标控制。

#### 功能特性

- **双模式切换**：全局指令和定向指令
- **MQTT 集成**：与 MQTT 服务集成，控制消息目标
- **响应式设计**：适配不同屏幕尺寸

#### Props 接口

```typescript
interface ContentToggleProps {
  selectedType: string;
  onChange: (type: string) => void;
}
```

#### 使用示例

```tsx
import { ContentToggle } from './components/ContentToggle';

function ControlPanel() {
  const [selectedType, setSelectedType] = useState('global');

  return (
    <ContentToggle
      selectedType={selectedType}
      onChange={setSelectedType}
    />
  );
}
```

## 布局组件

### 布局组件系统

项目采用模块化布局组件系统，位于 `src/components/layout/` 目录：

- **HeaderSection**：页面头部组件
- **ContentArea**：主内容区域组件
- **NavigationSidebar**：导航侧边栏组件
- **FooterSection**：页面底部组件

#### 使用示例

```tsx
import { 
  HeaderSection, 
  ContentArea, 
  NavigationSidebar, 
  FooterSection 
} from './components/layout';

function AppLayout() {
  return (
    <div className="app-layout">
      <HeaderSection />
      <div className="main-content">
        <NavigationSidebar />
        <ContentArea />
      </div>
      <FooterSection />
    </div>
  );
}
```

## 业务组件

### 题目相关组件

位于 `src/components/question/` 目录，提供题目显示的完整组件套件：

- **QuestionHeader**：题目头部信息
- **QuestionBody**：题目主体内容
- **QuestionAnswer**：答案选项组件
- **QuestionExplanation**：答案解析组件

### 内容组件

- **HomePageContent / HomePageSkeleton**：首页内容和骨架屏
- **QuestionContent / QuestionSkeleton**：题目内容和骨架屏
- **RankingContent / RankingSkeleton**：排名内容和骨架屏
- **UltimatePKContent / UltimatePKSkeleton**：终极PK内容和骨架屏

### 功能组件

- **TimeRaceRankingContainer**：争分夺秒排名容器
- **TimeRaceTimerDisplay**：计时器显示组件
- **RankingProgressIndicator**：排名进度指示器
- **DeviceStatus**：设备状态显示

## 样式系统

### CSS 架构

1. **全局样式**：`src/index.css` - 全局样式和 CSS 变量
2. **组件样式**：每个组件对应的 `.css` 文件
3. **Adobe Spectrum**：基础设计系统样式
4. **自定义样式**：项目特定的样式扩展

### CSS 类名规范

```css
/* 组件主容器 */
.component-name { }

/* 组件子元素 */
.component-name-element { }

/* 状态类名 */
.component-name.is-active { }
.component-name.is-disabled { }
.component-name.is-loading { }

/* 修饰符类名 */
.component-name--variant { }
.component-name--size-large { }
```

### Adobe Spectrum 集成

```tsx
import { 
  Button, 
  View, 
  Text, 
  TreeView,
  ProgressCircle 
} from '@adobe/react-spectrum';

// 使用 UNSAFE_ 前缀的属性进行自定义
<Button 
  variant="accent"
  UNSAFE_className="custom-button"
  UNSAFE_style={{ marginTop: '10px' }}
>
  按钮文本
</Button>
```

### 响应式设计

```css
/* 移动端适配 */
@media (max-width: 768px) {
  .component-name {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .component-name {
    border: 2px solid;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .component-name {
    transition: none;
  }
}
```

## 开发规范

### 组件开发流程

1. **需求分析**：明确组件功能和使用场景
2. **接口设计**：定义 Props 接口和类型
3. **组件实现**：编写组件逻辑和 JSX
4. **样式开发**：创建对应的 CSS 文件
5. **测试编写**：单元测试和集成测试
6. **文档更新**：更新组件文档和使用示例

### TypeScript 规范

```typescript
// Props 接口定义
interface ComponentProps {
  /** 必需属性 - 添加 JSDoc 注释 */
  requiredProp: string;
  /** 可选属性 - 提供默认值说明 */
  optionalProp?: number;
  /** 回调函数 - 明确参数类型 */
  onAction?: (data: ActionData) => void;
  /** 联合类型 - 限制可选值 */
  variant?: 'primary' | 'secondary' | 'danger';
  /** 子组件 */
  children?: React.ReactNode;
}

// 组件实现
export const Component: React.FC<ComponentProps> = ({
  requiredProp,
  optionalProp = 0,
  onAction,
  variant = 'primary',
  children,
}) => {
  // 组件逻辑
  return (
    <div className={`component component--${variant}`}>
      {children}
    </div>
  );
};
```

### 性能优化规范

```typescript
// 使用 React.memo 优化重渲染
export const OptimizedComponent = React.memo<ComponentProps>(({
  data,
  onAction,
}) => {
  // 使用 useCallback 优化回调函数
  const handleClick = useCallback(() => {
    onAction?.(data);
  }, [data, onAction]);

  // 使用 useMemo 优化计算
  const processedData = useMemo(() => {
    return data.map(item => processItem(item));
  }, [data]);

  return (
    <div onClick={handleClick}>
      {processedData.map(item => (
        <Item key={item.id} data={item} />
      ))}
    </div>
  );
});
```

### 错误处理规范

```typescript
// 错误边界组件
class ComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Component Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>组件加载失败，请刷新页面重试</div>;
    }

    return this.props.children;
  }
}

// 组件内错误处理
export const SafeComponent: React.FC<Props> = ({ data }) => {
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // 可能出错的操作
      processData(data);
    } catch (err) {
      setError(err.message);
    }
  }, [data]);

  if (error) {
    return <div className="error-message">错误：{error}</div>;
  }

  return <div>正常内容</div>;
};
```

## 测试指南

### 单元测试

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { AudioPlayer } from './AudioPlayer';

describe('AudioPlayer', () => {
  test('renders with audio source', () => {
    render(<AudioPlayer audioSrc="/test-audio.mp3" />);
    
    const audioElement = screen.getByRole('button', { name: /播放/ });
    expect(audioElement).toBeInTheDocument();
  });

  test('calls onPlay when play button is clicked', () => {
    const onPlay = jest.fn();
    render(<AudioPlayer audioSrc="/test-audio.mp3" onPlay={onPlay} />);
    
    const playButton = screen.getByRole('button', { name: /播放/ });
    fireEvent.click(playButton);
    
    expect(onPlay).toHaveBeenCalled();
  });
});
```

### 集成测试

```typescript
import { render, screen } from '@testing-library/react';
import { NavigationTreeView } from './NavigationTreeView';

describe('NavigationTreeView Integration', () => {
  test('renders navigation data correctly', async () => {
    const navigationData = [
      { id: 'home', name: '首页', icon: 'Home' },
      { id: 'questions', name: '题目', icon: 'FileTxt' }
    ];

    render(
      <NavigationTreeView 
        navigationData={navigationData}
        loading={false}
        configurationDataReady={true}
      />
    );

    expect(await screen.findByText('首页')).toBeInTheDocument();
    expect(await screen.findByText('题目')).toBeInTheDocument();
  });
});
```

### 测试工具配置

```json
// jest.config.js
{
  "testEnvironment": "jsdom",
  "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"] // 可选：测试设置文件
  "moduleNameMapping": {
    "\\.(css|less|scss)$": "identity-obj-proxy"
  }
}
```

## 性能优化

### React 性能优化

1. **使用 React.memo**：防止不必要的重渲染
2. **useCallback 和 useMemo**：优化函数和计算结果缓存
3. **懒加载组件**：使用 React.lazy 和 Suspense
4. **虚拟化长列表**：对于大量数据使用虚拟滚动

### 渲染优化

```typescript
// 避免在渲染函数中创建对象
// ❌ 错误做法
function Component() {
  return <Child style={{ margin: 10 }} />;
}

// ✅ 正确做法
const childStyle = { margin: 10 };
function Component() {
  return <Child style={childStyle} />;
}

// 或使用 useMemo
function Component() {
  const childStyle = useMemo(() => ({ margin: 10 }), []);
  return <Child style={childStyle} />;
}
```

### 图片和资源优化

```typescript
// 图片懒加载
const LazyImage = React.lazy(() => import('./ImageWithRetry'));

function Gallery() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <LazyImage src="/large-image.jpg" />
    </Suspense>
  );
}

// 预加载关键资源
useEffect(() => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = '/critical-image.jpg';
  link.as = 'image';
  document.head.appendChild(link);
}, []);
```

## 故障排除

### 常见问题

#### 1. 组件样式不生效

**问题描述：** 组件样式没有正确应用

**解决方案：**
1. 检查 CSS 文件是否正确导入
2. 确认 CSS 类名拼写正确
3. 检查 CSS 选择器优先级
4. 使用浏览器开发者工具检查样式

```typescript
// 确保导入样式文件
import '../nexus-panel/src/components/AudioPlayer.css'; // 示例：导入对应的样式文件

// 检查类名是否正确
<div className="component-name" />
```

#### 2. TypeScript 类型错误

**问题描述：** TypeScript 编译错误

**解决方案：**
1. 检查 Props 接口定义
2. 确认导入的类型是否正确
3. 更新 TypeScript 版本
4. 检查 tsconfig.json 配置

```typescript
// 正确的类型定义
interface Props {
  data: DataType[];
  onSelect: (item: DataType) => void;
}
```

#### 3. 组件重渲染性能问题

**问题描述：** 组件频繁重渲染导致性能问题

**解决方案：**
1. 使用 React.memo 包装组件
2. 优化 props 传递，避免每次创建新对象
3. 使用 useCallback 和 useMemo
4. 检查依赖数组

```typescript
// 使用 React.memo 优化
export const OptimizedComponent = React.memo(Component);

// 优化回调函数
const handleClick = useCallback(() => {
  // 处理逻辑
}, [dependency]);
```

#### 4. Adobe Spectrum 组件样式冲突

**问题描述：** 自定义样式与 Spectrum 样式冲突

**解决方案：**
1. 使用 UNSAFE_className 和 UNSAFE_style
2. 提高 CSS 选择器优先级
3. 使用 CSS 模块或 styled-components
4. 检查 CSS 变量覆盖

```tsx
<Button 
  variant="accent"
  UNSAFE_className="custom-button"
  UNSAFE_style={{ 
    backgroundColor: 'var(--custom-color)',
    '&:hover': { backgroundColor: 'var(--custom-hover-color)' }
  }}
>
  自定义按钮
</Button>
```

#### 5. 图片加载问题

**问题描述：** 图片无法正常加载或显示

**解决方案：**
1. 检查图片路径是否正确
2. 确认图片格式是否支持
3. 检查网络连接和跨域设置
4. 使用 ImageWithRetry 组件处理加载失败

```tsx
<ImageWithRetry
  src="/images/photo.jpg"
  alt="描述"
  maxRetries={3}
  onError={(error) => console.error('图片加载失败:', error)}
/>
```

### 调试技巧

1. **使用 React Developer Tools**：检查组件状态和 props
2. **Console 日志**：添加适当的日志输出
3. **性能分析**：使用 React Profiler 分析性能
4. **网络面板**：检查资源加载情况

```typescript
// 调试日志
useEffect(() => {
  console.log('Component mounted with props:', props);
}, []);

// 性能监控
const handleExpensiveOperation = useCallback(() => {
  console.time('expensive-operation');
  // 执行操作
  console.timeEnd('expensive-operation');
}, []);
```

## 相关文档

- [API 使用指南](./API_GUIDE.md) - API 接口和数据管理
- [MQTT 完整指南](./MQTT_GUIDE.md) - 实时通信和消息处理
- [系统架构文档](./ARCHITECTURE.md) - 整体架构设计
- [故障排除指南](./TROUBLESHOOTING.md) - 问题诊断和解决
- [性能优化指南](./PERFORMANCE_OPTIMIZATION.md) - 性能优化策略
- [开发指南](./DEVELOPMENT_GUIDE.md) - 开发流程和规范

## 更新历史

- v1.0 (2025-01-15): 初始版本，整合所有组件文档
  - 合并 AudioPlayer、NavigationTreeView、SidebarButtonStyles 文档
  - 添加 DynamicComponentRenderer、ImageWithRetry、ContentToggle 文档
  - 完善组件架构和开发规范
  - 添加测试指南和性能优化建议
  - 提供完整的故障排除方案