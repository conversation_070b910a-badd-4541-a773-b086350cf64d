# 故障排除指南

**版本：** v1.0  
**最后更新：** 2025-01-15  
**维护者：** Nexus Panel 开发团队

## 概述

本文档提供了 Nexus Panel 应用程序常见问题的诊断和解决方案。涵盖了 MQTT 连接、API 请求、内存管理、组件渲染等各个方面的故障排除方法。

## 目录

- [MQTT 相关问题](#mqtt-相关问题)
- [API 请求问题](#api-请求问题)
- [内存管理问题](#内存管理问题)
- [组件渲染问题](#组件渲染问题)
- [性能问题](#性能问题)
- [数据同步问题](#数据同步问题)
- [调试工具使用](#调试工具使用)

## MQTT 相关问题

### 问题：MQTT 内存管理器无限循环

**症状：**
```
Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate.
```

**根本原因：**
- `start` 函数依赖 `memoryManager` 状态，同时内部会更新该状态
- useEffect 依赖链形成循环：`updateMemoryStats` → `startStatsTimer` → `start` → `memoryManager`
- 组件卸载清理函数依赖复杂状态

**解决方案：**

1. **使用 useRef 替代状态依赖**
```typescript
// 添加 ref 引用，避免状态依赖
const memoryManagerRef = useRef<MQTTMemoryManager | null>(null);

const updateMemoryStats = useCallback(() => {
  const currentManager = memoryManagerRef.current; // 使用 ref
  if (currentManager) {
    // 更新逻辑
  }
}, [log]); // 移除 memoryManager 依赖
```

2. **简化组件卸载清理**
```typescript
useEffect(() => {
  return () => {
    const currentManager = memoryManagerRef.current;
    if (currentManager) {
      currentManager.stop();
      memoryManagerRef.current = null;
    }
  };
}, []); // 空依赖数组
```

**验证方法：**
- 页面加载后不应出现无限循环错误
- 内存管理器应正常启动和显示统计信息
- 控制台日志应显示正常的启动序列

### 问题：日志数量不实时更新

**症状：**
- ConsolePanel 标题栏徽章实时更新
- 内存统计区域的日志数量延迟5秒更新

**根本原因：**
- 标题栏直接绑定 React 状态，自动更新
- 内存统计通过定时器获取，存在延迟

**解决方案：**

1. **添加主动触发更新机制**
```typescript
const addConsoleLog = useCallback((level, message, details) => {
  setConsoleLogs(prev => {
    const newLogs = [newLog, ...prev.slice(0, 99)];
    
    // 主动触发内存统计更新
    if (memoryManagerStartedRef.current) {
      setTimeout(() => {
        memoryManager.updateStats();
      }, 0);
    }
    
    return newLogs;
  });
}, [memoryManager]);
```

2. **使用 useRef 存储回调函数**
```typescript
const getLogCountRef = useRef(() => consoleLogs.length);

useEffect(() => {
  getLogCountRef.current = () => consoleLogs.length;
}, [consoleLogs.length]);
```

**验证方法：**
- 收到 MQTT 消息时，日志数量立即增加
- 手动清空日志时，日志数量立即归零
- 标题栏徽章和内存统计显示相同数值

## API 请求问题

### 问题：重复的 API 请求

**症状：**
- 进入争分夺秒环节时，bases API 请求4次
- 学生表和答题记录表被重复获取

**根本原因：**
- 多个组件同时使用 useRaceApi Hook，各自管理状态
- 缓存失效或未命中导致重复请求
- 组件初始化时序问题

**解决方案：**

1. **实现全局请求去重**
```typescript
// requestDeduplicator.ts
class GlobalRequestDeduplicator {
  private static pendingRequests = new Map<string, Promise<any>>();
  
  static async execute<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!;
    }
    
    const promise = requestFn();
    this.pendingRequests.set(key, promise);
    
    try {
      return await promise;
    } finally {
      this.pendingRequests.delete(key);
    }
  }
}
```

2. **优化数据复用策略**
```typescript
// 优先复用已有的选手列表数据
if (playerListData && playerListData.length > 0) {
  onLog?.('success', '✅ 复用已有选手列表数据，避免重复请求');
  
  if (!sectionRankingData && !sectionRankingLoading) {
    setTimeout(() => {
      fetchSectionRankingData(baseId, sectionName);
    }, 300);
  }
}
```

3. **实现单例模式**
```typescript
// 确保整个应用只有一个 useRaceApi 实例
const RaceApiProvider = ({ children }) => {
  const raceApiInstance = useRaceApi();
  return (
    <RaceApiContext.Provider value={raceApiInstance}>
      {children}
    </RaceApiContext.Provider>
  );
};
```

**验证方法：**
- 使用浏览器开发者工具监控网络请求
- 检查控制台的请求去重日志
- 确认 API 调用次数减少75%

### 问题：表结构缓存失效

**症状：**
- 频繁的 bases API 调用
- 缓存命中率低

**解决方案：**

1. **优化缓存键管理**
```typescript
// 确保 baseId 一致性
const cacheKey = `table_structure_${baseId}`;
```

2. **改进缓存验证逻辑**
```typescript
// 检查缓存有效性
const isValid = cached && 
  (Date.now() - cached.timestamp) < CACHE_DURATION &&
  cached.data && 
  cached.data.list && 
  cached.data.list.length > 0;
```

## 内存管理问题

### 问题：内存泄漏

**症状：**
- 页面长时间运行后内存使用持续增长
- 浏览器响应变慢

**诊断方法：**

1. **检查内存统计**
```typescript
// 查看内存管理器统计
console.log('Handler Count:', memoryManager.getHandlerCount());
console.log('Log Count:', memoryManager.getLogCount());
console.log('Cleanup History:', memoryManager.getCleanupHistory());
```

2. **监控清理频率**
- 正常：每天 < 3次清理
- 需要关注：每天 > 5次清理
- 需要处理：每小时 > 1次清理

**解决方案：**

1. **调整清理阈值**
```typescript
const config = {
  handlerThreshold: 50,    // 处理器数量阈值
  logThreshold: 1000,      // 日志数量阈值
  cleanupInterval: 300000  // 5分钟清理间隔
};
```

2. **手动清理**
```typescript
// 手动触发清理
memoryManager.cleanup();
```

### 问题：处理器数量过多

**症状：**
- 处理器数量 > 50个（红色警告）
- 系统响应变慢

**解决方案：**

1. **清理过期处理器**
```typescript
// 清理超过1小时未使用的处理器
memoryManager.cleanupExpiredHandlers();
```

2. **优化订阅策略**
```typescript
// 避免重复订阅相同主题
if (!mqtt.hasSubscription(topic)) {
  mqtt.subscribe(topic, handler);
}
```

## 组件渲染问题

### 问题：组件无限重新渲染

**症状：**
- CPU 使用率100%
- 页面卡死

**诊断方法：**
- 使用 React DevTools Profiler 识别重新渲染原因
- 检查 useEffect 依赖数组

**解决方案：**

1. **优化 useCallback 依赖**
```typescript
// 避免在依赖中包含会被修改的状态
const callback = useCallback(() => {
  // 使用 ref 访问最新值
  const currentValue = valueRef.current;
}, []); // 空依赖数组
```

2. **使用 React.memo 优化**
```typescript
const MyComponent = React.memo(({ data }) => {
  return <div>{data}</div>;
});
```

### 问题：数据未正确显示

**症状：**
- 组件渲染但数据为空
- 加载状态一直显示

**诊断方法：**

1. **检查数据流**
```typescript
console.log('Data:', data);
console.log('Loading:', loading);
console.log('Error:', error);
```

2. **验证 API 响应**
```typescript
// 检查 API 响应格式
console.log('API Response:', response.data);
```

**解决方案：**

1. **检查数据映射**
```typescript
// 确保数据结构正确
const mappedData = response.data.list.map(item => ({
  id: item.Id,
  name: item.选手名 || `选手${item.Id}`
}));
```

2. **添加错误边界**
```typescript
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('Component Error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }
    return this.props.children;
  }
}
```

## 性能问题

### 问题：页面加载缓慢

**诊断方法：**

1. **网络面板分析**
- 检查 API 请求数量和响应时间
- 识别阻塞请求

2. **性能面板分析**
- 检查 JavaScript 执行时间
- 识别性能瓶颈

**解决方案：**

1. **实现请求合并**
```typescript
// 合并多个相关请求
const [playerData, rankingData] = await Promise.all([
  getPlayerInfo(tableId),
  getRankingData(tableId)
]);
```

2. **添加加载优化**
```typescript
// 使用骨架屏提升用户体验
if (loading) {
  return <SkeletonComponent />;
}
```

### 问题：实时更新延迟

**症状：**
- 数据更新不及时
- 轮询间隔过长

**解决方案：**

1. **优化轮询策略**
```typescript
// 根据用户活动调整轮询间隔
const interval = userActive ? 2000 : 5000;
```

2. **实现智能更新**
```typescript
// 只在数据变化时更新UI
if (JSON.stringify(newData) !== JSON.stringify(oldData)) {
  setData(newData);
}
```

## 数据同步问题

### 问题：多组件数据不一致

**症状：**
- 不同组件显示不同的数据
- 状态更新不同步

**解决方案：**

1. **使用全局状态管理**
```typescript
// 通过 Context 共享状态
const DataContext = createContext();

const DataProvider = ({ children }) => {
  const [data, setData] = useState();
  return (
    <DataContext.Provider value={{ data, setData }}>
      {children}
    </DataContext.Provider>
  );
};
```

2. **实现数据同步机制**
```typescript
// 数据更新时通知所有相关组件
const updateData = (newData) => {
  setData(newData);
  notifyComponents(newData);
};
```

## 调试工具使用

### 浏览器开发者工具

1. **网络面板**
```javascript
// 过滤特定请求
// 在 Filter 中输入：records
// 查看 API 请求详情
```

2. **控制台调试**
```javascript
// 加载调试脚本
const script = document.createElement("script");
script.src = "/debug-api-requests.js";
document.head.appendChild(script);

// 开始监控
debugApiRequests.startMonitoring(30000);
```

### React DevTools

1. **组件检查**
- 查看组件 props 和 state
- 检查组件重新渲染原因

2. **性能分析**
- 使用 Profiler 识别性能问题
- 分析组件渲染时间

### 自定义调试工具

1. **内存统计监控**
```typescript
// 查看内存管理器状态
window.debugMemoryManager = () => {
  console.log('Memory Stats:', memoryManager.getMemoryStats());
};
```

2. **API 请求监控**
```typescript
// 监控 API 请求
window.debugApiRequests = () => {
  console.log('Pending Requests:', GlobalRequestDeduplicator.getPendingRequests());
};
```

## 常见问题快速解决

### 快速检查清单

- [ ] 检查控制台是否有错误信息
- [ ] 验证网络连接和 API 响应
- [ ] 确认组件 props 和 state 正确
- [ ] 检查内存使用和清理状态
- [ ] 验证 MQTT 连接状态
- [ ] 确认缓存命中率

### 紧急修复步骤

1. **页面无响应**
   - 刷新页面
   - 清除浏览器缓存
   - 检查网络连接

2. **数据不更新**
   - 手动刷新数据
   - 重新连接 MQTT
   - 清理缓存

3. **内存问题**
   - 手动清理内存
   - 重启应用
   - 检查内存泄漏

## 相关文档

- [性能优化指南](./PERFORMANCE_OPTIMIZATION.md)
- [API 使用指南](./API_GUIDE.md)
- [MQTT 使用指南](./MQTT_GUIDE.md)
- [组件开发指南](./COMPONENTS_GUIDE.md)

## 更新历史

- v1.0 (2025-01-15): 初始版本，整合所有故障排除文档