# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览

Nexus Panel是一个基于React + TypeScript + Vite构建的实时竞赛管理仪表板，支持实时问答竞赛场景，集成MQTT实现实时通信。

**核心功能**：
- 实时竞赛监控和参与者管理
- 动态题目管理和多种题型支持
- 多阶段竞赛（快答、终极PK等）
- MQTT实时通信集成
- Adobe React Spectrum设计系统

## 项目结构

```
nexus-panel/
├── nexus-panel/              # 主应用目录
│   ├── src/
│   │   ├── components/       # React组件
│   │   │   ├── common/       # 通用组件
│   │   │   ├── layout/       # 布局组件
│   │   │   └── question/     # 业务组件
│   │   ├── hooks/           # 自定义Hooks
│   │   ├── services/        # API和MQTT服务
│   │   ├── config/          # 配置管理
│   │   ├── types/           # TypeScript类型定义
│   │   └── utils/           # 工具函数
│   ├── public/              # 静态资源
│   └── package.json
├── docs/                    # 项目文档
└── scripts/                 # 文档维护脚本
```

## 常用命令

**开发命令**（在nexus-panel目录下执行）：
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run lint         # 代码检查
npm run preview      # 预览构建结果
```

**文档维护命令**：
```bash
npm run docs:validate          # 完整文档验证
npm run docs:check-links       # 检查文档链接
npm run docs:verify-code-refs  # 验证代码引用
npm run docs:check-duplicates  # 检测重复内容
npm run docs:full-check        # 完整文档检查
```

**文档维护调度**：
```bash
npm run docs:maintenance-init     # 初始化维护计划
npm run docs:maintenance-daily    # 执行日常维护
npm run docs:maintenance-status   # 查看维护状态
npm run docs:generate-weekly-reminder  # 生成周报
```

## 技术栈

- **前端**: React 19.1.0 + TypeScript 5.8.3
- **构建**: Vite 6.3.5
- **UI框架**: Adobe React Spectrum 3.42.1
- **实时通信**: MQTT 5.13.1
- **动画**: Motion 12.23.0
- **代码质量**: ESLint 9.25.0 + Prettier 3.6.0

## 开发环境

**环境要求**：
- Node.js 18+
- npm 8+

**环境变量**（.env.development）：
```
VITE_API_BASE_URL=http://localhost:3000
VITE_MQTT_BROKER_URL=wss://ws.ohvfx.com:8084/mqtt
VITE_DEBUG_MODE=true
```

## 核心架构模式

**数据流**：
1. 用户交互 → React组件 → 自定义Hooks
2. API服务/ MQTT服务 → 状态管理 → 组件更新
3. MQTT消息 → 实时更新 → 界面同步

**关键服务**：
- `services/api/` - HTTP API通信
- `services/mqtt/` - MQTT实时通信
- `hooks/useRaceApi/` - 竞赛数据管理
- `hooks/useMQTT*` - MQTT集成Hooks

## 代码规范

**命名规范**：
- React组件: PascalCase (`TimeRaceTimer.tsx`)
- Hooks: camelCase + use前缀 (`useRaceApi.ts`)
- 工具函数: camelCase (`rankingUtils.ts`)
- 类型定义: PascalCase (`ApiResponse`)

**样式规范**：
- 使用BEM命名规范
- CSS自定义属性管理主题
- 组件级CSS文件与组件同名

## 调试要点

**React调试**：
- 使用React Developer Tools
- 组件添加displayName便于调试
- 使用Profiler进行性能分析

**MQTT调试**：
- 检查`services/mqtt/MQTTService.ts`中的连接配置
- 使用MQTTX客户端测试连接
- 监控`useMQTT*`相关hooks的订阅状态

**网络调试**：
- 检查`services/api/client.ts`中的API配置
- 使用浏览器网络面板监控请求
- 验证CORS配置

## 文档系统

**文档结构**：
- `docs/README.md` - 项目总览
- `docs/DEVELOPMENT_GUIDE.md` - 开发指南
- `docs/ARCHITECTURE.md` - 系统架构
- `docs/API_GUIDE.md` - API使用指南
- `docs/COMPONENTS_GUIDE.md` - 组件开发指南
- `docs/MQTT_GUIDE.md` - MQTT集成指南

**文档模板**：
- `docs/templates/` - 包含标准文档模板
- 使用metadata-schema.json定义文档元数据标准