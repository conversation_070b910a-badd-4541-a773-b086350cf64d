# 文档验证和维护工具

这个目录包含了用于验证和维护 Nexus Panel 项目文档的自动化工具。这些工具是 docs-optimization 规范实现的一部分。

## 🛠️ 工具概览

### 1. 文档验证器 (`validate-docs.js`)
**功能**: 全面验证文档质量和一致性
- 验证文档结构和命名规范
- 检查文档元数据（版本、更新日期、维护者等）
- 验证内部链接有效性
- 检查代码文件引用的准确性
- 检测重复内容

**使用方法**:
```bash
node scripts/validate-docs.js
# 或者在 nexus-panel 目录中
npm run docs:validate
```

### 2. 重复内容检测器 (`check-duplicate-content.js`)
**功能**: 高级重复和相似内容检测
- 检测完全重复的文件
- 使用多种算法识别相似内容（Jaccard、余弦相似度、编辑距离）
- 发现重复的标题和代码块
- 生成详细的重复内容报告

**使用方法**:
```bash
node scripts/check-duplicate-content.js
# 或者在 nexus-panel 目录中
npm run docs:check-duplicates
```

### 3. 文档分析器 (`analyze-docs.js`)
**功能**: 分析文档内容和结构
- 生成文档内容摘要和分类映射
- 识别过时信息和需要更新的内容
- 创建文档迁移计划
- 提供文档优化建议

**使用方法**:
```bash
node scripts/analyze-docs.js
# 或者在 nexus-panel 目录中
npm run docs:analyze
```

### 4. 文档测试套件 (`docs-test-suite.js`)
**功能**: 自动化测试流程编排
- 运行所有文档验证工具
- 生成综合测试报告
- 提供 CI/CD 集成支持
- 输出 JSON 和 Markdown 格式的结果

**使用方法**:
```bash
node scripts/docs-test-suite.js
# 或者在 nexus-panel 目录中
npm run docs:test-suite
```

## 📊 生成的报告

### 文档分析报告 (`docs-analysis-report.md`)
- 文档统计信息
- 按类别分布的文件列表
- 详细的文件分析结果
- 重复内容和过时文档识别
- 文档迁移计划建议

### 重复内容报告 (`duplicate-content-report.md`)
- 完全重复文件列表
- 相似内容分析（包含相似度百分比）
- 重复标题统计
- 重复代码块检测
- 具体的处理建议

### 测试套件报告 (`docs-test-results.md`)
- 所有测试项目的执行状态
- 详细的错误信息和警告
- 质量指标评估
- 改进建议和行动项

## 🚀 快速开始

### 运行完整的文档质量检查
```bash
# 在项目根目录
node scripts/docs-test-suite.js

# 或者在 nexus-panel 目录中
npm run docs:test-suite
```

### 检查特定问题
```bash
# 只检查重复内容
npm run docs:check-duplicates

# 只验证文档结构和链接
npm run docs:validate

# 分析文档内容和分类
npm run docs:analyze
```

## 📋 集成到开发流程

### 1. 预提交检查
在 `.git/hooks/pre-commit` 中添加：
```bash
#!/bin/sh
echo "Running documentation validation..."
npm run docs:validate
if [ $? -ne 0 ]; then
  echo "Documentation validation failed. Please fix the issues before committing."
  exit 1
fi
```

### 2. CI/CD 集成
在 GitHub Actions 或其他 CI 系统中：
```yaml
- name: Validate Documentation
  run: |
    cd nexus-panel
    npm run docs:test-suite
```

### 3. 定期维护
建议每周运行一次完整的文档测试套件：
```bash
# 设置 cron job
0 9 * * 1 cd /path/to/nexus-panel && npm run docs:test-suite
```

## 🔧 配置和自定义

### 文档元数据要求
所有文档应包含以下元数据：
```markdown
**版本**: v1.0
**最后更新**: 2025-07-16
**维护者**: [负责人姓名]
**类别**: [api|components|mqtt|architecture|troubleshooting|performance|development]
```

### 文件命名规范
- 主要文档使用大写字母和下划线：`API_GUIDE.md`
- 组件文档使用 PascalCase：`AudioPlayer.md`
- 避免文件名中包含空格
- 使用描述性的文件名

### 目录结构要求
```
docs/
├── README.md                    # 项目总览
├── ARCHITECTURE.md              # 系统架构
├── API_GUIDE.md                 # API 指南
├── COMPONENTS_GUIDE.md          # 组件指南
├── MQTT_GUIDE.md               # MQTT 指南
├── TROUBLESHOOTING.md          # 故障排除
├── PERFORMANCE_OPTIMIZATION.md # 性能优化
├── DEVELOPMENT_GUIDE.md        # 开发指南
├── RELEASE_NOTES.md            # 发布说明
├── templates/                  # 文档模板
└── archive/                    # 归档文档
    ├── legacy/                 # 过时文档
    └── fixes/                  # 历史修复记录
```

## 🐛 故障排除

### 常见问题

**问题**: 脚本报告"文件未找到"错误
**解决**: 确保在正确的目录中运行脚本，或使用 npm scripts

**问题**: 大量的元数据缺失警告
**解决**: 这是预期的，因为现有文档还未按新标准更新

**问题**: 代码引用验证失败
**解决**: 检查文档中引用的文件路径是否正确，更新不存在的引用

### 调试模式
设置环境变量启用详细输出：
```bash
DEBUG=1 node scripts/validate-docs.js
```

## 📈 性能优化

### 大型项目优化
- 使用 `--parallel` 标志并行处理文件
- 设置 `--max-files` 限制处理的文件数量
- 使用 `--exclude` 排除不需要检查的目录

### 内存使用
- 大文件处理时可能需要增加 Node.js 内存限制：
```bash
node --max-old-space-size=4096 scripts/docs-test-suite.js
```

## 🤝 贡献指南

### 添加新的验证规则
1. 在相应的脚本中添加验证逻辑
2. 更新测试用例
3. 更新文档说明

### 报告问题
请在项目 issue 中报告：
- 脚本版本和 Node.js 版本
- 完整的错误信息
- 重现步骤

## 📚 相关文档

- [文档优化规范](.kiro/specs/docs-optimization/)
- [项目架构文档](../docs/ARCHITECTURE.md)
- [开发指南](../docs/DEVELOPMENT_GUIDE.md)

---

*这些工具是 Nexus Panel 文档优化项目的一部分，旨在提高文档质量和维护效率。*