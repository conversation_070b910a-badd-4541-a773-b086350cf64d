#!/usr/bin/env node

/**
 * 新开发者体验测试脚本
 * 模拟新开发者使用文档的完整流程
 */

const fs = require('fs');
const path = require('path');

class NewDeveloperExperienceTest {
  constructor() {
    this.testResults = [];
    this.score = 0;
    this.maxScore = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${prefix} ${message}`);
  }

  addTest(name, passed, weight = 1, details = '') {
    this.testResults.push({
      name,
      passed,
      weight,
      details,
      timestamp: new Date().toISOString()
    });
    
    this.maxScore += weight;
    if (passed) {
      this.score += weight;
    }
  }

  // 测试1: 项目概览和快速开始
  testProjectOverview() {
    this.log('🏁 Testing project overview and quick start...');
    
    const readmeExists = fs.existsSync('docs/README.md');
    this.addTest('Documentation README exists', readmeExists, 2);
    
    if (readmeExists) {
      const content = fs.readFileSync('docs/README.md', 'utf8');
      
      // 检查项目描述
      const hasDescription = content.includes('Nexus Panel') || content.includes('项目概述');
      this.addTest('Has project description', hasDescription, 2);
      
      // 检查文档结构说明
      const hasStructure = content.includes('文档结构') || content.includes('目录结构');
      this.addTest('Has documentation structure', hasStructure, 1);
      
      // 检查快速导航
      const hasNavigation = content.includes('快速导航') || content.includes('Quick Navigation');
      this.addTest('Has quick navigation', hasNavigation, 1);
    }
    
    // 检查项目根目录README
    const projectReadmeExists = fs.existsSync('nexus-panel/README.md');
    this.addTest('Project README exists', projectReadmeExists, 2);
    
    if (projectReadmeExists) {
      const content = fs.readFileSync('nexus-panel/README.md', 'utf8');
      
      // 检查安装说明
      const hasInstallation = content.includes('npm install') || content.includes('安装');
      this.addTest('Has installation instructions', hasInstallation, 3);
      
      // 检查运行说明
      const hasRunInstructions = content.includes('npm run') || content.includes('运行');
      this.addTest('Has run instructions', hasRunInstructions, 3);
    }
  }

  // 测试2: 开发环境设置
  testDevelopmentSetup() {
    this.log('⚙️ Testing development setup guidance...');
    
    const devGuideExists = fs.existsSync('docs/DEVELOPMENT_GUIDE.md');
    this.addTest('Development guide exists', devGuideExists, 3);
    
    if (devGuideExists) {
      const content = fs.readFileSync('docs/DEVELOPMENT_GUIDE.md', 'utf8');
      
      // 检查环境要求
      const hasRequirements = content.includes('环境要求') || content.includes('Requirements');
      this.addTest('Has environment requirements', hasRequirements, 2);
      
      // 检查开发工具配置
      const hasToolConfig = content.includes('开发工具') || content.includes('Development Tools');
      this.addTest('Has development tools configuration', hasToolConfig, 2);
      
      // 检查常见问题
      const hasTroubleshooting = content.includes('常见问题') || content.includes('故障排除');
      this.addTest('Has troubleshooting section', hasTroubleshooting, 1);
    }
  }

  // 测试3: 架构理解
  testArchitectureUnderstanding() {
    this.log('🏗️ Testing architecture understanding...');
    
    const archGuideExists = fs.existsSync('docs/ARCHITECTURE.md');
    this.addTest('Architecture guide exists', archGuideExists, 3);
    
    if (archGuideExists) {
      const content = fs.readFileSync('docs/ARCHITECTURE.md', 'utf8');
      
      // 检查系统概览
      const hasOverview = content.includes('系统概览') || content.includes('System Overview');
      this.addTest('Has system overview', hasOverview, 2);
      
      // 检查技术栈说明
      const hasTechStack = content.includes('技术栈') || content.includes('Technology Stack');
      this.addTest('Has technology stack', hasTechStack, 2);
      
      // 检查目录结构
      const hasStructure = content.includes('目录结构') || content.includes('Directory Structure');
      this.addTest('Has directory structure', hasStructure, 1);
    }
    
    // 检查结构概览文档
    const structureExists = fs.existsSync('docs/STRUCTURE_OVERVIEW.md');
    this.addTest('Structure overview exists', structureExists, 2);
  }

  // 测试4: API和组件使用
  testAPIAndComponentUsage() {
    this.log('🔧 Testing API and component usage guidance...');
    
    const apiGuideExists = fs.existsSync('docs/API_GUIDE.md');
    this.addTest('API guide exists', apiGuideExists, 3);
    
    if (apiGuideExists) {
      const content = fs.readFileSync('docs/API_GUIDE.md', 'utf8');
      
      // 检查API使用示例
      const hasExamples = content.includes('使用示例') || content.includes('Examples');
      this.addTest('Has API usage examples', hasExamples, 2);
      
      // 检查错误处理
      const hasErrorHandling = content.includes('错误处理') || content.includes('Error Handling');
      this.addTest('Has error handling guidance', hasErrorHandling, 1);
    }
    
    const componentGuideExists = fs.existsSync('docs/COMPONENTS_GUIDE.md');
    this.addTest('Component guide exists', componentGuideExists, 3);
    
    if (componentGuideExists) {
      const content = fs.readFileSync('docs/COMPONENTS_GUIDE.md', 'utf8');
      
      // 检查组件使用示例
      const hasComponentExamples = content.includes('组件示例') || content.includes('Component Examples');
      this.addTest('Has component examples', hasComponentExamples, 2);
      
      // 检查样式指南
      const hasStyleGuide = content.includes('样式') || content.includes('Style');
      this.addTest('Has styling guidance', hasStyleGuide, 1);
    }
  }

  // 测试5: 故障排除和支持
  testTroubleshootingSupport() {
    this.log('🔧 Testing troubleshooting and support...');
    
    const troubleshootingExists = fs.existsSync('docs/TROUBLESHOOTING.md');
    this.addTest('Troubleshooting guide exists', troubleshootingExists, 3);
    
    if (troubleshootingExists) {
      const content = fs.readFileSync('docs/TROUBLESHOOTING.md', 'utf8');
      
      // 检查常见问题
      const hasCommonIssues = content.includes('常见问题') || content.includes('Common Issues');
      this.addTest('Has common issues section', hasCommonIssues, 2);
      
      // 检查调试指南
      const hasDebugging = content.includes('调试') || content.includes('Debug');
      this.addTest('Has debugging guidance', hasDebugging, 2);
      
      // 检查性能问题
      const hasPerformance = content.includes('性能') || content.includes('Performance');
      this.addTest('Has performance troubleshooting', hasPerformance, 1);
    }
  }

  // 测试6: 文档导航和发现性
  testDocumentationNavigation() {
    this.log('🧭 Testing documentation navigation and discoverability...');
    
    // 检查文档间的链接
    const docsDir = 'docs';
    const files = fs.readdirSync(docsDir).filter(f => f.endsWith('.md'));
    
    let totalLinks = 0;
    let internalLinks = 0;
    
    for (const file of files) {
      const content = fs.readFileSync(path.join(docsDir, file), 'utf8');
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      let match;
      
      while ((match = linkRegex.exec(content)) !== null) {
        totalLinks++;
        const linkUrl = match[2];
        
        if (linkUrl.startsWith('./') || linkUrl.startsWith('../') || 
            (linkUrl.endsWith('.md') && !linkUrl.startsWith('http'))) {
          internalLinks++;
        }
      }
    }
    
    const linkRatio = internalLinks / totalLinks;
    this.addTest('Good internal linking ratio', linkRatio > 0.3, 2, 
      `Internal links: ${internalLinks}/${totalLinks} (${Math.round(linkRatio * 100)}%)`);
    
    // 检查目录结构
    const hasTemplates = fs.existsSync('docs/templates');
    this.addTest('Has documentation templates', hasTemplates, 1);
    
    // Archive directory is optional - removed to avoid unnecessary references
    // const hasArchive = fs.existsSync('docs/archive');
    // this.addTest('Has archived documentation', hasArchive, 1);
  }

  // 生成体验报告
  generateExperienceReport() {
    const successRate = Math.round((this.score / this.maxScore) * 100);
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.testResults.length,
        passedTests: this.testResults.filter(t => t.passed).length,
        failedTests: this.testResults.filter(t => !t.passed).length,
        score: this.score,
        maxScore: this.maxScore,
        successRate: successRate
      },
      testResults: this.testResults,
      recommendations: this.generateRecommendations(),
      experienceLevel: this.getExperienceLevel(successRate)
    };
    
    // 保存详细报告
    fs.writeFileSync('new-developer-experience-results.json', JSON.stringify(report, null, 2));
    
    // 生成可读报告
    this.generateReadableExperienceReport(report);
    
    return report;
  }

  generateRecommendations() {
    const failedTests = this.testResults.filter(t => !t.passed);
    const recommendations = [];
    
    if (failedTests.some(t => t.name.includes('README'))) {
      recommendations.push('改善项目README文档，添加清晰的项目描述和快速开始指南');
    }
    
    if (failedTests.some(t => t.name.includes('installation'))) {
      recommendations.push('添加详细的安装和运行说明');
    }
    
    if (failedTests.some(t => t.name.includes('Architecture') || t.name.includes('overview'))) {
      recommendations.push('完善架构文档，帮助新开发者理解系统结构');
    }
    
    if (failedTests.some(t => t.name.includes('examples'))) {
      recommendations.push('增加更多实用的代码示例和使用案例');
    }
    
    if (failedTests.some(t => t.name.includes('Troubleshooting'))) {
      recommendations.push('建立完善的故障排除和支持文档');
    }
    
    return recommendations;
  }

  getExperienceLevel(successRate) {
    if (successRate >= 90) return '优秀 (Excellent)';
    if (successRate >= 80) return '良好 (Good)';
    if (successRate >= 70) return '一般 (Fair)';
    if (successRate >= 60) return '需要改进 (Needs Improvement)';
    return '较差 (Poor)';
  }

  generateReadableExperienceReport(report) {
    const content = `# 新开发者体验测试报告

**执行时间**: ${report.timestamp}
**体验评级**: ${report.experienceLevel}
**成功率**: ${report.summary.successRate}%

## 📊 测试概览

- 总测试数: ${report.summary.totalTests}
- 通过测试: ${report.summary.passedTests}
- 失败测试: ${report.summary.failedTests}
- 得分: ${report.summary.score}/${report.summary.maxScore}

## 📋 详细测试结果

${report.testResults.map(test => {
  const status = test.passed ? '✅' : '❌';
  const details = test.details ? ` (${test.details})` : '';
  return `${status} **${test.name}** (权重: ${test.weight})${details}`;
}).join('\n')}

## 💡 改进建议

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

## 🎯 新开发者入门路径建议

### 第一步：项目了解
1. 阅读项目README了解基本概念
2. 查看架构文档理解系统设计
3. 浏览文档结构概览

### 第二步：环境搭建
1. 按照开发指南设置环境
2. 运行项目并验证功能
3. 熟悉开发工具配置

### 第三步：代码理解
1. 学习API使用方法
2. 了解组件开发规范
3. 查看实际代码示例

### 第四步：问题解决
1. 遇到问题时查看故障排除指南
2. 了解常见问题和解决方案
3. 学会使用调试工具

## 📈 体验质量指标

- **文档完整性**: ${Math.round((report.testResults.filter(t => t.name.includes('exists')).filter(t => t.passed).length / report.testResults.filter(t => t.name.includes('exists')).length) * 100)}%
- **内容质量**: ${Math.round((report.testResults.filter(t => !t.name.includes('exists')).filter(t => t.passed).length / report.testResults.filter(t => !t.name.includes('exists')).length) * 100)}%
- **导航便利性**: ${Math.round((report.testResults.filter(t => t.name.includes('navigation') || t.name.includes('linking')).filter(t => t.passed).length / Math.max(1, report.testResults.filter(t => t.name.includes('navigation') || t.name.includes('linking')).length)) * 100)}%

---
*此报告模拟新开发者使用文档的完整体验流程*
`;

    fs.writeFileSync('new-developer-experience-report.md', content);
  }

  // 运行所有测试
  async runAllTests() {
    this.log('🚀 Starting new developer experience testing...');
    
    this.testProjectOverview();
    this.testDevelopmentSetup();
    this.testArchitectureUnderstanding();
    this.testAPIAndComponentUsage();
    this.testTroubleshootingSupport();
    this.testDocumentationNavigation();
    
    const report = this.generateExperienceReport();
    
    this.log(`📊 Testing completed. Experience level: ${report.experienceLevel}`);
    this.log(`📄 Detailed report saved to: new-developer-experience-report.md`);
    
    return report.summary.successRate >= 80;
  }
}

// 运行测试
if (require.main === module) {
  const tester = new NewDeveloperExperienceTest();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = NewDeveloperExperienceTest;