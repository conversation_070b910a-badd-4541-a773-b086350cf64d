#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get all markdown files in docs directory
function getAllMarkdownFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'archive') {
      files.push(...getAllMarkdownFiles(fullPath));
    } else if (stat.isFile() && item.endsWith('.md')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Check if a file exists
function fileExists(filePath) {
  try {
    return fs.statSync(filePath).isFile();
  } catch (e) {
    return false;
  }
}

// Validate all links in a file
function validateFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const docsDir = path.resolve('docs');
  const fileDir = path.dirname(filePath);
  const relativePath = path.relative('.', filePath);
  
  let issues = [];
  
  // Check markdown links
  const linkRegex = /\[([^\]]+)\]\(([^)]+\.md)\)/g;
  let match;
  while ((match = linkRegex.exec(content)) !== null) {
    const [fullMatch, linkText, linkPath] = match;
    
    // Skip external links
    if (linkPath.startsWith('http')) continue;
    
    // Resolve the target file path
    let targetPath;
    if (linkPath.startsWith('./')) {
      targetPath = path.resolve(fileDir, linkPath);
    } else if (linkPath.startsWith('../')) {
      targetPath = path.resolve(fileDir, linkPath);
    } else {
      targetPath = path.resolve(docsDir, linkPath);
    }
    
    if (!fileExists(targetPath)) {
      issues.push({
        type: 'broken_link',
        text: fullMatch,
        target: linkPath,
        line: content.substring(0, match.index).split('\n').length
      });
    }
  }
  
  // Check code file references
  const codeRefRegex = /nexus-panel\/src\/([^`\s)]+\.(ts|tsx|js|jsx|css))/g;
  let codeMatch;
  while ((codeMatch = codeRefRegex.exec(content)) !== null) {
    const [fullMatch, relativePath] = codeMatch;
    const fullCodePath = path.resolve('nexus-panel/src', relativePath);
    
    if (!fileExists(fullCodePath)) {
      issues.push({
        type: 'broken_code_ref',
        text: fullMatch,
        target: relativePath,
        line: content.substring(0, codeMatch.index).split('\n').length
      });
    }
  }
  
  return {
    file: relativePath,
    issues: issues
  };
}

// Main validation function
function main() {
  console.log('🔍 Validating all documentation links and references...\n');
  
  const docsDir = path.resolve('docs');
  const markdownFiles = getAllMarkdownFiles(docsDir);
  
  // Also check nexus-panel README
  const nexusReadme = path.resolve('nexus-panel/README.md');
  if (fileExists(nexusReadme)) {
    markdownFiles.push(nexusReadme);
  }
  
  let totalIssues = 0;
  let filesWithIssues = 0;
  
  for (const file of markdownFiles) {
    const result = validateFile(file);
    
    if (result.issues.length > 0) {
      filesWithIssues++;
      totalIssues += result.issues.length;
      
      console.log(`❌ ${result.file}:`);
      for (const issue of result.issues) {
        if (issue.type === 'broken_link') {
          console.log(`   Line ${issue.line}: Broken link to ${issue.target}`);
        } else if (issue.type === 'broken_code_ref') {
          console.log(`   Line ${issue.line}: Broken code reference ${issue.text}`);
        }
      }
      console.log('');
    } else {
      console.log(`✅ ${result.file}`);
    }
  }
  
  console.log(`\n📊 Validation Summary:`);
  console.log(`   Files checked: ${markdownFiles.length}`);
  console.log(`   Files with issues: ${filesWithIssues}`);
  console.log(`   Total issues: ${totalIssues}`);
  
  if (totalIssues === 0) {
    console.log(`\n🎉 All documentation links and references are valid!`);
  } else {
    console.log(`\n⚠️  Found ${totalIssues} issues that need to be fixed.`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { validateFile, getAllMarkdownFiles };