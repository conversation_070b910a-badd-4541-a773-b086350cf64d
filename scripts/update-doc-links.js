#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get all markdown files in docs directory
function getAllMarkdownFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'archive') {
      files.push(...getAllMarkdownFiles(fullPath));
    } else if (stat.isFile() && item.endsWith('.md')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Check if a file exists
function fileExists(filePath) {
  try {
    return fs.statSync(filePath).isFile();
  } catch (e) {
    return false;
  }
}

// Update internal markdown links in a file
function updateLinksInFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const docsDir = path.resolve('docs');
  const fileDir = path.dirname(filePath);
  
  // Find all markdown links
  const linkRegex = /\[([^\]]+)\]\(([^)]+\.md)\)/g;
  let updatedContent = content;
  let hasChanges = false;
  
  let match;
  while ((match = linkRegex.exec(content)) !== null) {
    const [fullMatch, linkText, linkPath] = match;
    
    // Skip external links
    if (linkPath.startsWith('http')) continue;
    
    // Resolve the target file path
    let targetPath;
    if (linkPath.startsWith('./')) {
      targetPath = path.resolve(fileDir, linkPath);
    } else if (linkPath.startsWith('../')) {
      targetPath = path.resolve(fileDir, linkPath);
    } else {
      targetPath = path.resolve(docsDir, linkPath);
    }
    
    // Check if target file exists
    if (!fileExists(targetPath)) {
      console.log(`❌ Broken link in ${path.relative('.', filePath)}: ${linkPath}`);
      
      // Try to find the file in docs directory
      const fileName = path.basename(linkPath);
      const possiblePath = path.join(docsDir, fileName);
      
      if (fileExists(possiblePath)) {
        const relativePath = path.relative(fileDir, possiblePath);
        const newLink = `[${linkText}](${relativePath})`;
        updatedContent = updatedContent.replace(fullMatch, newLink);
        hasChanges = true;
        console.log(`✅ Fixed link: ${linkPath} → ${relativePath}`);
      }
    } else {
      console.log(`✅ Valid link in ${path.relative('.', filePath)}: ${linkPath}`);
    }
  }
  
  // Update source code file references
  const codeRefRegex = /nexus-panel\/src\/([^`\s)]+\.(ts|tsx|js|jsx|css))/g;
  let codeMatch;
  while ((codeMatch = codeRefRegex.exec(content)) !== null) {
    const [fullMatch, relativePath] = codeMatch;
    const fullCodePath = path.resolve('nexus-panel/src', relativePath);
    
    if (!fileExists(fullCodePath)) {
      console.log(`❌ Broken code reference in ${path.relative('.', filePath)}: ${fullMatch}`);
    } else {
      console.log(`✅ Valid code reference in ${path.relative('.', filePath)}: ${fullMatch}`);
    }
  }
  
  // Write updated content if there were changes
  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    console.log(`📝 Updated ${path.relative('.', filePath)}`);
  }
  
  return hasChanges;
}

// Main function
function main() {
  console.log('🔍 Scanning documentation for internal links...\n');
  
  const docsDir = path.resolve('docs');
  const markdownFiles = getAllMarkdownFiles(docsDir);
  
  let totalUpdated = 0;
  
  for (const file of markdownFiles) {
    console.log(`\n📄 Checking ${path.relative('.', file)}:`);
    if (updateLinksInFile(file)) {
      totalUpdated++;
    }
  }
  
  console.log(`\n✨ Scan complete! Updated ${totalUpdated} files.`);
}

if (require.main === module) {
  main();
}

module.exports = { updateLinksInFile, getAllMarkdownFiles };