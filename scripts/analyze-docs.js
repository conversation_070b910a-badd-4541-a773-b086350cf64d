#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Document Analysis Script for Nexus Panel Documentation Optimization
 * 
 * This script analyzes all markdown files in the docs directory to:
 * 1. Generate content summaries and classification mappings
 * 2. Identify duplicate content and outdated information
 * 3. Create a document migration plan
 */

class DocumentAnalyzer {
  constructor() {
    this.docsDir = path.join(process.cwd(), 'docs');
    this.results = {
      files: [],
      categories: {},
      duplicates: [],
      outdated: [],
      migrationPlan: []
    };
    
    // Define content categories based on design document
    this.categories = {
      'API': {
        keywords: ['api', 'endpoint', 'request', 'response', 'race', 'ranking', 'duplicate'],
        description: 'API related documentation including endpoints, testing, and architecture'
      },
      'MQTT': {
        keywords: ['mqtt', 'message', 'broker', 'publish', 'subscribe', 'integration'],
        description: 'MQTT communication and integration documentation'
      },
      'COMPONENTS': {
        keywords: ['component', 'audio', 'player', 'button', 'sidebar', 'ui', 'css', 'style'],
        description: 'UI components, styling, and component development'
      },
      'PERFORMANCE': {
        keywords: ['performance', 'memory', 'leak', 'optimization', 'stats'],
        description: 'Performance analysis, optimization, and memory management'
      },
      'FIXES': {
        keywords: ['fix', 'bug', 'issue', 'loop', 'error', 'hook'],
        description: 'Bug fixes, issue resolutions, and troubleshooting'
      },
      'ARCHITECTURE': {
        keywords: ['architecture', 'singleton', 'flow', 'structure', 'design'],
        description: 'System architecture and design documentation'
      },
      'RELEASE': {
        keywords: ['release', 'update', 'changelog', 'version', 'notes'],
        description: 'Release notes, updates, and version history'
      },
      'TASKS': {
        keywords: ['task', 'todo', 'progress', 'verification'],
        description: 'Task tracking and project management'
      }
    };
  }

  async analyzeDocuments() {
    console.log('🔍 Starting document analysis...\n');
    
    try {
      const files = await this.getMarkdownFiles();
      
      for (const file of files) {
        await this.analyzeFile(file);
      }
      
      this.identifyDuplicates();
      this.identifyOutdated();
      this.generateMigrationPlan();
      this.generateReport();
      
      console.log('✅ Analysis complete! Check the generated reports.\n');
      
    } catch (error) {
      console.error('❌ Error during analysis:', error.message);
      process.exit(1);
    }
  }

  async getMarkdownFiles() {
    const files = fs.readdirSync(this.docsDir)
      .filter(file => file.endsWith('.md'))
      .map(file => path.join(this.docsDir, file));
    
    console.log(`📁 Found ${files.length} markdown files to analyze`);
    return files;
  }

  async analyzeFile(filePath) {
    const fileName = path.basename(filePath);
    const content = fs.readFileSync(filePath, 'utf8');
    
    const analysis = {
      fileName,
      filePath,
      size: content.length,
      lines: content.split('\n').length,
      wordCount: content.split(/\s+/).length,
      lastModified: fs.statSync(filePath).mtime,
      categories: this.categorizeContent(content, fileName),
      summary: this.generateSummary(content),
      headings: this.extractHeadings(content),
      codeBlocks: this.extractCodeBlocks(content),
      links: this.extractLinks(content),
      isOutdated: this.checkIfOutdated(content, fileName),
      contentHash: this.generateContentHash(content)
    };
    
    this.results.files.push(analysis);
    
    // Add to category mappings
    analysis.categories.forEach(category => {
      if (!this.results.categories[category]) {
        this.results.categories[category] = [];
      }
      this.results.categories[category].push(fileName);
    });
    
    console.log(`📄 Analyzed: ${fileName} (${analysis.categories.join(', ')})`);
  }

  categorizeContent(content, fileName) {
    const categories = [];
    const lowerContent = content.toLowerCase();
    const lowerFileName = fileName.toLowerCase();
    
    for (const [category, config] of Object.entries(this.categories)) {
      const score = config.keywords.reduce((acc, keyword) => {
        const contentMatches = (lowerContent.match(new RegExp(keyword, 'g')) || []).length;
        const fileNameMatches = (lowerFileName.match(new RegExp(keyword, 'g')) || []).length * 2; // Weight filename matches higher
        return acc + contentMatches + fileNameMatches;
      }, 0);
      
      if (score > 0) {
        categories.push({ category, score });
      }
    }
    
    // Sort by score and return category names
    return categories
      .sort((a, b) => b.score - a.score)
      .map(item => item.category);
  }

  generateSummary(content) {
    const lines = content.split('\n');
    const firstHeading = lines.find(line => line.startsWith('#'));
    const firstParagraph = lines.find(line => line.trim() && !line.startsWith('#') && !line.startsWith('```'));
    
    return {
      title: firstHeading ? firstHeading.replace(/^#+\s*/, '') : 'No title found',
      description: firstParagraph ? firstParagraph.substring(0, 200) + '...' : 'No description found'
    };
  }

  extractHeadings(content) {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings = [];
    let match;
    
    while ((match = headingRegex.exec(content)) !== null) {
      headings.push({
        level: match[1].length,
        text: match[2]
      });
    }
    
    return headings;
  }

  extractCodeBlocks(content) {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const codeBlocks = [];
    let match;
    
    while ((match = codeBlockRegex.exec(content)) !== null) {
      codeBlocks.push({
        language: match[1] || 'unknown',
        code: match[2]
      });
    }
    
    return codeBlocks;
  }

  extractLinks(content) {
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const links = [];
    let match;
    
    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        text: match[1],
        url: match[2]
      });
    }
    
    return links;
  }

  checkIfOutdated(content, fileName) {
    const outdatedIndicators = [
      /version\s*[<>]=?\s*\d+\.\d+/i,
      /deprecated/i,
      /todo.*fix/i,
      /temporary/i,
      /old.*implementation/i
    ];
    
    const hasOutdatedContent = outdatedIndicators.some(regex => regex.test(content));
    const isOldTaskFile = fileName.includes('Tasks_') && fileName.includes('2025-07-14');
    
    return hasOutdatedContent || isOldTaskFile;
  }

  generateContentHash(content) {
    // Simple hash function for duplicate detection
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  identifyDuplicates() {
    const contentHashes = {};
    const similarContent = {};
    
    this.results.files.forEach(file => {
      // Check for exact duplicates
      if (contentHashes[file.contentHash]) {
        this.results.duplicates.push({
          type: 'exact',
          files: [contentHashes[file.contentHash], file.fileName],
          reason: 'Identical content hash'
        });
      } else {
        contentHashes[file.contentHash] = file.fileName;
      }
      
      // Check for similar titles/topics
      const titleWords = file.summary.title.toLowerCase().split(/\s+/);
      titleWords.forEach(word => {
        if (word.length > 3) { // Ignore short words
          if (!similarContent[word]) similarContent[word] = [];
          similarContent[word].push(file.fileName);
        }
      });
    });
    
    // Find files with similar topics
    Object.entries(similarContent).forEach(([word, files]) => {
      if (files.length > 1) {
        this.results.duplicates.push({
          type: 'similar',
          files,
          reason: `Similar topic: "${word}"`
        });
      }
    });
  }

  identifyOutdated() {
    this.results.outdated = this.results.files
      .filter(file => file.isOutdated)
      .map(file => ({
        fileName: file.fileName,
        reasons: this.getOutdatedReasons(file),
        lastModified: file.lastModified
      }));
  }

  getOutdatedReasons(file) {
    const reasons = [];
    const content = fs.readFileSync(file.filePath, 'utf8');
    
    if (/deprecated/i.test(content)) reasons.push('Contains deprecated content');
    if (/todo.*fix/i.test(content)) reasons.push('Contains TODO fixes');
    if (/temporary/i.test(content)) reasons.push('Marked as temporary');
    if (file.fileName.includes('Tasks_2025-07-14')) reasons.push('Old task file');
    
    return reasons;
  }

  generateMigrationPlan() {
    // Based on the design document categories
    const migrationMap = {
      'API_GUIDE.md': {
        sources: this.results.categories['API'] || [],
        description: 'Consolidated API documentation including endpoints, testing, and architecture'
      },
      'MQTT_GUIDE.md': {
        sources: this.results.categories['MQTT'] || [],
        description: 'Complete MQTT integration and development guide'
      },
      'COMPONENTS_GUIDE.md': {
        sources: this.results.categories['COMPONENTS'] || [],
        description: 'UI components development and styling guide'
      },
      'TROUBLESHOOTING.md': {
        sources: this.results.categories['FIXES'] || [],
        description: 'Consolidated troubleshooting and bug fix documentation'
      },
      'PERFORMANCE_OPTIMIZATION.md': {
        sources: this.results.categories['PERFORMANCE'] || [],
        description: 'Performance analysis and optimization guide'
      },
      'ARCHITECTURE.md': {
        sources: this.results.categories['ARCHITECTURE'] || [],
        description: 'System architecture and design documentation'
      },
      'RELEASE_NOTES.md': {
        sources: this.results.categories['RELEASE'] || [],
        description: 'Consolidated release notes and version history'
      },
      'archive/': {
        sources: this.results.categories['TASKS'] || [],
        description: 'Archived task files and outdated documentation'
      }
    };
    
    this.results.migrationPlan = Object.entries(migrationMap)
      .filter(([target, config]) => config.sources.length > 0)
      .map(([target, config]) => ({
        target,
        sources: config.sources,
        description: config.description,
        action: target.includes('archive') ? 'archive' : 'merge'
      }));
  }

  generateReport() {
    const reportPath = path.join(process.cwd(), 'docs-analysis-report.md');
    const report = this.buildReport();
    
    fs.writeFileSync(reportPath, report);
    console.log(`📊 Report generated: ${reportPath}`);
    
    // Also generate JSON data for programmatic use
    const jsonPath = path.join(process.cwd(), 'docs-analysis-data.json');
    fs.writeFileSync(jsonPath, JSON.stringify(this.results, null, 2));
    console.log(`📋 Data exported: ${jsonPath}`);
  }

  buildReport() {
    const totalFiles = this.results.files.length;
    const totalSize = this.results.files.reduce((acc, file) => acc + file.size, 0);
    
    return `# 文档分析报告

**生成时间**: ${new Date().toISOString()}
**分析文件数**: ${totalFiles}
**总文档大小**: ${(totalSize / 1024).toFixed(2)} KB

## 📊 文档统计

### 按类别分布
${Object.entries(this.results.categories)
  .map(([category, files]) => `- **${category}**: ${files.length} 个文件`)
  .join('\n')}

### 文件大小分布
${this.results.files
  .sort((a, b) => b.size - a.size)
  .slice(0, 10)
  .map(file => `- ${file.fileName}: ${(file.size / 1024).toFixed(2)} KB`)
  .join('\n')}

## 📋 详细文件分析

${this.results.files.map(file => `
### ${file.fileName}
- **类别**: ${file.categories.join(', ') || '未分类'}
- **大小**: ${(file.size / 1024).toFixed(2)} KB (${file.lines} 行)
- **标题**: ${file.summary.title}
- **描述**: ${file.summary.description}
- **最后修改**: ${file.lastModified.toISOString().split('T')[0]}
- **主要章节**: ${file.headings.map(h => h.text).slice(0, 3).join(', ')}
${file.isOutdated ? '- **⚠️ 可能过时**' : ''}
`).join('\n')}

## 🔄 重复内容分析

${this.results.duplicates.length === 0 ? '未发现重复内容。' : 
  this.results.duplicates.map(dup => `
### ${dup.type === 'exact' ? '完全重复' : '相似内容'}
- **文件**: ${dup.files.join(', ')}
- **原因**: ${dup.reason}
`).join('\n')}

## ⚠️ 过时文档识别

${this.results.outdated.length === 0 ? '未发现明显过时的文档。' : 
  this.results.outdated.map(doc => `
### ${doc.fileName}
- **原因**: ${doc.reasons.join(', ')}
- **最后修改**: ${doc.lastModified.toISOString().split('T')[0]}
`).join('\n')}

## 📦 文档迁移计划

${this.results.migrationPlan.map(plan => `
### ${plan.target}
- **操作**: ${plan.action === 'merge' ? '合并' : '归档'}
- **源文件** (${plan.sources.length}): ${plan.sources.join(', ')}
- **说明**: ${plan.description}
`).join('\n')}

## 🎯 建议行动

### 立即行动
1. **合并 API 文档**: ${this.results.categories['API']?.length || 0} 个文件需要整合
2. **整理 MQTT 文档**: ${this.results.categories['MQTT']?.length || 0} 个文件需要合并
3. **归档过时文档**: ${this.results.outdated.length} 个文件需要处理

### 优先级排序
1. **高优先级**: API 和 MQTT 文档（使用频率高）
2. **中优先级**: 组件和性能文档（开发相关）
3. **低优先级**: 发布说明和任务文档（可归档）

### 质量改进
- 标准化文档模板
- 添加文档元数据（版本、维护者、更新日期）
- 建立文档更新流程
- 实施自动化链接检查

---
*此报告由文档分析脚本自动生成*
`;
  }
}

// Run the analysis
if (require.main === module) {
  const analyzer = new DocumentAnalyzer();
  analyzer.analyzeDocuments().catch(console.error);
}

module.exports = DocumentAnalyzer;