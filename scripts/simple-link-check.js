#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`\n📄 Checking ${filePath}:`);
  
  lines.forEach((line, index) => {
    // Check for App.ts references (not App.tsx)
    if (line.includes('nexus-panel/src/App.ts') && !line.includes('App.tsx')) {
      console.log(`❌ Line ${index + 1}: ${line.trim()}`);
    }
    
    // Check for AudioPlayer.ts references (not AudioPlayer.tsx)
    if (line.includes('nexus-panel/src/components/AudioPlayer.ts') && !line.includes('AudioPlayer.tsx')) {
      console.log(`❌ Line ${index + 1}: ${line.trim()}`);
    }
  });
}

// Check the problematic files
const filesToCheck = [
  'docs/API_DUPLICATE_REQUESTS_ANALYSIS.md',
  'docs/HOOK_FIXES_SUMMARY.md',
  'docs/ICON_UPDATE_SUMMARY.md',
  'docs/PLAYER_DATA_AUTO_FETCH.md',
  'docs/RACE_API_SINGLETON_ARCHITECTURE.md'
];

filesToCheck.forEach(checkFile);