#!/usr/bin/env node

/**
 * 综合文档系统测试脚本
 * 验证文档系统的完整性和可用性
 */

const fs = require('fs');
const path = require('path');

class DocumentationTester {
  constructor() {
    this.results = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      issues: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  addIssue(category, file, issue, severity = 'medium') {
    this.results.issues.push({
      category,
      file,
      issue,
      severity,
      timestamp: new Date().toISOString()
    });
  }

  runTest(testName, testFn) {
    this.results.totalTests++;
    try {
      const result = testFn();
      if (result) {
        this.results.passedTests++;
        this.log(`${testName}: PASSED`, 'success');
        return true;
      } else {
        this.results.failedTests++;
        this.log(`${testName}: FAILED`, 'error');
        return false;
      }
    } catch (error) {
      this.results.failedTests++;
      this.log(`${testName}: ERROR - ${error.message}`, 'error');
      return false;
    }
  }

  // 测试文档结构完整性
  testDocumentStructure() {
    this.log('🏗️ Testing documentation structure...');
    
    const requiredDocs = [
      'docs/README.md',
      'docs/API_GUIDE.md',
      'docs/COMPONENTS_GUIDE.md',
      'docs/DEVELOPMENT_GUIDE.md',
      'docs/ARCHITECTURE.md',
      'docs/TROUBLESHOOTING.md'
    ];

    let allExist = true;
    for (const doc of requiredDocs) {
      if (!fs.existsSync(doc)) {
        this.addIssue('structure', doc, 'Required documentation file missing', 'high');
        allExist = false;
      }
    }

    return this.runTest('Documentation Structure', () => allExist);
  }

  // 测试文档内容质量
  testDocumentContent() {
    this.log('📝 Testing documentation content quality...');
    
    const docsDir = 'docs';
    const files = fs.readdirSync(docsDir)
      .filter(f => f.endsWith('.md') && f !== 'RELEASE_NOTES.md')
      .map(f => path.join(docsDir, f));

    let allValid = true;

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查文档长度
      if (content.length < 100) {
        this.addIssue('content', file, 'Document too short (< 100 characters)', 'medium');
        allValid = false;
      }

      // 检查是否有标题
      if (!content.match(/^#\s+.+/m)) {
        this.addIssue('content', file, 'Missing main heading', 'medium');
        allValid = false;
      }

      // 检查是否有目录结构
      const hasTableOfContents = content.includes('## 目录') || 
                                content.includes('## Table of Contents') ||
                                content.includes('## 概述');
      
      if (!hasTableOfContents && content.length > 2000) {
        this.addIssue('content', file, 'Long document missing table of contents', 'low');
      }
    }

    return this.runTest('Document Content Quality', () => allValid);
  }

  // 测试链接有效性
  testLinkValidity() {
    this.log('🔗 Testing link validity...');
    
    const docsDir = 'docs';
    const files = this.getAllMarkdownFiles(docsDir);
    let allLinksValid = true;

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const links = this.extractLinks(content);

      for (const link of links) {
        if (this.isInternalLink(link.url)) {
          const isValid = this.validateInternalLink(file, link.url);
          if (!isValid) {
            this.addIssue('links', file, `Broken internal link: ${link.url}`, 'medium');
            allLinksValid = false;
          }
        }
      }
    }

    return this.runTest('Link Validity', () => allLinksValid);
  }

  // 测试代码引用有效性
  testCodeReferences() {
    this.log('💻 Testing code references...');
    
    const docsDir = 'docs';
    const files = this.getAllMarkdownFiles(docsDir);
    let allRefsValid = true;

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const codeRefs = this.extractCodeReferences(content);

      for (const ref of codeRefs) {
        const isValid = this.validateCodeReference(ref);
        if (!isValid) {
          this.addIssue('code-refs', file, `Invalid code reference: ${ref}`, 'medium');
          allRefsValid = false;
        }
      }
    }

    return this.runTest('Code References', () => allRefsValid);
  }

  // 测试新开发者体验
  testNewDeveloperExperience() {
    this.log('👨‍💻 Testing new developer experience...');
    
    const essentialFiles = [
      'docs/README.md',
      'docs/DEVELOPMENT_GUIDE.md',
      'nexus-panel/README.md'
    ];

    let goodExperience = true;

    for (const file of essentialFiles) {
      if (!fs.existsSync(file)) {
        this.addIssue('developer-experience', file, 'Essential file missing for new developers', 'high');
        goodExperience = false;
        continue;
      }

      const content = fs.readFileSync(file, 'utf8');
      
      // 检查是否包含快速开始信息
      const hasQuickStart = content.includes('快速开始') || 
                           content.includes('Quick Start') ||
                           content.includes('Getting Started') ||
                           content.includes('开始使用');

      if (!hasQuickStart && file.includes('README.md')) {
        this.addIssue('developer-experience', file, 'Missing quick start section', 'medium');
      }

      // 检查是否包含安装说明
      const hasInstallation = content.includes('安装') || 
                             content.includes('Installation') ||
                             content.includes('npm install');

      if (!hasInstallation && file.includes('README.md')) {
        this.addIssue('developer-experience', file, 'Missing installation instructions', 'medium');
      }
    }

    return this.runTest('New Developer Experience', () => goodExperience);
  }

  // 辅助方法
  getAllMarkdownFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
        files.push(...this.getAllMarkdownFiles(fullPath));
      } else if (item.endsWith('.md')) {
        files.push(fullPath);
      }
    }

    return files;
  }

  extractLinks(content) {
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const links = [];
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        text: match[1],
        url: match[2]
      });
    }

    return links;
  }

  isInternalLink(url) {
    return url.startsWith('./') || 
           url.startsWith('../') || 
           (url.endsWith('.md') && !url.startsWith('http')) ||
           url.startsWith('#');
  }

  validateInternalLink(sourceFile, linkUrl) {
    if (linkUrl.startsWith('#')) {
      // 锚点链接，需要检查同文件内的标题
      return true; // 简化处理，实际应该检查标题是否存在
    }

    const sourceDir = path.dirname(sourceFile);
    let targetPath;

    if (linkUrl.startsWith('./')) {
      targetPath = path.resolve(sourceDir, linkUrl.substring(2).split('#')[0]);
    } else if (linkUrl.startsWith('../')) {
      targetPath = path.resolve(sourceDir, linkUrl.split('#')[0]);
    } else {
      targetPath = path.resolve(sourceDir, linkUrl.split('#')[0]);
    }

    return fs.existsSync(targetPath);
  }

  extractCodeReferences(content) {
    const codeRefRegex = /(?:src\/|nexus-panel\/src\/|\.\/)([a-zA-Z0-9_\-\/]+\.(tsx|jsx|css|ts|js))\b/g;
    const refs = [];
    let match;

    while ((match = codeRefRegex.exec(content)) !== null) {
      // Skip generic examples and comments
      const line = content.split('\n').find(l => l.includes(match[0]));
      if (line && (line.includes('// ❌') || line.includes('示例') || line.includes('可选'))) {
        continue;
      }
      refs.push(match[0]);
    }

    return refs;
  }

  validateCodeReference(ref) {
    // Handle JSON files that might be misidentified as JS
    if (ref.endsWith('.js')) {
      const jsonRef = ref.replace('.js', '.json');
      if (fs.existsSync(jsonRef)) {
        return true;
      }
    }

    const possiblePaths = [
      ref,
      path.join('nexus-panel', ref),
      path.join('nexus-panel', 'src', ref.replace(/^src\//, ''))
    ];

    return possiblePaths.some(p => fs.existsSync(p));
  }

  // 生成测试报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.results.totalTests,
        passedTests: this.results.passedTests,
        failedTests: this.results.failedTests,
        successRate: Math.round((this.results.passedTests / this.results.totalTests) * 100)
      },
      issues: this.results.issues,
      recommendations: this.generateRecommendations()
    };

    // 保存详细报告
    fs.writeFileSync('docs-usability-test-results.json', JSON.stringify(report, null, 2));
    
    // 生成可读报告
    this.generateReadableReport(report);

    return report;
  }

  generateRecommendations() {
    const recommendations = [];
    const issuesByCategory = {};

    // 按类别分组问题
    for (const issue of this.results.issues) {
      if (!issuesByCategory[issue.category]) {
        issuesByCategory[issue.category] = [];
      }
      issuesByCategory[issue.category].push(issue);
    }

    // 生成建议
    if (issuesByCategory['structure']) {
      recommendations.push('补充缺失的核心文档文件');
    }
    if (issuesByCategory['links']) {
      recommendations.push('修复损坏的内部链接');
    }
    if (issuesByCategory['code-refs']) {
      recommendations.push('更新无效的代码文件引用');
    }
    if (issuesByCategory['developer-experience']) {
      recommendations.push('改善新开发者入门体验');
    }

    return recommendations;
  }

  generateReadableReport(report) {
    const content = `# 文档系统可用性测试报告

**执行时间**: ${report.timestamp}
**测试通过率**: ${report.summary.successRate}%

## 📊 测试概览

- 总测试数: ${report.summary.totalTests}
- 通过测试: ${report.summary.passedTests}
- 失败测试: ${report.summary.failedTests}

## 🔍 发现的问题

${report.issues.map(issue => 
  `### ${issue.category.toUpperCase()}: ${issue.file}
- **问题**: ${issue.issue}
- **严重程度**: ${issue.severity}
- **时间**: ${issue.timestamp}
`).join('\n')}

## 💡 改进建议

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

## 🎯 下一步行动

1. 优先处理高严重程度问题
2. 修复损坏的链接和代码引用
3. 改善文档结构和内容质量
4. 定期运行此测试套件

---
*此报告由综合文档测试脚本自动生成*
`;

    fs.writeFileSync('docs-usability-test-report.md', content);
  }

  // 运行所有测试
  async runAllTests() {
    this.log('🚀 Starting comprehensive documentation testing...');
    
    this.testDocumentStructure();
    this.testDocumentContent();
    this.testLinkValidity();
    this.testCodeReferences();
    this.testNewDeveloperExperience();

    const report = this.generateReport();
    
    this.log(`📊 Testing completed. Success rate: ${report.summary.successRate}%`);
    this.log(`📄 Detailed report saved to: docs-usability-test-report.md`);
    
    return report.summary.successRate === 100;
  }
}

// 运行测试
if (require.main === module) {
  const tester = new DocumentationTester();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = DocumentationTester;