#!/usr/bin/env node

/**
 * Documentation Automated Testing Suite
 * Orchestrates all documentation validation and maintenance tools
 * Part of the docs-optimization spec implementation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class DocumentationTestSuite {
  constructor() {
    this.scriptsDir = path.join(__dirname);
    this.results = {
      validation: null,
      duplicateCheck: null,
      linkCheck: null,
      codeRefCheck: null,
      overall: 'pending'
    };
    this.startTime = Date.now();
  }

  /**
   * Run the complete test suite
   */
  async runSuite() {
    console.log('🚀 Starting Documentation Test Suite...\n');
    console.log('='.repeat(60));
    
    try {
      // Run all tests in sequence
      await this.runValidationTests();
      await this.runDuplicateContentCheck();
      await this.runLinkValidation();
      await this.runCodeReferenceCheck();
      
      // Generate comprehensive report
      this.generateSummaryReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      this.results.overall = 'failed';
      process.exit(1);
    }
  }

  /**
   * Run document validation tests
   */
  async runValidationTests() {
    console.log('📋 Running document validation tests...');
    
    try {
      const output = execSync('node scripts/validate-docs.js', { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..')
      });
      
      this.results.validation = {
        status: 'passed',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Document validation passed\n');
      
    } catch (error) {
      this.results.validation = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
        timestamp: new Date().toISOString()
      };
      
      console.log('❌ Document validation failed');
      console.log(error.stdout || error.message);
      console.log();
    }
  }

  /**
   * Run duplicate content detection
   */
  async runDuplicateContentCheck() {
    console.log('🔍 Running duplicate content detection...');
    
    try {
      const output = execSync('node scripts/check-duplicate-content.js', { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..')
      });
      
      this.results.duplicateCheck = {
        status: 'completed',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Duplicate content check completed\n');
      
    } catch (error) {
      this.results.duplicateCheck = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
        timestamp: new Date().toISOString()
      };
      
      console.log('❌ Duplicate content check failed');
      console.log(error.stdout || error.message);
      console.log();
    }
  }

  /**
   * Run link validation
   */
  async runLinkValidation() {
    console.log('🔗 Running link validation...');
    
    try {
      // Use a focused link checking approach
      const linkCheckScript = this.createLinkCheckScript();
      const output = execSync(`node -e "${linkCheckScript}"`, { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..')
      });
      
      this.results.linkCheck = {
        status: 'passed',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Link validation passed\n');
      
    } catch (error) {
      this.results.linkCheck = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
        timestamp: new Date().toISOString()
      };
      
      console.log('❌ Link validation failed');
      console.log(error.stdout || error.message);
      console.log();
    }
  }

  /**
   * Run code reference validation
   */
  async runCodeReferenceCheck() {
    console.log('💻 Running code reference validation...');
    
    try {
      const codeRefScript = this.createCodeRefCheckScript();
      const output = execSync(`node -e "${codeRefScript}"`, { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..')
      });
      
      this.results.codeRefCheck = {
        status: 'passed',
        output: output,
        timestamp: new Date().toISOString()
      };
      
      console.log('✅ Code reference validation passed\n');
      
    } catch (error) {
      this.results.codeRefCheck = {
        status: 'failed',
        error: error.message,
        output: error.stdout || '',
        timestamp: new Date().toISOString()
      };
      
      console.log('❌ Code reference validation failed');
      console.log(error.stdout || error.message);
      console.log();
    }
  }

  /**
   * Create inline link checking script
   */
  createLinkCheckScript() {
    return `
      const fs = require('fs');
      const path = require('path');
      
      const docsDir = path.join(process.cwd(), 'docs');
      const files = fs.readdirSync(docsDir).filter(f => f.endsWith('.md'));
      let brokenLinks = 0;
      let totalLinks = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(docsDir, file), 'utf8');
        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;
        let match;
        
        while ((match = linkRegex.exec(content)) !== null) {
          totalLinks++;
          const linkUrl = match[2];
          
          // Check internal links
          if (linkUrl.startsWith('./') || linkUrl.startsWith('../') || 
              (linkUrl.endsWith('.md') && !linkUrl.startsWith('http'))) {
            
            let targetPath;
            if (linkUrl.startsWith('./')) {
              targetPath = path.join(docsDir, linkUrl.substring(2));
            } else if (linkUrl.startsWith('../')) {
              targetPath = path.resolve(docsDir, linkUrl);
            } else {
              targetPath = path.join(docsDir, linkUrl);
            }
            
            if (!fs.existsSync(targetPath)) {
              console.log('Broken link in ' + file + ': ' + linkUrl);
              brokenLinks++;
            }
          }
        }
      }
      
      console.log('Link validation summary: ' + totalLinks + ' total links, ' + brokenLinks + ' broken');
      if (brokenLinks > 0) process.exit(1);
    `;
  }

  /**
   * Create inline code reference checking script
   */
  createCodeRefCheckScript() {
    return `
      const fs = require('fs');
      const path = require('path');
      
      const docsDir = path.join(process.cwd(), 'docs');
      const projectRoot = process.cwd();
      const files = fs.readdirSync(docsDir).filter(f => f.endsWith('.md'));
      let brokenRefs = 0;
      let totalRefs = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(docsDir, file), 'utf8');
        
        // Check file references
        const fileRefRegex = /(?:src\\/|nexus-panel\\/src\\/|\\.\\/)([a-zA-Z0-9_\\-\\/]+\\.(tsx?|jsx?|css|ts|js))/g;
        let match;
        
        while ((match = fileRefRegex.exec(content)) !== null) {
          totalRefs++;
          const referencedFile = match[0];
          
          const possiblePaths = [
            path.join(projectRoot, referencedFile),
            path.join(projectRoot, 'nexus-panel', referencedFile),
            path.join(projectRoot, 'nexus-panel', 'src', referencedFile.replace(/^src\\//, ''))
          ];
          
          let fileExists = false;
          for (const possiblePath of possiblePaths) {
            if (fs.existsSync(possiblePath)) {
              fileExists = true;
              break;
            }
          }
          
          if (!fileExists) {
            console.log('Referenced file not found in ' + file + ': ' + referencedFile);
            brokenRefs++;
          }
        }
      }
      
      console.log('Code reference summary: ' + totalRefs + ' total references, ' + brokenRefs + ' broken');
      if (brokenRefs > 0) process.exit(1);
    `;
  }

  /**
   * Generate comprehensive summary report
   */
  generateSummaryReport() {
    const duration = Date.now() - this.startTime;
    const reportPath = path.join(process.cwd(), 'docs-test-results.md');
    
    // Determine overall status
    const hasFailures = Object.values(this.results).some(result => 
      result && (result.status === 'failed' || result.status === 'error')
    );
    
    this.results.overall = hasFailures ? 'failed' : 'passed';
    
    const report = this.buildSummaryReport(duration);
    fs.writeFileSync(reportPath, report);
    
    // Also save JSON results
    const jsonPath = path.join(process.cwd(), 'docs-test-results.json');
    fs.writeFileSync(jsonPath, JSON.stringify(this.results, null, 2));
    
    console.log('='.repeat(60));
    console.log('📊 Test Suite Summary:');
    console.log(`   Duration: ${(duration / 1000).toFixed(2)}s`);
    console.log(`   Overall Status: ${this.results.overall.toUpperCase()}`);
    console.log(`   Report: ${reportPath}`);
    console.log(`   Data: ${jsonPath}`);
    
    if (this.results.overall === 'failed') {
      console.log('\n❌ Some tests failed. Please review the detailed report.');
      process.exit(1);
    } else {
      console.log('\n✅ All documentation tests passed!');
    }
  }

  /**
   * Build summary report
   */
  buildSummaryReport(duration) {
    return `# 文档测试套件报告

**执行时间**: ${new Date().toISOString()}
**总耗时**: ${(duration / 1000).toFixed(2)} 秒
**整体状态**: ${this.results.overall === 'passed' ? '✅ 通过' : '❌ 失败'}

## 📋 测试结果概览

| 测试项目 | 状态 | 说明 |
|---------|------|------|
| 文档验证 | ${this.getStatusIcon(this.results.validation?.status)} | 文档结构、元数据、格式验证 |
| 重复内容检测 | ${this.getStatusIcon(this.results.duplicateCheck?.status)} | 检测重复和相似内容 |
| 链接验证 | ${this.getStatusIcon(this.results.linkCheck?.status)} | 验证内部链接有效性 |
| 代码引用检查 | ${this.getStatusIcon(this.results.codeRefCheck?.status)} | 检查代码文件引用 |

## 📊 详细结果

### 文档验证测试
**状态**: ${this.results.validation?.status || 'unknown'}
**时间**: ${this.results.validation?.timestamp || 'N/A'}

${this.results.validation?.status === 'failed' ? 
  `**错误信息**:
\`\`\`
${this.results.validation.error || 'Unknown error'}
\`\`\`` : 
  '✅ 所有文档验证检查通过'}

### 重复内容检测
**状态**: ${this.results.duplicateCheck?.status || 'unknown'}
**时间**: ${this.results.duplicateCheck?.timestamp || 'N/A'}

${this.results.duplicateCheck?.status === 'failed' ? 
  `**错误信息**:
\`\`\`
${this.results.duplicateCheck.error || 'Unknown error'}
\`\`\`` : 
  '✅ 重复内容检测完成，详见 duplicate-content-report.md'}

### 链接验证测试
**状态**: ${this.results.linkCheck?.status || 'unknown'}
**时间**: ${this.results.linkCheck?.timestamp || 'N/A'}

${this.results.linkCheck?.status === 'failed' ? 
  `**错误信息**:
\`\`\`
${this.results.linkCheck.error || 'Unknown error'}
\`\`\`` : 
  '✅ 所有内部链接验证通过'}

### 代码引用检查
**状态**: ${this.results.codeRefCheck?.status || 'unknown'}
**时间**: ${this.results.codeRefCheck?.timestamp || 'N/A'}

${this.results.codeRefCheck?.status === 'failed' ? 
  `**错误信息**:
\`\`\`
${this.results.codeRefCheck.error || 'Unknown error'}
\`\`\`` : 
  '✅ 所有代码引用检查通过'}

## 🎯 建议行动

${this.results.overall === 'passed' ? 
  `### ✅ 文档质量良好
- 所有自动化检查都通过了
- 文档结构规范，链接有效
- 可以继续进行文档优化的下一步工作` :
  `### ⚠️ 需要处理的问题
${this.generateActionItems()}`}

## 📈 质量指标

- **文档结构合规性**: ${this.results.validation?.status === 'passed' ? '100%' : '需要改进'}
- **链接有效性**: ${this.results.linkCheck?.status === 'passed' ? '100%' : '需要修复'}
- **代码引用准确性**: ${this.results.codeRefCheck?.status === 'passed' ? '100%' : '需要更新'}
- **内容重复度**: 详见重复内容检测报告

## 🔄 持续改进

### 自动化流程
- 建议将此测试套件集成到 CI/CD 流程中
- 在文档更新时自动运行验证
- 定期执行重复内容检测

### 质量标准
- 保持文档元数据的完整性
- 及时更新失效的链接和代码引用
- 定期清理重复和过时内容

---
*此报告由文档测试套件自动生成*
`;
  }

  /**
   * Get status icon for display
   */
  getStatusIcon(status) {
    switch (status) {
      case 'passed':
      case 'completed':
        return '✅ 通过';
      case 'failed':
      case 'error':
        return '❌ 失败';
      default:
        return '⏳ 未知';
    }
  }

  /**
   * Generate action items based on failures
   */
  generateActionItems() {
    const items = [];
    
    if (this.results.validation?.status === 'failed') {
      items.push('- 修复文档验证错误（检查文档结构和元数据）');
    }
    
    if (this.results.linkCheck?.status === 'failed') {
      items.push('- 修复损坏的内部链接');
    }
    
    if (this.results.codeRefCheck?.status === 'failed') {
      items.push('- 更新无效的代码文件引用');
    }
    
    if (this.results.duplicateCheck?.status === 'failed') {
      items.push('- 处理重复内容检测中发现的问题');
    }
    
    return items.join('\n');
  }
}

// Run test suite if called directly
if (require.main === module) {
  const testSuite = new DocumentationTestSuite();
  testSuite.runSuite().catch(console.error);
}

module.exports = DocumentationTestSuite;