#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files to fix and their corrections
const fixes = [
  {
    file: 'docs/API_DUPLICATE_REQUESTS_ANALYSIS.md',
    replacements: [
      { from: 'nexus-panel/src/App.ts', to: 'nexus-panel/src/App.tsx' }
    ]
  },
  {
    file: 'docs/HOOK_FIXES_SUMMARY.md', 
    replacements: [
      { from: 'nexus-panel/src/App.ts', to: 'nexus-panel/src/App.tsx' }
    ]
  },
  {
    file: 'docs/PLAYER_DATA_AUTO_FETCH.md',
    replacements: [
      { from: 'nexus-panel/src/App.ts', to: 'nexus-panel/src/App.tsx' }
    ]
  },
  {
    file: 'docs/RACE_API_SINGLETON_ARCHITECTURE.md',
    replacements: [
      { from: 'nexus-panel/src/App.ts', to: 'nexus-panel/src/App.tsx' }
    ]
  },
  {
    file: 'docs/ICON_UPDATE_SUMMARY.md',
    replacements: [
      { from: 'nexus-panel/src/components/AudioPlayer.ts', to: 'nexus-panel/src/components/AudioPlayer.tsx' }
    ]
  }
];

function fixFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  for (const replacement of replacements) {
    if (content.includes(replacement.from)) {
      content = content.replace(new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement.to);
      hasChanges = true;
      console.log(`✅ Fixed in ${filePath}: ${replacement.from} → ${replacement.to}`);
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔧 Fixing broken code references...\n');
  
  let totalFixed = 0;
  
  for (const fix of fixes) {
    if (fixFile(fix.file, fix.replacements)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✨ Fixed ${totalFixed} files.`);
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };