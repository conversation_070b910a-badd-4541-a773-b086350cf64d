#!/usr/bin/env node

/**
 * 文档维护调度器
 * 用于设置和管理文档的定期审查机制
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const CONFIG = {
  docsDir: path.join(__dirname, '..', 'docs'),
  reportsDir: path.join(__dirname, '..', 'docs-maintenance-reports'),
  scheduleFile: path.join(__dirname, '..', 'docs-maintenance-schedule.json'),
  
  // 审查周期配置
  reviewCycles: {
    daily: {
      name: '日常审查',
      frequency: 'daily',
      checks: ['links', 'code-refs', 'format'],
      automated: true
    },
    weekly: {
      name: '周度审查',
      frequency: 'weekly',
      day: 'friday',
      checks: ['quality', 'feedback', 'urgent-fixes'],
      automated: false
    },
    monthly: {
      name: '月度审查',
      frequency: 'monthly',
      day: 1,
      checks: ['comprehensive-quality', 'usage-analysis', 'process-optimization'],
      automated: false
    },
    quarterly: {
      name: '季度审查',
      frequency: 'quarterly',
      month: [1, 4, 7, 10],
      day: 1,
      checks: ['architecture-review', 'user-satisfaction', 'strategic-planning'],
      automated: false
    }
  }
};

class DocumentMaintenanceScheduler {
  constructor() {
    this.ensureDirectories();
    this.loadSchedule();
  }

  ensureDirectories() {
    if (!fs.existsSync(CONFIG.reportsDir)) {
      fs.mkdirSync(CONFIG.reportsDir, { recursive: true });
    }
  }

  loadSchedule() {
    if (fs.existsSync(CONFIG.scheduleFile)) {
      this.schedule = JSON.parse(fs.readFileSync(CONFIG.scheduleFile, 'utf8'));
    } else {
      this.schedule = {
        lastRun: {},
        nextRun: {},
        history: []
      };
      this.saveSchedule();
    }
  }

  saveSchedule() {
    fs.writeFileSync(CONFIG.scheduleFile, JSON.stringify(this.schedule, null, 2));
  }

  // 运行日常自动化检查
  runDailyCheck() {
    console.log('🔍 开始日常文档检查...');
    
    const results = {
      timestamp: new Date().toISOString(),
      type: 'daily',
      checks: {}
    };

    try {
      // 链接检查
      console.log('  📎 检查文档链接...');
      results.checks.links = this.checkLinks();
      
      // 代码引用检查
      console.log('  🔗 检查代码引用...');
      results.checks.codeRefs = this.checkCodeReferences();
      
      // 格式检查
      console.log('  📝 检查文档格式...');
      results.checks.format = this.checkFormat();
      
      // 生成报告
      this.generateDailyReport(results);
      
      // 更新调度信息
      this.schedule.lastRun.daily = new Date().toISOString();
      this.schedule.nextRun.daily = this.getNextRunTime('daily');
      this.saveSchedule();
      
      console.log('✅ 日常检查完成');
      return results;
      
    } catch (error) {
      console.error('❌ 日常检查失败:', error.message);
      return { error: error.message };
    }
  }

  // 检查文档链接
  checkLinks() {
    try {
      const result = execSync('npm run docs:check-links', { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..', 'nexus-panel')
      });
      return { status: 'success', output: result };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  // 检查代码引用
  checkCodeReferences() {
    try {
      const result = execSync('npm run docs:verify-code-refs', { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..', 'nexus-panel')
      });
      return { status: 'success', output: result };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  // 检查文档格式
  checkFormat() {
    try {
      const result = execSync('npm run docs:lint', { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..', 'nexus-panel')
      });
      return { status: 'success', output: result };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }

  // 生成日常报告
  generateDailyReport(results) {
    const reportDate = new Date().toISOString().split('T')[0];
    const reportFile = path.join(CONFIG.reportsDir, `daily-${reportDate}.json`);
    
    fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
    
    // 生成简要的 Markdown 报告
    const markdownReport = this.generateDailyMarkdownReport(results);
    const markdownFile = path.join(CONFIG.reportsDir, `daily-${reportDate}.md`);
    fs.writeFileSync(markdownFile, markdownReport);
  }

  generateDailyMarkdownReport(results) {
    const date = new Date(results.timestamp).toLocaleDateString('zh-CN');
    
    return `# 日常文档检查报告

**检查日期：** ${date}
**检查时间：** ${results.timestamp}

## 检查结果摘要

| 检查项目 | 状态 | 说明 |
|---------|------|------|
| 链接检查 | ${results.checks.links?.status === 'success' ? '✅ 通过' : '❌ 失败'} | ${results.checks.links?.status === 'success' ? '所有链接有效' : '存在无效链接'} |
| 代码引用检查 | ${results.checks.codeRefs?.status === 'success' ? '✅ 通过' : '❌ 失败'} | ${results.checks.codeRefs?.status === 'success' ? '代码引用正确' : '存在无效引用'} |
| 格式检查 | ${results.checks.format?.status === 'success' ? '✅ 通过' : '❌ 失败'} | ${results.checks.format?.status === 'success' ? '格式规范' : '格式需要调整'} |

## 详细结果

### 链接检查
${results.checks.links?.status === 'success' ? 
  '所有文档链接检查通过。' : 
  `发现问题：\n\`\`\`\n${results.checks.links?.error || ''}\n\`\`\``}

### 代码引用检查
${results.checks.codeRefs?.status === 'success' ? 
  '所有代码引用检查通过。' : 
  `发现问题：\n\`\`\`\n${results.checks.codeRefs?.error || ''}\n\`\`\``}

### 格式检查
${results.checks.format?.status === 'success' ? 
  '文档格式检查通过。' : 
  `发现问题：\n\`\`\`\n${results.checks.format?.error || ''}\n\`\`\``}

## 建议行动

${this.generateActionItems(results)}

---
*此报告由文档维护调度器自动生成*
`;
  }

  generateActionItems(results) {
    const actions = [];
    
    if (results.checks.links?.status !== 'success') {
      actions.push('- 🔗 修复无效链接');
    }
    
    if (results.checks.codeRefs?.status !== 'success') {
      actions.push('- 📝 更新代码引用');
    }
    
    if (results.checks.format?.status !== 'success') {
      actions.push('- 🎨 修复格式问题');
    }
    
    if (actions.length === 0) {
      return '无需特别行动，文档状态良好。';
    }
    
    return actions.join('\n');
  }

  // 生成周度审查提醒
  generateWeeklyReminder() {
    const reminderContent = `# 周度文档审查提醒

**提醒时间：** ${new Date().toLocaleString('zh-CN')}
**负责人：** 当周值班开发者

## 本周审查任务

### ✅ 必须完成的任务
- [ ] 检查本周新增/修改的文档质量
- [ ] 处理用户反馈和问题报告
- [ ] 修复紧急文档问题
- [ ] 更新文档状态记录

### 📋 检查清单
- [ ] 运行自动化检查工具
- [ ] 审查新增文档的质量
- [ ] 检查用户反馈渠道
- [ ] 更新问题跟踪列表
- [ ] 准备周度状态报告

### 🔧 可用工具
\`\`\`bash
# 运行完整检查
npm run docs:full-check

# 生成质量报告
npm run docs:generate-quality-report

# 检查用户反馈
npm run docs:check-feedback
\`\`\`

### 📊 报告模板
请使用以下模板记录本周审查结果：

**审查日期：** ${new Date().toLocaleDateString('zh-CN')}
**审查人员：** [姓名]

**新增文档：**
- 文档1：质量评级 [A/B/C/D]
- 文档2：质量评级 [A/B/C/D]

**修改文档：**
- 文档1：修改内容和质量影响
- 文档2：修改内容和质量影响

**用户反馈处理：**
- 反馈1：处理状态和结果
- 反馈2：处理状态和结果

**发现问题：**
- 问题1：严重程度和处理计划
- 问题2：严重程度和处理计划

**改进建议：**
- 建议1
- 建议2

---
*请在周五下午完成审查并提交报告*
`;

    const reminderFile = path.join(CONFIG.reportsDir, `weekly-reminder-${new Date().toISOString().split('T')[0]}.md`);
    fs.writeFileSync(reminderFile, reminderContent);
    
    console.log(`📅 周度审查提醒已生成: ${reminderFile}`);
  }

  // 生成月度审查计划
  generateMonthlyPlan() {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    const planContent = `# 月度文档审查计划

**审查月份：** ${currentYear}年${currentMonth}月
**计划生成时间：** ${currentDate.toLocaleString('zh-CN')}
**负责小组：** 文档维护小组

## 审查目标

### 🎯 主要目标
- 全面评估文档质量状态
- 分析文档使用情况和用户反馈
- 优化文档维护流程
- 制定下月改进计划

### 📊 关键指标
- 文档质量平均分：目标 > 85分
- 用户满意度：目标 > 4.0/5.0
- 问题解决率：目标 > 90%
- 文档更新及时性：目标 < 3天

## 审查范围

### 📚 重点审查文档
- [ ] API_GUIDE.md - 核心API文档
- [ ] COMPONENTS_GUIDE.md - 组件开发指南
- [ ] MQTT_GUIDE.md - MQTT集成指南
- [ ] TROUBLESHOOTING.md - 故障排除指南
- [ ] ARCHITECTURE.md - 系统架构文档
- [ ] DEVELOPMENT_GUIDE.md - 开发指南

### 🔍 审查维度
- [ ] 准确性：信息是否准确和最新
- [ ] 完整性：内容是否全面和深入
- [ ] 可用性：用户体验是否良好
- [ ] 维护性：是否便于维护和更新

## 审查计划

### 第1周：准备和数据收集
- [ ] 运行自动化质量检查
- [ ] 收集用户反馈和使用数据
- [ ] 分析文档访问统计
- [ ] 准备审查工具和模板

### 第2周：详细质量评估
- [ ] 逐一评估重点文档
- [ ] 记录问题和改进机会
- [ ] 进行用户体验测试
- [ ] 收集团队成员意见

### 第3周：问题分析和方案制定
- [ ] 分析发现的问题
- [ ] 制定改进方案
- [ ] 评估改进成本和收益
- [ ] 确定优先级和时间表

### 第4周：报告和计划
- [ ] 编写月度质量报告
- [ ] 制定下月改进计划
- [ ] 召开审查总结会议
- [ ] 更新维护流程

## 工具和资源

### 🛠️ 检查工具
\`\`\`bash
# 完整质量检查
npm run docs:comprehensive-check

# 用户反馈分析
npm run docs:analyze-feedback

# 使用情况统计
npm run docs:usage-stats

# 质量报告生成
npm run docs:monthly-report
\`\`\`

### 📋 评估模板
- [文档质量评估表](../templates/DOCUMENT_QUALITY_STANDARDS.md)
- [用户体验测试清单](../templates/DOCUMENTATION_UPDATE_CHECKLIST.md)
- [改进计划模板](../DOCUMENTATION_MAINTENANCE_GUIDE.md#改进计划模板)

## 成功标准

### ✅ 审查完成标准
- [ ] 所有重点文档都已评估
- [ ] 质量问题都已记录和分类
- [ ] 改进方案都已制定
- [ ] 月度报告已完成
- [ ] 下月计划已确定

### 📈 质量改进目标
- 文档平均质量分提升 5分
- 用户反馈问题减少 20%
- 文档更新响应时间缩短 30%
- 团队文档维护效率提升 15%

## 风险和应对

### ⚠️ 潜在风险
- **时间不足**：审查任务过多，时间安排紧张
  - 应对：提前规划，分配任务，必要时延长审查周期
- **资源冲突**：团队成员忙于其他项目
  - 应对：提前协调，调整审查范围，寻求额外支持
- **质量标准不一致**：不同审查者标准不同
  - 应对：统一培训，使用标准化评估工具

### 🔄 应急预案
- 如果发现严重质量问题，立即启动紧急修复流程
- 如果用户反馈集中在某个问题，优先处理该问题
- 如果审查进度滞后，调整审查范围和深度

---
*此计划将根据实际情况动态调整*
`;

    const planFile = path.join(CONFIG.reportsDir, `monthly-plan-${currentYear}-${currentMonth.toString().padStart(2, '0')}.md`);
    fs.writeFileSync(planFile, planContent);
    
    console.log(`📋 月度审查计划已生成: ${planFile}`);
  }

  // 获取下次运行时间
  getNextRunTime(cycle) {
    const now = new Date();
    
    switch (cycle) {
      case 'daily':
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(9, 0, 0, 0); // 每天上午9点
        return tomorrow.toISOString();
        
      case 'weekly':
        const nextFriday = new Date(now);
        const daysUntilFriday = (5 - now.getDay() + 7) % 7 || 7;
        nextFriday.setDate(now.getDate() + daysUntilFriday);
        nextFriday.setHours(14, 0, 0, 0); // 周五下午2点
        return nextFriday.toISOString();
        
      case 'monthly':
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        nextMonth.setHours(9, 0, 0, 0); // 每月1号上午9点
        return nextMonth.toISOString();
        
      case 'quarterly':
        const currentQuarter = Math.floor(now.getMonth() / 3);
        const nextQuarterMonth = (currentQuarter + 1) * 3;
        const nextQuarter = new Date(now.getFullYear(), nextQuarterMonth, 1);
        if (nextQuarterMonth >= 12) {
          nextQuarter.setFullYear(now.getFullYear() + 1);
          nextQuarter.setMonth(0);
        }
        nextQuarter.setHours(9, 0, 0, 0);
        return nextQuarter.toISOString();
        
      default:
        return null;
    }
  }

  // 检查是否需要运行审查
  checkSchedule() {
    const now = new Date();
    const results = [];
    
    Object.keys(CONFIG.reviewCycles).forEach(cycle => {
      const config = CONFIG.reviewCycles[cycle];
      const nextRun = this.schedule.nextRun[cycle];
      
      if (!nextRun || new Date(nextRun) <= now) {
        results.push({
          cycle,
          config,
          shouldRun: true,
          overdue: nextRun ? (now - new Date(nextRun)) / (1000 * 60 * 60 * 24) : 0
        });
      }
    });
    
    return results;
  }

  // 显示状态
  showStatus() {
    console.log('\n📊 文档维护调度状态\n');
    
    Object.keys(CONFIG.reviewCycles).forEach(cycle => {
      const config = CONFIG.reviewCycles[cycle];
      const lastRun = this.schedule.lastRun[cycle];
      const nextRun = this.schedule.nextRun[cycle];
      
      console.log(`${config.name} (${cycle}):`);
      console.log(`  上次运行: ${lastRun ? new Date(lastRun).toLocaleString('zh-CN') : '从未运行'}`);
      console.log(`  下次运行: ${nextRun ? new Date(nextRun).toLocaleString('zh-CN') : '未安排'}`);
      console.log(`  自动化: ${config.automated ? '是' : '否'}`);
      console.log('');
    });
    
    const pending = this.checkSchedule();
    if (pending.length > 0) {
      console.log('⏰ 待执行的审查:');
      pending.forEach(item => {
        console.log(`  - ${item.config.name}: ${item.overdue > 0 ? `逾期 ${Math.floor(item.overdue)} 天` : '需要执行'}`);
      });
    } else {
      console.log('✅ 所有审查都按计划进行');
    }
  }

  // 初始化调度
  initializeSchedule() {
    console.log('🚀 初始化文档维护调度...');
    
    // 设置所有审查的下次运行时间
    Object.keys(CONFIG.reviewCycles).forEach(cycle => {
      this.schedule.nextRun[cycle] = this.getNextRunTime(cycle);
    });
    
    this.saveSchedule();
    
    // 生成初始计划文档
    this.generateWeeklyReminder();
    this.generateMonthlyPlan();
    
    console.log('✅ 调度初始化完成');
    this.showStatus();
  }
}

// 命令行接口
function main() {
  const scheduler = new DocumentMaintenanceScheduler();
  const command = process.argv[2];
  
  switch (command) {
    case 'init':
      scheduler.initializeSchedule();
      break;
      
    case 'daily':
      scheduler.runDailyCheck();
      break;
      
    case 'weekly-reminder':
      scheduler.generateWeeklyReminder();
      break;
      
    case 'monthly-plan':
      scheduler.generateMonthlyPlan();
      break;
      
    case 'status':
      scheduler.showStatus();
      break;
      
    case 'check':
      const pending = scheduler.checkSchedule();
      if (pending.length > 0) {
        console.log('需要执行的审查:');
        pending.forEach(item => {
          console.log(`- ${item.config.name}`);
        });
        process.exit(1);
      } else {
        console.log('所有审查都按计划进行');
        process.exit(0);
      }
      break;
      
    default:
      console.log(`
文档维护调度器

用法:
  node docs-maintenance-scheduler.js <command>

命令:
  init              初始化调度系统
  daily             运行日常检查
  weekly-reminder   生成周度审查提醒
  monthly-plan      生成月度审查计划
  status            显示调度状态
  check             检查待执行的审查

示例:
  node docs-maintenance-scheduler.js init
  node docs-maintenance-scheduler.js daily
  node docs-maintenance-scheduler.js status
`);
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = DocumentMaintenanceScheduler;