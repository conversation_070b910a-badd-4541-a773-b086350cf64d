#!/usr/bin/env node

/**
 * Duplicate Content Detection Script
 * Advanced duplicate and similar content detection for documentation
 * Part of the docs-optimization spec implementation
 */

const fs = require('fs');
const path = require('path');

class DuplicateContentDetector {
  constructor() {
    this.docsDir = path.join(__dirname, '..', 'docs');
    this.results = {
      exactDuplicates: [],
      similarContent: [],
      duplicateHeadings: [],
      duplicateCodeBlocks: []
    };
  }

  /**
   * Main detection function
   */
  async detect() {
    console.log('🔍 Starting duplicate content detection...\n');
    
    try {
      const files = await this.getMarkdownFiles();
      const fileContents = await this.loadFileContents(files);
      
      await this.detectExactDuplicates(fileContents);
      await this.detectSimilarContent(fileContents);
      await this.detectDuplicateHeadings(fileContents);
      await this.detectDuplicateCodeBlocks(fileContents);
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Detection failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Get all markdown files
   */
  async getMarkdownFiles() {
    const files = fs.readdirSync(this.docsDir)
      .filter(file => file.endsWith('.md'))
      .map(file => path.join(this.docsDir, file));
    
    console.log(`📁 Found ${files.length} markdown files to analyze`);
    return files;
  }

  /**
   * Load file contents with metadata
   */
  async loadFileContents(files) {
    const contents = [];
    
    for (const filePath of files) {
      const fileName = path.basename(filePath);
      const content = fs.readFileSync(filePath, 'utf8');
      
      contents.push({
        fileName,
        filePath,
        content,
        lines: content.split('\n'),
        wordCount: content.split(/\s+/).length,
        size: content.length,
        contentHash: this.generateHash(content),
        normalizedContent: this.normalizeContent(content),
        headings: this.extractHeadings(content),
        codeBlocks: this.extractCodeBlocks(content),
        paragraphs: this.extractParagraphs(content)
      });
    }
    
    return contents;
  }

  /**
   * Detect exact duplicate files
   */
  async detectExactDuplicates(fileContents) {
    console.log('🔍 Detecting exact duplicates...');
    
    const hashMap = new Map();
    
    for (const file of fileContents) {
      if (hashMap.has(file.contentHash)) {
        this.results.exactDuplicates.push({
          files: [hashMap.get(file.contentHash), file.fileName],
          reason: 'Identical content',
          similarity: 100
        });
      } else {
        hashMap.set(file.contentHash, file.fileName);
      }
    }
    
    console.log(`   Found ${this.results.exactDuplicates.length} exact duplicates`);
  }

  /**
   * Detect similar content using various algorithms
   */
  async detectSimilarContent(fileContents) {
    console.log('🔍 Detecting similar content...');
    
    for (let i = 0; i < fileContents.length; i++) {
      for (let j = i + 1; j < fileContents.length; j++) {
        const file1 = fileContents[i];
        const file2 = fileContents[j];
        
        // Skip if already found as exact duplicate
        if (this.results.exactDuplicates.some(dup => 
          dup.files.includes(file1.fileName) && dup.files.includes(file2.fileName))) {
          continue;
        }
        
        // Calculate similarity using multiple methods
        const similarities = {
          jaccard: this.calculateJaccardSimilarity(file1.normalizedContent, file2.normalizedContent),
          cosine: this.calculateCosineSimilarity(file1.normalizedContent, file2.normalizedContent),
          levenshtein: this.calculateLevenshteinSimilarity(file1.normalizedContent, file2.normalizedContent)
        };
        
        const avgSimilarity = (similarities.jaccard + similarities.cosine + similarities.levenshtein) / 3;
        
        // Threshold for similar content (70% similarity)
        if (avgSimilarity > 0.7) {
          this.results.similarContent.push({
            files: [file1.fileName, file2.fileName],
            similarities,
            avgSimilarity: Math.round(avgSimilarity * 100),
            reason: this.determineSimilarityReason(file1, file2, similarities)
          });
        }
      }
    }
    
    console.log(`   Found ${this.results.similarContent.length} similar content pairs`);
  }

  /**
   * Detect duplicate headings across files
   */
  async detectDuplicateHeadings(fileContents) {
    console.log('🔍 Detecting duplicate headings...');
    
    const headingMap = new Map();
    
    for (const file of fileContents) {
      for (const heading of file.headings) {
        const normalizedHeading = heading.text.toLowerCase().trim();
        
        if (!headingMap.has(normalizedHeading)) {
          headingMap.set(normalizedHeading, []);
        }
        
        headingMap.get(normalizedHeading).push({
          file: file.fileName,
          level: heading.level,
          text: heading.text
        });
      }
    }
    
    // Find headings that appear in multiple files
    for (const [heading, occurrences] of headingMap.entries()) {
      if (occurrences.length > 1) {
        this.results.duplicateHeadings.push({
          heading,
          occurrences,
          count: occurrences.length
        });
      }
    }
    
    console.log(`   Found ${this.results.duplicateHeadings.length} duplicate headings`);
  }

  /**
   * Detect duplicate code blocks
   */
  async detectDuplicateCodeBlocks(fileContents) {
    console.log('🔍 Detecting duplicate code blocks...');
    
    const codeMap = new Map();
    
    for (const file of fileContents) {
      for (const codeBlock of file.codeBlocks) {
        const normalizedCode = codeBlock.code.trim().replace(/\s+/g, ' ');
        const codeHash = this.generateHash(normalizedCode);
        
        if (!codeMap.has(codeHash)) {
          codeMap.set(codeHash, []);
        }
        
        codeMap.get(codeHash).push({
          file: file.fileName,
          language: codeBlock.language,
          code: codeBlock.code.substring(0, 100) + '...' // Preview
        });
      }
    }
    
    // Find code blocks that appear in multiple files
    for (const [hash, occurrences] of codeMap.entries()) {
      if (occurrences.length > 1) {
        this.results.duplicateCodeBlocks.push({
          hash,
          occurrences,
          count: occurrences.length
        });
      }
    }
    
    console.log(`   Found ${this.results.duplicateCodeBlocks.length} duplicate code blocks`);
  }

  /**
   * Generate content hash
   */
  generateHash(content) {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(16);
  }

  /**
   * Normalize content for comparison
   */
  normalizeContent(content) {
    return content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Extract headings from content
   */
  extractHeadings(content) {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings = [];
    let match;
    
    while ((match = headingRegex.exec(content)) !== null) {
      headings.push({
        level: match[1].length,
        text: match[2]
      });
    }
    
    return headings;
  }

  /**
   * Extract code blocks from content
   */
  extractCodeBlocks(content) {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const codeBlocks = [];
    let match;
    
    while ((match = codeBlockRegex.exec(content)) !== null) {
      codeBlocks.push({
        language: match[1] || 'unknown',
        code: match[2]
      });
    }
    
    return codeBlocks;
  }

  /**
   * Extract paragraphs from content
   */
  extractParagraphs(content) {
    return content
      .split('\n\n')
      .filter(para => para.trim() && !para.startsWith('#') && !para.startsWith('```'))
      .map(para => para.trim());
  }

  /**
   * Calculate Jaccard similarity
   */
  calculateJaccardSimilarity(text1, text2) {
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * Calculate Cosine similarity
   */
  calculateCosineSimilarity(text1, text2) {
    const words1 = text1.split(/\s+/);
    const words2 = text2.split(/\s+/);
    
    const wordFreq1 = this.getWordFrequency(words1);
    const wordFreq2 = this.getWordFrequency(words2);
    
    const allWords = new Set([...words1, ...words2]);
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (const word of allWords) {
      const freq1 = wordFreq1[word] || 0;
      const freq2 = wordFreq2[word] || 0;
      
      dotProduct += freq1 * freq2;
      norm1 += freq1 * freq1;
      norm2 += freq2 * freq2;
    }
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * Calculate Levenshtein similarity
   */
  calculateLevenshteinSimilarity(text1, text2) {
    const distance = this.levenshteinDistance(text1, text2);
    const maxLength = Math.max(text1.length, text2.length);
    return 1 - (distance / maxLength);
  }

  /**
   * Calculate Levenshtein distance
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Get word frequency map
   */
  getWordFrequency(words) {
    const freq = {};
    for (const word of words) {
      freq[word] = (freq[word] || 0) + 1;
    }
    return freq;
  }

  /**
   * Determine similarity reason
   */
  determineSimilarityReason(file1, file2, similarities) {
    const reasons = [];
    
    if (similarities.jaccard > 0.8) reasons.push('High word overlap');
    if (similarities.cosine > 0.8) reasons.push('Similar word patterns');
    if (similarities.levenshtein > 0.8) reasons.push('Similar text structure');
    
    // Check for similar file names
    const name1 = file1.fileName.toLowerCase();
    const name2 = file2.fileName.toLowerCase();
    if (name1.includes(name2.split('.')[0]) || name2.includes(name1.split('.')[0])) {
      reasons.push('Similar file names');
    }
    
    return reasons.join(', ') || 'General content similarity';
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    const reportPath = path.join(process.cwd(), 'duplicate-content-report.md');
    const report = this.buildReport();
    
    fs.writeFileSync(reportPath, report);
    console.log(`\n📊 Duplicate content report generated: ${reportPath}`);
    
    // Also generate JSON data
    const jsonPath = path.join(process.cwd(), 'duplicate-content-data.json');
    fs.writeFileSync(jsonPath, JSON.stringify(this.results, null, 2));
    console.log(`📋 Data exported: ${jsonPath}`);
    
    this.printSummary();
  }

  /**
   * Build markdown report
   */
  buildReport() {
    return `# 重复内容检测报告

**生成时间**: ${new Date().toISOString()}

## 📊 检测摘要

- **完全重复**: ${this.results.exactDuplicates.length} 对
- **相似内容**: ${this.results.similarContent.length} 对
- **重复标题**: ${this.results.duplicateHeadings.length} 个
- **重复代码块**: ${this.results.duplicateCodeBlocks.length} 个

## 🔍 完全重复文件

${this.results.exactDuplicates.length === 0 ? '未发现完全重复的文件。' :
  this.results.exactDuplicates.map(dup => `
### ${dup.files.join(' ↔ ')}
- **相似度**: ${dup.similarity}%
- **原因**: ${dup.reason}
- **建议**: 保留一个文件，删除或归档另一个
`).join('\n')}

## 📝 相似内容文件

${this.results.similarContent.length === 0 ? '未发现高度相似的内容。' :
  this.results.similarContent.map(sim => `
### ${sim.files.join(' ↔ ')}
- **平均相似度**: ${sim.avgSimilarity}%
- **Jaccard 相似度**: ${Math.round(sim.similarities.jaccard * 100)}%
- **余弦相似度**: ${Math.round(sim.similarities.cosine * 100)}%
- **编辑距离相似度**: ${Math.round(sim.similarities.levenshtein * 100)}%
- **原因**: ${sim.reason}
- **建议**: 考虑合并相似内容或明确区分用途
`).join('\n')}

## 📑 重复标题

${this.results.duplicateHeadings.length === 0 ? '未发现重复标题。' :
  this.results.duplicateHeadings.slice(0, 10).map(heading => `
### "${heading.heading}"
- **出现次数**: ${heading.count}
- **文件**: ${heading.occurrences.map(occ => `${occ.file} (H${occ.level})`).join(', ')}
`).join('\n')}

${this.results.duplicateHeadings.length > 10 ? `\n*显示前 10 个，总共 ${this.results.duplicateHeadings.length} 个重复标题*` : ''}

## 💻 重复代码块

${this.results.duplicateCodeBlocks.length === 0 ? '未发现重复代码块。' :
  this.results.duplicateCodeBlocks.slice(0, 5).map(code => `
### 代码块 ${code.hash}
- **出现次数**: ${code.count}
- **文件**: ${code.occurrences.map(occ => `${occ.file} (${occ.language})`).join(', ')}
- **预览**: \`${code.occurrences[0].code}\`
`).join('\n')}

${this.results.duplicateCodeBlocks.length > 5 ? `\n*显示前 5 个，总共 ${this.results.duplicateCodeBlocks.length} 个重复代码块*` : ''}

## 🎯 建议行动

### 立即处理
${this.results.exactDuplicates.length > 0 ? 
  `1. **删除完全重复文件**: ${this.results.exactDuplicates.length} 对文件需要处理` : 
  '1. ✅ 无完全重复文件需要处理'}

### 考虑合并
${this.results.similarContent.filter(s => s.avgSimilarity > 85).length > 0 ?
  `2. **合并高度相似内容**: ${this.results.similarContent.filter(s => s.avgSimilarity > 85).length} 对文件相似度超过 85%` :
  '2. ✅ 无高度相似内容需要合并'}

### 标准化处理
${this.results.duplicateHeadings.length > 0 ?
  `3. **统一重复标题**: ${this.results.duplicateHeadings.length} 个标题在多个文件中重复` :
  '3. ✅ 标题使用规范'}

### 代码复用优化
${this.results.duplicateCodeBlocks.length > 0 ?
  `4. **优化重复代码**: ${this.results.duplicateCodeBlocks.length} 个代码块重复使用` :
  '4. ✅ 代码示例无重复'}

---
*此报告由重复内容检测脚本自动生成*
`;
  }

  /**
   * Print summary to console
   */
  printSummary() {
    console.log('\n📊 检测结果摘要:');
    console.log('='.repeat(50));
    console.log(`完全重复文件: ${this.results.exactDuplicates.length} 对`);
    console.log(`相似内容文件: ${this.results.similarContent.length} 对`);
    console.log(`重复标题: ${this.results.duplicateHeadings.length} 个`);
    console.log(`重复代码块: ${this.results.duplicateCodeBlocks.length} 个`);
    
    if (this.results.exactDuplicates.length > 0 || 
        this.results.similarContent.filter(s => s.avgSimilarity > 85).length > 0) {
      console.log('\n⚠️  发现需要处理的重复内容，请查看详细报告。');
    } else {
      console.log('\n✅ 文档内容重复度在可接受范围内。');
    }
  }
}

// Run detection if called directly
if (require.main === module) {
  const detector = new DuplicateContentDetector();
  detector.detect().catch(console.error);
}

module.exports = DuplicateContentDetector;