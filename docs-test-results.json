{"validation": {"status": "failed", "error": "Command failed: node scripts/validate-docs.js", "output": "🔍 Validating all documentation links and references...\n\n✅ docs/API_GUIDE.md\n✅ docs/ARCHITECTURE.md\n✅ docs/COMPONENTS_GUIDE.md\n✅ docs/DEVELOPMENT_GUIDE.md\n✅ docs/DOCUMENTATION_MAINTENANCE_GUIDE.md\n✅ docs/MQTT_GUIDE.md\n✅ docs/PERFORMANCE_OPTIMIZATION.md\n✅ docs/README.md\n✅ docs/RELEASE_NOTES.md\n✅ docs/STRUCTURE_OVERVIEW.md\n✅ docs/TROUBLESHOOTING.md\n✅ docs/templates/API_GUIDE_TEMPLATE.md\n✅ docs/templates/COMPONENT_GUIDE_TEMPLATE.md\n✅ docs/templates/DOCUMENTATION_UPDATE_CHECKLIST.md\n❌ docs/templates/DOCUMENT_QUALITY_STANDARDS.md:\n   Line 449: Broken link to DOCUMENTATION_UPDATE_CHECKLIST.md\n\n✅ docs/templates/STANDARD_TEMPLATE.md\n✅ nexus-panel/README.md\n\n📊 Validation Summary:\n   Files checked: 17\n   Files with issues: 1\n   Total issues: 1\n\n⚠️  Found 1 issues that need to be fixed.\n", "timestamp": "2025-07-16T06:05:03.447Z"}, "duplicateCheck": {"status": "completed", "output": "🔍 Starting duplicate content detection...\n\n📁 Found 11 markdown files to analyze\n🔍 Detecting exact duplicates...\n   Found 0 exact duplicates\n🔍 Detecting similar content...\n   Found 0 similar content pairs\n🔍 Detecting duplicate headings...\n   Found 37 duplicate headings\n🔍 Detecting duplicate code blocks...\n   Found 1 duplicate code blocks\n\n📊 Duplicate content report generated: /Users/<USER>/nexus-panel/duplicate-content-report.md\n📋 Data exported: /Users/<USER>/nexus-panel/duplicate-content-data.json\n\n📊 检测结果摘要:\n==================================================\n完全重复文件: 0 对\n相似内容文件: 0 对\n重复标题: 37 个\n重复代码块: 1 个\n\n✅ 文档内容重复度在可接受范围内。\n", "timestamp": "2025-07-16T06:06:13.155Z"}, "linkCheck": {"status": "failed", "error": "Command failed: node -e \"\n      const fs = require('fs');\n      const path = require('path');\n      \n      const docsDir = path.join(process.cwd(), 'docs');\n      const files = fs.readdirSync(docsDir).filter(f => f.endsWith('.md'));\n      let brokenLinks = 0;\n      let totalLinks = 0;\n      \n      for (const file of files) {\n        const content = fs.readFileSync(path.join(docsDir, file), 'utf8');\n        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n        let match;\n        \n        while ((match = linkRegex.exec(content)) !== null) {\n          totalLinks++;\n          const linkUrl = match[2];\n          \n          // Check internal links\n          if (linkUrl.startsWith('./') || linkUrl.startsWith('../') || \n              (linkUrl.endsWith('.md') && !linkUrl.startsWith('http'))) {\n            \n            let targetPath;\n            if (linkUrl.startsWith('./')) {\n              targetPath = path.join(docsDir, linkUrl.substring(2));\n            } else if (linkUrl.startsWith('../')) {\n              targetPath = path.resolve(docsDir, linkUrl);\n            } else {\n              targetPath = path.join(docsDir, linkUrl);\n            }\n            \n            if (!fs.existsSync(targetPath)) {\n              console.log('Broken link in ' + file + ': ' + linkUrl);\n              brokenLinks++;\n            }\n          }\n        }\n      }\n      \n      console.log('Link validation summary: ' + totalLinks + ' total links, ' + brokenLinks + ' broken');\n      if (brokenLinks > 0) process.exit(1);\n    \"", "output": "Broken link in STRUCTURE_OVERVIEW.md: ./DOCUMENTATION_MAINTENANCE_GUIDE.md#📋-内容质量检查清单\nLink validation summary: 138 total links, 1 broken\n", "timestamp": "2025-07-16T06:06:13.197Z"}, "codeRefCheck": {"status": "failed", "error": "Command failed: node -e \"\n      const fs = require('fs');\n      const path = require('path');\n      \n      const docsDir = path.join(process.cwd(), 'docs');\n      const projectRoot = process.cwd();\n      const files = fs.readdirSync(docsDir).filter(f => f.endsWith('.md'));\n      let brokenRefs = 0;\n      let totalRefs = 0;\n      \n      for (const file of files) {\n        const content = fs.readFileSync(path.join(docsDir, file), 'utf8');\n        \n        // Check file references\n        const fileRefRegex = /(?:src\\/|nexus-panel\\/src\\/|\\.\\/)([a-zA-Z0-9_\\-\\/]+\\.(tsx?|jsx?|css|ts|js))/g;\n        let match;\n        \n        while ((match = fileRefRegex.exec(content)) !== null) {\n          totalRefs++;\n          const referencedFile = match[0];\n          \n          const possiblePaths = [\n            path.join(projectRoot, referencedFile),\n            path.join(projectRoot, 'nexus-panel', referencedFile),\n            path.join(projectRoot, 'nexus-panel', 'src', referencedFile.replace(/^src\\//, ''))\n          ];\n          \n          let fileExists = false;\n          for (const possiblePath of possiblePaths) {\n            if (fs.existsSync(possiblePath)) {\n              fileExists = true;\n              break;\n            }\n          }\n          \n          if (!fileExists) {\n            console.log('Referenced file not found in ' + file + ': ' + referencedFile);\n            brokenRefs++;\n          }\n        }\n      }\n      \n      console.log('Code reference summary: ' + totalRefs + ' total references, ' + brokenRefs + ' broken');\n      if (brokenRefs > 0) process.exit(1);\n    \"", "output": "Referenced file not found in COMPONENTS_GUIDE.md: src/setupTests.ts\nReferenced file not found in DEVELOPMENT_GUIDE.md: ./MyComponent.tsx\nReferenced file not found in README.md: ./templates/metadata-schema.js\nCode reference summary: 12 total references, 3 broken\n", "timestamp": "2025-07-16T06:06:13.234Z"}, "overall": "failed"}