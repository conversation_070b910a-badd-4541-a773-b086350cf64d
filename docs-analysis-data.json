{"files": [{"fileName": "API_ENDPOINTS_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/API_ENDPOINTS_ANALYSIS.md", "size": 13609, "lines": 764, "wordCount": 1725, "lastModified": "2025-07-09T06:32:58.056Z", "categories": ["API", "MQTT", "COMPONENTS", "FIXES", "PERFORMANCE", "RELEASE", "ARCHITECTURE"], "summary": {"title": "Nexus Panel API 端点全面分析", "description": "本文档提供了 Nexus Panel 项目中所有 API 端点的全面分析，包括 HTTP REST API、MQTT WebSocket API 和内部服务 API 的详细信息。..."}, "headings": [{"level": 1, "text": "Nexus Panel API 端点全面分析"}, {"level": 2, "text": "文档概述"}, {"level": 2, "text": "目录"}, {"level": 2, "text": "API 架构概览"}, {"level": 3, "text": "核心设计原则"}, {"level": 2, "text": "HTTP REST API 端点"}, {"level": 3, "text": "基础配置"}, {"level": 3, "text": "1. 赛事管理 API"}, {"level": 4, "text": "1.1 获取赛事列表"}, {"level": 4, "text": "1.2 健康检查"}, {"level": 3, "text": "2. 导航数据 API"}, {"level": 4, "text": "2.1 获取表结构"}, {"level": 4, "text": "2.2 获取环节数据"}, {"level": 3, "text": "3. 题目数据 API"}, {"level": 4, "text": "3.1 获取题目数据"}, {"level": 3, "text": "4. 配置管理 API"}, {"level": 4, "text": "4.1 获取配置数据"}, {"level": 4, "text": "4.2 获取规则介绍"}, {"level": 2, "text": "MQTT WebSocket API 端点"}, {"level": 3, "text": "基础配置"}, {"level": 3, "text": "Topic 结构规范"}, {"level": 4, "text": "域 (Domain)"}, {"level": 4, "text": "上下文 (Context)"}, {"level": 4, "text": "目标 (Target)"}, {"level": 4, "text": "动作 (Action)"}, {"level": 3, "text": "常用 Topic 示例"}, {"level": 4, "text": "1. 环节控制"}, {"level": 4, "text": "2. 显示控制"}, {"level": 4, "text": "3. 系统控制"}, {"level": 2, "text": "内部服务 API"}, {"level": 3, "text": "1. Hook 层 API"}, {"level": 4, "text": "useRaceApi Hook"}, {"level": 4, "text": "useApi Hook"}, {"level": 3, "text": "2. 服务层 API"}, {"level": 4, "text": "HTTP Client"}, {"level": 4, "text": "MQTT Service"}, {"level": 2, "text": "按功能域分类"}, {"level": 3, "text": "1. 赛事管理域"}, {"level": 3, "text": "2. 导航数据域"}, {"level": 3, "text": "3. 题目数据域"}, {"level": 3, "text": "4. 配置管理域"}, {"level": 3, "text": "5. 实时通信域"}, {"level": 2, "text": "按技术协议分类"}, {"level": 3, "text": "1. HTTP REST API"}, {"level": 3, "text": "2. MQTT WebSocket API"}, {"level": 3, "text": "3. 内部服务 API"}, {"level": 2, "text": "按数据流向分类"}, {"level": 3, "text": "1. 数据获取 (Pull)"}, {"level": 3, "text": "2. 数据推送 (<PERSON><PERSON>)"}, {"level": 3, "text": "3. 状态管理 (State)"}, {"level": 2, "text": "按使用模式分类"}, {"level": 3, "text": "1. 核心业务 API"}, {"level": 3, "text": "2. 辅助功能 API"}, {"level": 3, "text": "3. 系统配置 API"}, {"level": 2, "text": "安全和认证"}, {"level": 3, "text": "1. HTTP API 安全"}, {"level": 3, "text": "2. MQTT API 安全"}, {"level": 3, "text": "3. 内部 API 安全"}, {"level": 2, "text": "API 依赖关系"}, {"level": 3, "text": "依赖关系图"}, {"level": 3, "text": "关键依赖关系"}, {"level": 2, "text": "总结"}, {"level": 2, "text": "附录A: 详细端点清单"}, {"level": 3, "text": "HTTP REST API 详细清单"}, {"level": 4, "text": "1. 赛事管理相关端点"}, {"level": 4, "text": "2. 导航数据相关端点"}, {"level": 4, "text": "3. 题目数据相关端点"}, {"level": 4, "text": "4. 配置管理相关端点"}, {"level": 3, "text": "MQTT Topic 详细清单"}, {"level": 4, "text": "1. 环节控制 Topics"}, {"level": 4, "text": "2. 显示控制 Topics"}, {"level": 4, "text": "3. 系统控制 Topics"}, {"level": 2, "text": "附录B: 数据结构定义"}, {"level": 3, "text": "HTTP API 数据结构"}, {"level": 4, "text": "RaceApiItem"}, {"level": 4, "text": "QuestionApiItem"}, {"level": 4, "text": "SectionDataItem"}, {"level": 3, "text": "MQTT 数据结构"}, {"level": 4, "text": "SessionData"}, {"level": 4, "text": "RankData"}, {"level": 4, "text": "PlayerData"}, {"level": 4, "text": "SystemRefreshData"}, {"level": 2, "text": "附录C: 错误处理和状态码"}, {"level": 3, "text": "HTTP API 错误处理"}, {"level": 4, "text": "常见状态码"}, {"level": 4, "text": "错误响应格式"}, {"level": 3, "text": "MQTT 连接状态"}, {"level": 4, "text": "连接状态枚举"}, {"level": 4, "text": "错误处理策略"}, {"level": 2, "text": "附录D: 性能优化策略"}, {"level": 3, "text": "1. HTTP API 优化"}, {"level": 3, "text": "2. MQTT 优化"}, {"level": 3, "text": "3. 内存管理"}, {"level": 2, "text": "附录E: 开发和调试指南"}, {"level": 3, "text": "1. API 调试工具"}, {"level": 3, "text": "2. 常见问题排查"}, {"level": 3, "text": "3. 监控和日志"}], "codeBlocks": [{"language": "unknown", "code": "┌─────────────────────────────────────────────────────────────┐\n│                    前端应用层 (React)                        │\n├─────────────────────────────────────────────────────────────┤\n│                    Hook 抽象层                              │\n│  useRaceApi | useApi | useMQTTIntegration | useNavigation   │\n├─────────────────────────────────────────────────────────────┤\n│                    服务层 (Services)                        │\n│     HTTP Client    │    MQTT Service    │   Memory Manager  │\n├─────────────────────────────────────────────────────────────┤\n│                    协议层 (Protocols)                       │\n│      HTTP REST     │    MQTT/WebSocket  │   Internal APIs   │\n├─────────────────────────────────────────────────────────────┤\n│                    外部服务层                               │\n│      NocoDB API    │    MQTT Broker     │   File System     │\n└─────────────────────────────────────────────────────────────┘\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/m19dww1xfzsfipk/records?viewId=vwoimmnq6pws8pso&limit=500\n"}, {"language": "typescript", "code": "interface RaceApiResponse {\n  list: RaceApiItem[];\n  pageInfo: {\n    totalRows: number;\n    page: number;\n    pageSize: number;\n    isFirstPage: boolean;\n    isLastPage: boolean;\n  };\n}\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/m19dww1xfzsfipk/records?limit=1\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/meta/bases/{baseId}/tables\n"}, {"language": "typescript", "code": "interface TableStructureResponse {\n  list: TableStructureItem[];\n}\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records?limit=500\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records?limit=500&where=(所属环节,eq,{环节名称})~and(题包编号,eq,{题包编号})\n"}, {"language": "typescript", "code": "interface QuestionApiResponse {\n  list: QuestionApiItem[];\n  pageInfo: PaginationInfo;\n}\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records\n"}, {"language": "http", "code": "GET https://noco.ohvfx.com/api/v2/tables/{tableId}/records\n"}, {"language": "unknown", "code": "{domain}/{context}/{target}/{action}\n"}, {"language": "unknown", "code": "quiz/session/all/start    # 开始环节\nquiz/session/all/stop     # 停止环节\n"}, {"language": "unknown", "code": "display/rank/screen/show     # 显示排行榜\ndisplay/player/screen/show   # 显示选手信息\ndisplay/rule/screen/show     # 显示规则\n"}, {"language": "unknown", "code": "system/client/all/refresh    # 刷新所有客户端\n"}, {"language": "typescript", "code": "interface UseRaceApiReturn {\n  races: ProcessedRaceItem[];\n  loading: boolean;\n  error: ApiError | null;\n  status: ApiStatus;\n  execute: () => Promise<void>;\n  retry: () => Promise<void>;\n}\n"}, {"language": "typescript", "code": "interface UseApiReturn<T> {\n  data: T | null;\n  loading: boolean;\n  error: ApiError | null;\n  status: ApiStatus;\n  execute: () => Promise<void>;\n}\n"}, {"language": "typescript", "code": "class HttpClient {\n  get<T>(\n    endpoint: string,\n    params?: Record<string, any>\n  ): Promise<ApiResponse<T>>;\n  post<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;\n  put<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;\n  delete<T>(endpoint: string): Promise<ApiResponse<T>>;\n  patch<T>(endpoint: string, body?: any): Promise<ApiResponse<T>>;\n}\n"}, {"language": "typescript", "code": "class MQTTService {\n  connect(): Promise<void>;\n  disconnect(): Promise<void>;\n  publish(topic: string, message: any): Promise<void>;\n  subscribe(topic: string, handler: MQTTMessageHandler): void;\n  unsubscribe(topic: string): void;\n}\n"}, {"language": "mermaid", "code": "graph TD\n    A[前端应用] --> B[Hook 层]\n    B --> C[服务层]\n    C --> D[HTTP Client]\n    C --> E[MQTT Service]\n    C --> F[Memory Manager]\n\n    D --> G[NocoDB API]\n    E --> H[MQTT Broker]\n    F --> I[浏览器存储]\n\n    B --> J[useRaceApi]\n    B --> K[useApi]\n    B --> L[useMQTTIntegration]\n\n    J --> M[赛事数据]\n    K --> N[通用数据]\n    L --> O[实时消息]\n"}, {"language": "typescript", "code": "interface RaceApiItem {\n  Id: number;\n  项目名称: string;\n  项目编号: string;\n  项目状态: string;\n  创建时间: string;\n  更新时间: string;\n  基础ID: string;\n  [key: string]: unknown;\n}\n"}, {"language": "typescript", "code": "interface QuestionApiItem {\n  Id: number;\n  题号?: number | null;\n  题型?: string | null;\n  分值?: number | null;\n  题干?: string | null;\n  选项?: string | null;\n  答案?: string | null;\n  解析?: string | null;\n  所属环节: string;\n  题包编号: string;\n  附件?: AttachmentItem[] | null;\n  所属阶段?: string | null;\n  [key: string]: unknown;\n}\n"}, {"language": "typescript", "code": "interface SectionDataItem {\n  Id: number;\n  环节名称: string;\n  显示顺序: number;\n  环节类型: string;\n  环节图标: string;\n  内容类型?: string;\n  初始阶段?: string;\n  [key: string]: unknown;\n}\n"}, {"language": "typescript", "code": "interface SessionData {\n  sessionType: string;\n  sessionId: number;\n  sessionName: string;\n  config: Record<string, unknown>;\n}\n"}, {"language": "typescript", "code": "interface RankData {\n  rankType: 'general' | 'speedrun' | 'speedrun-plus';\n}\n"}, {"language": "typescript", "code": "interface PlayerData {\n  playerId: number;\n  playerName: string;\n  position: number;\n  status: 'active' | 'inactive';\n}\n"}, {"language": "typescript", "code": "interface SystemRefreshData {\n  refreshType: 'full' | 'partial';\n  timestamp: number;\n}\n"}, {"language": "typescript", "code": "interface ApiError {\n  message: string;\n  code?: string | number;\n  status?: number;\n  originalError?: Error;\n}\n"}, {"language": "typescript", "code": "enum MQTTConnectionStatus {\n  DISCONNECTED = 'disconnected',\n  CONNECTING = 'connecting',\n  CONNECTED = 'connected',\n  RECONNECTING = 'reconnecting',\n  ERROR = 'error',\n}\n"}], "links": [{"text": "API 架构概览", "url": "#api-架构概览"}, {"text": "HTTP REST API 端点", "url": "#http-rest-api-端点"}, {"text": "MQTT WebSocket API 端点", "url": "#mqtt-websocket-api-端点"}, {"text": "内部服务 API", "url": "#内部服务-api"}, {"text": "按功能域分类", "url": "#按功能域分类"}, {"text": "按技术协议分类", "url": "#按技术协议分类"}, {"text": "按数据流向分类", "url": "#按数据流向分类"}, {"text": "按使用模式分类", "url": "#按使用模式分类"}, {"text": "安全和认证", "url": "#安全和认证"}, {"text": "API 依赖关系", "url": "#api-依赖关系"}], "isOutdated": false, "contentHash": "-f6451cb"}, {"fileName": "RACE_API_SINGLETON_ARCHITECTURE.md", "filePath": "/Users/<USER>/nexus-panel/docs/RACE_API_SINGLETON_ARCHITECTURE.md", "size": 10529, "lines": 518, "wordCount": 1112, "lastModified": "2025-07-15T12:37:29.344Z", "categories": ["API", "FIXES", "COMPONENTS", "ARCHITECTURE", "MQTT"], "summary": {"title": "Race API 单例架构重构", "description": "**变更时间：** 2025-01-15  ..."}, "headings": [{"level": 1, "text": "Race API 单例架构重构"}, {"level": 2, "text": "🔄 架构变更概述"}, {"level": 2, "text": "📋 变更背景"}, {"level": 3, "text": "问题描述"}, {"level": 3, "text": "具体问题场景"}, {"level": 2, "text": "🏗️ 新架构设计"}, {"level": 3, "text": "单例模式实现"}, {"level": 3, "text": "应用层集成"}, {"level": 2, "text": "🔧 迁移指南"}, {"level": 3, "text": "1. 组件迁移"}, {"level": 3, "text": "2. <PERSON> 迁移"}, {"level": 3, "text": "3. 应用入口配置"}, {"level": 3, "text": "4. 配置选项支持"}, {"level": 2, "text": "📊 架构对比"}, {"level": 3, "text": "迁移前架构"}, {"level": 3, "text": "迁移后架构"}, {"level": 2, "text": "✅ 优势与收益"}, {"level": 3, "text": "1. 性能优化"}, {"level": 3, "text": "2. 数据一致性"}, {"level": 3, "text": "3. 开发体验"}, {"level": 3, "text": "4. 维护性"}, {"level": 2, "text": "🔧 配置选项详解"}, {"level": 3, "text": "可用配置选项"}, {"level": 3, "text": "常见配置场景"}, {"level": 4, "text": "1. 开发环境调试配置"}, {"level": 4, "text": "2. 生产环境优化配置"}, {"level": 4, "text": "3. 测试环境配置"}, {"level": 2, "text": "🔍 使用示例"}, {"level": 3, "text": "基础用法"}, {"level": 3, "text": "高级用法 - 条件渲染"}, {"level": 2, "text": "🚨 注意事项"}, {"level": 3, "text": "1. Provider 配置"}, {"level": 3, "text": "2. 错误处理"}, {"level": 3, "text": "3. 生命周期管理"}, {"level": 2, "text": "🔄 后续优化计划"}, {"level": 3, "text": "短期优化"}, {"level": 3, "text": "长期规划"}, {"level": 2, "text": "📈 监控指标"}, {"level": 3, "text": "性能指标"}, {"level": 3, "text": "质量指标"}], "codeBlocks": [{"language": "typescript", "code": "// 问题：多个组件各自创建 useRaceApi 实例\nfunction App() {\n  const raceApi1 = useRaceApi(); // 实例1\n  // ...\n}\n\nfunction TimeRaceRankingContainer() {\n  const raceApi2 = useRaceApi(); // 实例2 - 重复请求\n  // ...\n}\n\nfunction useTimeRaceRanking() {\n  const raceApi3 = useRaceApi(); // 实例3 - 重复请求\n  // ...\n}\n"}, {"language": "typescript", "code": "/**\n * useRaceApi 单例管理器\n * 确保整个应用只有一个 useRaceApi 实例\n */\n\nimport React, { createContext, useContext, ReactNode } from 'react';\nimport { useRaceApi } from './useRaceApi';\nimport type { UseRaceApiReturn, UseRaceApiOptions } from './types';\n\n// 创建Context\nconst RaceApiContext = createContext<UseRaceApiReturn | null>(null);\n\n/**\n * RaceApi Provider 组件的Props\n */\nexport interface RaceApiProviderProps {\n    children: ReactNode;\n    /** useRaceApi的配置选项 */\n    options?: UseRaceApiOptions;\n}\n\n/**\n * RaceApi Provider 组件\n */\nexport function RaceApiProvider({ children, options = {} }: RaceApiProviderProps) {\n    // 创建单一的 useRaceApi 实例，使用传入的配置选项\n    const raceApiInstance = useRaceApi({\n        immediate: true,\n        onLog: (level, message, details) => {\n            console.log(`[RaceApiSingleton] ${level.toUpperCase()}: ${message}`, details);\n        },\n        ...options // 合并传入的配置选项\n    });\n\n    return (\n        <RaceApiContext.Provider value={raceApiInstance}>\n            {children}\n        </RaceApiContext.Provider>\n    );\n}\n\n/**\n * 使用共享的 RaceApi 实例\n */\nexport function useSharedRaceApi(): UseRaceApiReturn {\n    const context = useContext(RaceApiContext);\n\n    if (!context) {\n        throw new Error('useSharedRaceApi must be used within a RaceApiProvider');\n    }\n\n    return context;\n}\n"}, {"language": "typescript", "code": "// 导入单例模块\nimport { RaceApiProvider, useSharedRaceApi } from \"./hooks/useRaceApi/singleton\";\n\n// 内部组件使用共享实例\nfunction AppContent() {\n    const raceApi = useSharedRaceApi(); // 共享的单一实例\n    // ...\n}\n\n// 主应用组件包装Provider\nexport default function AppWithProvider() {\n    return (\n        <RaceApiProvider>\n            <App />\n        </RaceApiProvider>\n    );\n}\n"}, {"language": "typescript", "code": "import { useRaceApi } from './hooks/useRaceApi';\n\nfunction MyComponent() {\n    const raceApi = useRaceApi(); // 创建新实例\n    // ...\n}\n"}, {"language": "typescript", "code": "import { useSharedRaceApi } from './hooks/useRaceApi/singleton';\n\nfunction MyComponent() {\n    const raceApi = useSharedRaceApi(); // 使用共享实例\n    // ...\n}\n"}, {"language": "typescript", "code": "export function useTimeRaceRanking() {\n    const raceApi = useRaceApi(); // 独立实例\n    // ...\n}\n"}, {"language": "typescript", "code": "import { useSharedRaceApi } from './useRaceApi/singleton';\n\nexport function useTimeRaceRanking() {\n    const raceApi = useSharedRaceApi(); // 共享实例\n    // ...\n}\n"}, {"language": "typescript", "code": "// main.tsx 或 App.tsx\nimport { RaceApiProvider } from './hooks/useRaceApi/singleton';\n\nfunction App() {\n    return (\n        <RaceApiProvider>\n            {/* 应用内容 */}\n        </RaceApiProvider>\n    );\n}\n"}, {"language": "typescript", "code": "import { RaceApiProvider } from './hooks/useRaceApi/singleton';\n\nfunction App() {\n    const raceApiOptions = {\n        immediate: false, // 不立即获取数据\n        onSuccess: (data) => {\n            console.log('数据获取成功:', data);\n        },\n        onError: (error) => {\n            console.error('数据获取失败:', error);\n        },\n        onLog: (level, message, details) => {\n            // 自定义日志处理\n            console.log(`[CustomLog] ${level}: ${message}`, details);\n        }\n    };\n\n    return (\n        <RaceApiProvider options={raceApiOptions}>\n            {/* 应用内容 */}\n        </RaceApiProvider>\n    );\n}\n"}, {"language": "unknown", "code": "┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐\n│   Component A   │    │   Component B   │    │   Component C   │\n│                 │    │                 │    │                 │\n│ useRaceApi() ───┼────┼─ useRaceApi() ──┼────┼─ useRaceApi()   │\n│   (实例1)       │    │   (实例2)       │    │   (实例3)       │\n└─────────────────┘    └─────────────────┘    └─────────────────┘\n        │                       │                       │\n        ▼                       ▼                       ▼\n┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐\n│   API请求1      │    │   API请求2      │    │   API请求3      │\n│   (重复)        │    │   (重复)        │    │   (重复)        │\n└─────────────────┘    └─────────────────┘    └─────────────────┘\n"}, {"language": "unknown", "code": "                    ┌─────────────────────────┐\n                    │    RaceApiProvider      │\n                    │                         │\n                    │  useRaceApi() (单例)    │\n                    └─────────────┬───────────┘\n                                  │\n        ┌─────────────────────────┼─────────────────────────┐\n        │                         │                         │\n        ▼                         ▼                         ▼\n┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐\n│   Component A   │    │   Component B   │    │   Component C   │\n│                 │    │                 │    │                 │\n│useSharedRaceApi()│   │useSharedRaceApi()│   │useSharedRaceApi()│\n│   (共享实例)    │    │   (共享实例)    │    │   (共享实例)    │\n└─────────────────┘    └─────────────────┘    └─────────────────┘\n                                  │\n                                  ▼\n                    ┌─────────────────────────┐\n                    │      单一API请求        │\n                    │      (无重复)          │\n                    └─────────────────────────┘\n"}, {"language": "typescript", "code": "interface UseRaceApiOptions {\n    /** 是否立即获取数据，默认 true */\n    immediate?: boolean;\n    \n    /** 数据获取成功回调 */\n    onSuccess?: (races: ProcessedRaceItem[]) => void;\n    \n    /** 数据获取失败回调 */\n    onError?: (error: ApiError) => void;\n    \n    /** 日志记录回调 */\n    onLog?: LogFunction;\n    \n    /** 当前选中的项目ID */\n    selectedProject?: string;\n    \n    /** 当前选中的导航键 */\n    selectedNavigationKey?: string;\n    \n    /** 题包切换对话框状态 */\n    packageChangeDialog?: PackageChangeDialog;\n    \n    /** 题包切换对话框状态设置函数 */\n    setPackageChangeDialog?: (dialog: PackageChangeDialog) => void;\n}\n"}, {"language": "typescript", "code": "const developmentOptions = {\n    onLog: (level, message, details) => {\n        // 开发环境详细日志\n        console.log(`[RaceAPI-${level.toUpperCase()}] ${message}`, details);\n    },\n    onError: (error) => {\n        // 开发环境错误追踪\n        console.error('RaceAPI Error:', error);\n        // 可以集成错误监控服务\n    }\n};\n\n<RaceApiProvider options={developmentOptions}>\n    <App />\n</RaceApiProvider>\n"}, {"language": "typescript", "code": "const productionOptions = {\n    immediate: true, // 立即加载数据\n    onError: (error) => {\n        // 生产环境错误上报\n        errorReportingService.report(error);\n    },\n    onLog: (level, message, details) => {\n        // 只记录重要日志\n        if (level === 'error' || level === 'warning') {\n            analyticsService.track('race_api_event', { level, message });\n        }\n    }\n};\n"}, {"language": "typescript", "code": "const testOptions = {\n    immediate: false, // 测试时手动控制数据加载\n    onSuccess: (races) => {\n        // 测试断言\n        expect(races).toBeDefined();\n        expect(races.length).toBeGreaterThan(0);\n    }\n};\n"}, {"language": "typescript", "code": "import { useSharedRaceApi } from './hooks/useRaceApi/singleton';\n\nfunction RaceComponent() {\n    const {\n        races,\n        loading,\n        error,\n        navigationData,\n        configurationData,\n        fetchAllProjectData,\n        // ... 其他方法\n    } = useSharedRaceApi();\n\n    useEffect(() => {\n        if (selectedProject) {\n            fetchAllProjectData(selectedProject);\n        }\n    }, [selectedProject, fetchAllProjectData]);\n\n    if (loading) return <div>加载中...</div>;\n    if (error) return <div>错误: {error.message}</div>;\n\n    return (\n        <div>\n            <h2>赛事列表 ({races.length})</h2>\n            {races.map(race => (\n                <div key={race.id}>{race.name}</div>\n            ))}\n        </div>\n    );\n}\n"}, {"language": "typescript", "code": "import { useIsRaceApiProviderAvailable } from './hooks/useRaceApi/singleton';\n\nfunction ConditionalComponent() {\n    const isProviderAvailable = useIsRaceApiProviderAvailable();\n\n    if (!isProviderAvailable) {\n        return <div>RaceApi Provider 未配置</div>;\n    }\n\n    return <RaceComponent />;\n}\n"}, {"language": "typescript", "code": "// ✅ 正确：在应用根部配置\nfunction App() {\n    return (\n        <RaceApiProvider>\n            <Router>\n                <Routes>\n                    {/* 路由配置 */}\n                </Routes>\n            </Router>\n        </RaceApiProvider>\n    );\n}\n\n// ❌ 错误：在子组件中配置\nfunction SubComponent() {\n    return (\n        <RaceApiProvider> {/* 太深层级 */}\n            <SomeComponent />\n        </RaceApiProvider>\n    );\n}\n"}, {"language": "typescript", "code": "function MyComponent() {\n    try {\n        const raceApi = useSharedRaceApi();\n        // 正常使用\n    } catch (error) {\n        // 处理 Provider 未配置的情况\n        console.error('RaceApi Provider 未配置:', error);\n        return <div>配置错误</div>;\n    }\n}\n"}], "links": [], "isOutdated": false, "contentHash": "4436294"}, {"fileName": "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "size": 8507, "lines": 376, "wordCount": 835, "lastModified": "2025-07-15T13:13:22.429Z", "categories": ["API", "COMPONENTS", "FIXES", "ARCHITECTURE", "RELEASE"], "summary": {"title": "TimeRaceRankingContent API 请求流程详细分析", "description": "`TimeRaceRankingContent` 组件采用了**数据-UI 分离**的现代 React 架构模式，通过容器组件和自定义 Hook 管理 API 请求，实现了关注点分离和代码复用。..."}, "headings": [{"level": 1, "text": "TimeRaceRankingContent API 请求流程详细分析"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "架构层次图"}, {"level": 2, "text": "API 请求流程详解"}, {"level": 3, "text": "1. 初始化阶段"}, {"level": 4, "text": "1.1 组件挂载"}, {"level": 4, "text": "1.2 Hook 初始化"}, {"level": 3, "text": "2. 数据获取策略"}, {"level": 4, "text": "2.1 数据复用优先策略"}, {"level": 4, "text": "2.2 API 调用链路"}, {"level": 3, "text": "3. 底层 API 服务实现"}, {"level": 4, "text": "3.1 表结构获取（带缓存优化）"}, {"level": 4, "text": "3.2 数据获取和处理"}, {"level": 4, "text": "3.3 数据处理和排名计算"}, {"level": 3, "text": "4. 实时更新机制"}, {"level": 4, "text": "4.1 计时器联动轮询"}, {"level": 4, "text": "4.2 轮询实现"}, {"level": 3, "text": "5. 性能优化策略"}, {"level": 4, "text": "5.1 请求去重"}, {"level": 4, "text": "5.2 数据缓存"}, {"level": 4, "text": "5.3 防抖处理"}, {"level": 2, "text": "数据流向图"}, {"level": 2, "text": "错误处理机制"}, {"level": 3, "text": "1. 分层错误处理"}, {"level": 3, "text": "2. 错误恢复策略"}, {"level": 2, "text": "关键特性总结"}], "codeBlocks": [{"language": "unknown", "code": "App.tsx (主应用)\n    ↓ 使用 useRaceApi Hook\n    ↓ 管理全局状态和 API 调用\n    ↓\nTimeRaceRankingContainer (容器组件)\n    ↓ 使用 useTimeRaceRanking Hook\n    ↓ 管理争分夺秒排名数据和状态\n    ↓ 通过 props 传递数据\n    ↓\nTimeRaceRankingContent (展示组件)\n    ↓ 纯 UI 渲染，不直接调用 API\n    ↓ 接收数据并展示排名表格\n"}, {"language": "typescript", "code": "// TimeRaceRankingContainer.tsx\nexport const TimeRaceRankingContainer: React.FC<Props> = ({\n  sectionName,    // '争分夺秒' | '同分加赛'\n  baseId,         // 项目基础ID\n  pollingInterval = 5000,\n  pageSize = 8,\n  // ...其他props\n}) => {\n  // 使用数据管理Hook\n  const {\n    rankingData,\n    loading,\n    error,\n    isPolling,\n    // ...其他状态\n  } = useTimeRaceRanking({\n    sectionName,\n    baseId,\n    pollingInterval,\n    pageSize,\n    onLog\n  });\n"}, {"language": "typescript", "code": "// useTimeRaceRanking.ts\nexport function useTimeRaceRanking(options: UseTimeRaceRankingOptions) {\n  // 使用 useRaceApi Hook 获取 API 服务\n  const {\n    sectionRankingData,\n    sectionRankingLoading,\n    sectionRankingError,\n    fetchSectionRankingData,    // 核心API调用函数\n    startSectionRankingPolling, // 轮询控制\n    stopSectionRankingPolling,\n    playerListData,\n    fetchPlayerListData,\n  } = useRaceApi();\n"}, {"language": "typescript", "code": "// useTimeRaceRanking.ts - 组件初始化逻辑\nuseEffect(() => {\n  if (baseId && sectionName && !hasInitialized.current) {\n    hasInitialized.current = true;\n\n    // 优先复用已有的选手列表数据\n    if (playerListData && playerListData.length > 0) {\n      onLog?.('success', '✅ 复用已有选手列表数据，避免重复请求');\n      \n      // 如果还没有排名数据，则异步获取\n      if (!sectionRankingData && !sectionRankingLoading) {\n        setTimeout(() => {\n          fetchSectionRankingData(baseId, sectionName);\n        }, 300);\n      }\n    } else if (!playerListLoading && !playerListData) {\n      // 没有选手列表且未在加载中，才开始获取\n      fetchPlayerListData(baseId);\n    }\n  }\n}, [baseId, sectionName, /* 其他依赖 */]);\n"}, {"language": "typescript", "code": "// useRaceApi.ts\nconst fetchPlayerListData = useCallback(\n  (baseId: string) => factories.createTableBasedDataFetcher(\n    '选手列表',\n    '选手信息表',\n    setPlayerListData,\n    setPlayerListLoading,\n    setPlayerListError,\n    async (tableId: string) => {\n      const { getPlayerInfo } = await import('../../services/api');\n      const response = await getPlayerInfo(tableId);\n      return response.data.list;\n    },\n    cacheManager.getTableStructureWithCache  // 使用缓存避免重复请求\n  )(baseId),\n  [factories, cacheManager]\n);\n"}, {"language": "typescript", "code": "// useRaceApi.ts\nconst fetchSectionRankingData = useCallback(\n  async (baseId: string, sectionName: string): Promise<void> => {\n    try {\n      setSectionRankingLoading(true);\n      setSectionRankingError(null);\n\n      const { getSectionRankingData } = await import('../../services/api');\n      \n      // 使用缓存的表结构获取函数，避免重复请求bases API\n      const result = await getSectionRankingData(\n        baseId,\n        sectionName,\n        cacheManager.getTableStructureWithCache\n      );\n\n      setSectionRankingData(result);\n    } catch (error) {\n      setSectionRankingError(error);\n    } finally {\n      setSectionRankingLoading(false);\n    }\n  },\n  [cacheManager]\n);\n"}, {"language": "typescript", "code": "// rankingApi.ts - getSectionRankingData\nexport async function getSectionRankingData(\n  baseId: string,\n  sectionName: string,\n  getTableStructureWithCache?: (baseId: string) => Promise<TableStructureResponse>\n): Promise<RankingData> {\n  \n  // 优先使用缓存函数获取表结构\n  let tables: TableStructureResponse['list'];\n  if (getTableStructureWithCache) {\n    // 推荐方式：使用缓存，避免重复API调用\n    const tableStructureData = await getTableStructureWithCache(baseId);\n    tables = tableStructureData.list;\n  } else {\n    // 直接API调用（可能导致重复请求）\n    const tableStructureResponse = await RankingApiService.getTableStructure(baseId);\n    tables = tableStructureResponse.data.list;\n  }\n"}, {"language": "typescript", "code": "// 查找必要的表\nconst answerRecordTable = tables.find(table => table.title === \"答题记录表\");\nconst playerTable = tables.find(table => table.title === \"选手表\");\n\n// 获取选手信息\nconst playerInfoResponse = await RankingApiService.getPlayerInfo(playerTable.id);\nconst playerInfo = playerInfoResponse.data.list;\n\n// 获取答题记录（带环节过滤）\nconst answerRecordResponse = await fetch(\n  `https://noco.ohvfx.com/api/v2/tables/${answerRecordTable.id}/records?limit=1000&where=(所属环节,eq,${encodeURIComponent(sectionName)})`,\n  {\n    headers: {\n      'xc-token': 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',\n    }\n  }\n);\n"}, {"language": "typescript", "code": "// 处理数据并计算排名\nconst { calculatePlayerRanks, convertApiDataToPlayerScores } = await import('../../utils/rankingUtils');\n\nif (answerRecords.length === 0) {\n  // 没有答题记录时，创建基础的选手列表（所有分数为0）\n  players = playerInfo.map((player, index) => ({\n    playerId: String(player.Id),\n    playerName: String(player.选手名 || `选手${player.Id}`),\n    stageScores: { [sectionName]: 0 },\n    totalScore: 0,\n    rank: index + 1\n  }));\n} else {\n  // 有答题记录时，正常处理数据\n  players = convertApiDataToPlayerScores(answerRecords, playerInfo);\n  rankedPlayers = calculatePlayerRanks(players);\n}\n"}, {"language": "typescript", "code": "// useTimeRaceRanking.ts\nuseEffect(() => {\n  const handleTimerStateChange = () => {\n    const timerState = globalTimerManager.getState();\n    \n    // 当计时器启动且轮询未激活时，自动开始轮询\n    if (timerState.isRunning && !isPollingActive) {\n      startPolling();\n      onLog?.('info', `计时器启动，自动开始${sectionName}排名数据轮询`);\n    }\n  };\n\n  globalTimerManager.addListener(handleTimerStateChange);\n  \n  return () => {\n    globalTimerManager.removeListener(handleTimerStateChange);\n  };\n}, [isPollingActive, startPolling]);\n"}, {"language": "typescript", "code": "// useRaceApi.ts\nconst startSectionRankingPolling = useCallback(\n  (baseId: string, sectionName: string, interval: number = 5000): void => {\n    const poll = async () => {\n      try {\n        await fetchSectionRankingData(baseId, sectionName);\n        const timeoutId = setTimeout(poll, interval);\n        setSectionPollingInterval(timeoutId);\n      } catch (error) {\n        // 错误处理和日志记录\n      }\n    };\n    poll();\n  },\n  [fetchSectionRankingData]\n);\n"}, {"language": "typescript", "code": "// rankingApi.ts\nconst pendingSectionRankingRequests = new Map<string, Promise<RankingData>>();\n\nexport async function getSectionRankingData(baseId: string, sectionName: string) {\n  const requestKey = `${baseId}_${sectionName}`;\n  \n  // 检查是否有正在进行的相同请求\n  if (pendingSectionRankingRequests.has(requestKey)) {\n    return pendingSectionRankingRequests.get(requestKey)!;\n  }\n  \n  // 创建新请求并缓存\n  const requestPromise = /* API调用逻辑 */;\n  pendingSectionRankingRequests.set(requestKey, requestPromise);\n  \n  try {\n    return await requestPromise;\n  } finally {\n    pendingSectionRankingRequests.delete(requestKey);\n  }\n}\n"}, {"language": "typescript", "code": "// rankingApi.ts\nconst sectionRankingCache = new Map<string, { data: RankingData; timestamp: number }>();\nconst CACHE_DURATION = 30000; // 30秒缓存\n\n// 检查缓存\nconst cacheKey = `${baseId}-${sectionName}`;\nconst cached = sectionRankingCache.get(cacheKey);\nconst now = Date.now();\n\nif (cached && (now - cached.timestamp) < CACHE_DURATION) {\n  return cached.data; // 返回缓存数据\n}\n"}, {"language": "typescript", "code": "// useTimeRaceRanking.ts\nconst refresh = useCallback(async () => {\n  const now = Date.now();\n  \n  // 防抖处理：如果距离上次更新不到2秒，则跳过\n  if (now - lastUpdateTime < 2000) {\n    onLog?.('info', '排名数据刷新过于频繁，跳过本次请求');\n    return;\n  }\n  \n  // 执行刷新逻辑\n}, [lastUpdateTime]);\n"}, {"language": "unknown", "code": "用户操作/计时器事件\n    ↓\nTimeRaceRankingContainer\n    ↓\nuseTimeRaceRanking Hook\n    ↓\nuseRaceApi Hook\n    ↓\nAPI Service Layer (rankingApi.ts)\n    ↓\nHTTP Client (带缓存和去重)\n    ↓\nNocoDB API\n    ↓\n数据处理和排名计算\n    ↓\n状态更新\n    ↓\nTimeRaceRankingContent 重新渲染\n"}, {"language": "typescript", "code": "// useTimeRaceRanking.ts\nuseEffect(() => {\n  if (error && isPollingActive) {\n    const retryTimer = setTimeout(() => {\n      onLog?.('info', '排名数据获取失败，正在自动重试...');\n      refresh();\n    }, 10000); // 10秒后自动重试\n\n    return () => clearTimeout(retryTimer);\n  }\n}, [error, isPollingActive, refresh]);\n"}], "links": [], "isOutdated": false, "contentHash": "3ee9e6d4"}, {"fileName": "MQTT_APP_INTEGRATION_EXAMPLE.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_APP_INTEGRATION_EXAMPLE.md", "size": 8261, "lines": 345, "wordCount": 824, "lastModified": "2025-06-25T08:09:30.700Z", "categories": ["MQTT", "FIXES", "API", "COMPONENTS", "ARCHITECTURE", "RELEASE"], "summary": {"title": "App.tsx MQTT 集成示例", "description": "本文档提供将 MQTT 组件集成到 App.tsx 的完整示例代码，包括状态管理、消息发布、错误处理等。..."}, "headings": [{"level": 1, "text": "App.tsx MQTT 集成示例"}, {"level": 2, "text": "📋 概述"}, {"level": 2, "text": "🔧 集成步骤"}, {"level": 3, "text": "1. 导入 MQTT 相关模块"}, {"level": 3, "text": "2. 在 App 组件中添加 MQTT 状态"}, {"level": 3, "text": "3. 添加 MQTT 事件处理"}, {"level": 3, "text": "4. 更新项目选择处理器"}, {"level": 3, "text": "5. 更新导航选择处理器"}, {"level": 3, "text": "6. 更新状态显示"}, {"level": 3, "text": "7. 添加侧边栏按钮 MQTT 功能（可选）"}, {"level": 2, "text": "🎯 集成效果"}, {"level": 2, "text": "🔧 配置说明"}, {"level": 3, "text": "环境配置"}, {"level": 3, "text": "连接参数"}], "codeBlocks": [{"language": "typescript", "code": "// 在现有导入后添加\nimport {\n  useMQTT,\n  MQTTUtils,\n  MQTTPresets,\n  MQTTDomain,\n  MQTTContext,\n  MQTTAction,\n  MQTTTarget,\n  type MQTTMessage,\n} from \"./services/mqtt\";\n"}, {"language": "typescript", "code": "export function App() {\n  // 现有状态...\n  const [selectedProject, setSelectedProject] = useState<string | null>(null);\n  const [selectedNavigationKey, setSelectedNavigationKey] = useState<string | null>(null);\n  const [consoleLogs, setConsoleLogs] = useState<Array<{...}>>([...]);\n\n  // 添加MQTT配置和初始化\n  const mqttConfig = useMemo(() => {\n    const baseConfig = process.env.NODE_ENV === 'development'\n      ? MQTTPresets.development\n      : MQTTPresets.production;\n\n    return {\n      ...baseConfig,\n      clientId: MQTTUtils.generateClientId('nexus-panel')\n    };\n  }, []);\n\n  // 使用MQTT Hook\n  const mqtt = useMQTT({\n    config: mqttConfig,\n    autoConnect: true,\n    debug: process.env.NODE_ENV === 'development'\n  });\n\n  // 添加日志函数\n  const addConsoleLog = (level: 'info' | 'warning' | 'error' | 'success' | 'send' | 'get', message: string, details?: any) => {\n    const newLog = {\n      id: Date.now().toString(),\n      timestamp: new Date(),\n      level,\n      message,\n      details: details ? JSON.stringify(details) : undefined\n    };\n    setConsoleLogs(prev => [newLog, ...prev.slice(0, 99)]); // 保持最新100条\n  };\n\n  // 其余组件逻辑...\n}\n"}, {"language": "typescript", "code": "// 在App组件中添加MQTT事件监听\nuseEffect(() => {\n  // 监听连接状态变化\n  if (mqtt.isConnected) {\n    addConsoleLog(\"success\", \"MQTT连接成功\", {\n      broker: mqttConfig.brokerUrl,\n      clientId: mqttConfig.clientId,\n    });\n  } else if (mqtt.error) {\n    addConsoleLog(\"error\", \"MQTT连接失败\", { error: mqtt.error });\n  }\n}, [mqtt.isConnected, mqtt.error]);\n\n// 监听MQTT消息（可选）\nuseEffect(() => {\n  if (mqtt.isConnected) {\n    // 订阅所有消息用于监控\n    mqtt.subscribe(\"#\", (message: MQTTMessage, topic: string) => {\n      addConsoleLog(\"get\", `收到MQTT消息: ${topic}`, message.data);\n    });\n  }\n}, [mqtt.isConnected]);\n"}, {"language": "typescript", "code": "// 修改现有的项目选择处理器\nconst handleProjectSelectionChange = (key: Key | null) => {\n  setSelectedProject(key as string | null);\n\n  // 添加MQTT消息发送\n  if (key && mqtt.isConnected) {\n    const raceId = parseInt((key as string).replace(\"project\", \"\"));\n    const raceNames = [\"\", \"功能测试\", \"第一阶段\", \"第二阶段\", \"决赛阶段\"];\n\n    try {\n      mqtt.publish(\n        MQTTUtils.createTopic(\n          MQTTDomain.SYSTEM,\n          MQTTContext.RACE,\n          MQTTTarget.ALL,\n          MQTTAction.LOAD\n        ),\n        {\n          raceId,\n          raceName: raceNames[raceId] || `赛事${raceId}`,\n          status: \"active\",\n          timestamp: Date.now(),\n        }\n      );\n\n      addConsoleLog(\"send\", `发送赛事切换消息: ${raceNames[raceId]}`, {\n        raceId,\n      });\n    } catch (error) {\n      addConsoleLog(\"error\", \"发送赛事消息失败\", {\n        error: error instanceof Error ? error.message : String(error),\n      });\n    }\n  }\n};\n"}, {"language": "typescript", "code": "// 修改现有的导航选择处理器\nconst handleNavigationSelectionChange = (selectedKey: string | null) => {\n  setSelectedNavigationKey(selectedKey);\n\n  // 添加MQTT消息发送\n  if (selectedKey && mqtt.isConnected) {\n    handleNavigationMQTTMessage(selectedKey);\n  }\n};\n\n// 新增导航MQTT消息处理函数\nconst handleNavigationMQTTMessage = (navigationKey: string) => {\n  try {\n    if (navigationKey.startsWith(\"rules-\")) {\n      // 规则显示消息\n      const ruleType = navigationKey.replace(\"rules-\", \"\");\n      const ruleMapping: Record<string, { id: number; name: string }> = {\n        qa: { id: 1, name: \"有问必答规则\" },\n        onestation: { id: 3, name: \"一站到底规则\" },\n        timerace: { id: 4, name: \"争分夺秒规则\" },\n        finalpk: { id: 7, name: \"终极PK规则\" },\n        tiebreak: { id: 5, name: \"同分加赛规则\" },\n        scoring: { id: 8, name: \"积分办法\" },\n      };\n\n      const rule = ruleMapping[ruleType];\n      if (rule) {\n        mqtt.publish(\n          MQTTUtils.createTopic(\n            MQTTDomain.DISPLAY,\n            MQTTContext.RULE,\n            MQTTTarget.SCREEN,\n            MQTTAction.SHOW\n          ),\n          {\n            ruleType,\n            ruleId: rule.id,\n            ruleName: rule.name,\n            content: `${rule.name}的详细说明...`,\n          }\n        );\n\n        addConsoleLog(\"info\", `发送规则显示消息: ${rule.name}`, {\n          ruleType,\n          ruleId: rule.id,\n        });\n      }\n    } else if (navigationKey.startsWith(\"switch-\")) {\n      // 环节切换消息\n      const sessionType = navigationKey.replace(\"switch-\", \"\");\n      const sessionMapping: Record<string, { id: number; name: string }> = {\n        qa: { id: 1, name: \"有问必答\" },\n        onestation: { id: 3, name: \"一站到底\" },\n        timerace: { id: 4, name: \"争分夺秒\" },\n        finalpk: { id: 7, name: \"终极PK\" },\n        tiebreak: { id: 5, name: \"同分加赛\" },\n        ranking: { id: 8, name: \"总分排名\" },\n      };\n\n      const session = sessionMapping[sessionType];\n      if (session) {\n        if (sessionType === \"ranking\") {\n          // 排行榜显示\n          mqtt.publish(\n            MQTTUtils.createTopic(\n              MQTTDomain.DISPLAY,\n              MQTTContext.RANK,\n              MQTTTarget.SCREEN,\n              MQTTAction.SHOW\n            ),\n            { rankType: \"general\", timestamp: Date.now() }\n          );\n\n          addConsoleLog(\"send\", \"发送排行榜显示消息\", { rankType: \"general\" });\n        } else {\n          // 环节开始\n          mqtt.publish(\n            MQTTUtils.createTopic(\n              MQTTDomain.QUIZ,\n              MQTTContext.SESSION,\n              MQTTTarget.ALL,\n              MQTTAction.START\n            ),\n            {\n              sessionType,\n              sessionId: session.id,\n              sessionName: session.name,\n              startTime: Date.now(),\n              config: {},\n            }\n          );\n\n          addConsoleLog(\"send\", `发送环节开始消息: ${session.name}`, {\n            sessionType,\n            sessionId: session.id,\n          });\n        }\n      }\n    }\n  } catch (error) {\n    addConsoleLog(\"error\", \"发送导航消息失败\", {\n      navigationKey,\n      error: error instanceof Error ? error.message : String(error),\n    });\n  }\n};\n"}, {"language": "typescript", "code": "// 修改Footer中的状态显示\n<Flex alignItems=\"center\" gap=\"size-100\">\n  <StatusLight variant={mqtt.isConnected ? \"positive\" : \"negative\"}>\n    {mqtt.isConnected ? \"指令服务器已连接\" : \"指令服务器已断开\"}\n  </StatusLight>\n  <StatusLight variant=\"negative\">题库服务器已断开</StatusLight>\n</Flex>\n"}, {"language": "typescript", "code": "// 在侧边栏按钮配置中添加MQTT消息发送\nconst createMQTTButtonConfig = (\n  text: string,\n  topicStructure: any,\n  data: any\n): ButtonConfig => ({\n  text,\n  styleType: ButtonStyleType.PRIMARY_ACTION,\n  onPress: () => {\n    if (mqtt.isConnected) {\n      try {\n        mqtt.publish(topicStructure, data);\n        addConsoleLog(\"send\", `发送按钮消息: ${text}`, data);\n      } catch (error) {\n        addConsoleLog(\"error\", `按钮消息发送失败: ${text}`, {\n          error: error instanceof Error ? error.message : String(error),\n        });\n      }\n    } else {\n      addConsoleLog(\"warn\", \"MQTT未连接，无法发送消息\", { text });\n    }\n  },\n});\n\n// 使用示例\nconst questionButtonGroups = [\n  createButtonGroupConfig({\n    title: \"题目切换\",\n    tooltipContent: \"控制题目区域\",\n    buttons: [\n      createMQTTButtonConfig(\n        \"上一题\",\n        MQTTUtils.createTopic(\n          MQTTDomain.QUIZ,\n          MQTTContext.QUESTION,\n          MQTTTarget.ALL,\n          MQTTAction.UPDATE\n        ),\n        { action: \"previous\", timestamp: Date.now() }\n      ),\n      createMQTTButtonConfig(\n        \"下一题\",\n        MQTTUtils.createTopic(\n          MQTTDomain.QUIZ,\n          MQTTContext.QUESTION,\n          MQTTTarget.ALL,\n          MQTTAction.UPDATE\n        ),\n        { action: \"next\", timestamp: Date.now() }\n      ),\n    ],\n  }),\n];\n"}], "links": [], "isOutdated": false, "contentHash": "-6663b02"}, {"fileName": "AudioPlayer.md", "filePath": "/Users/<USER>/nexus-panel/docs/AudioPlayer.md", "size": 8033, "lines": 370, "wordCount": 919, "lastModified": "2025-06-19T07:18:23.829Z", "categories": ["COMPONENTS", "FIXES", "API", "RELEASE", "TASKS", "ARCHITECTURE"], "summary": {"title": "AudioPlayer 组件使用指南", "description": "AudioPlayer 是一个基于 HTML5 Audio API 和 Adobe React Spectrum 设计规范开发的音频播放组件，专为题目配音文件播放而设计。..."}, "headings": [{"level": 1, "text": "AudioPlayer 组件使用指南"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "功能特性"}, {"level": 3, "text": "🎵 核心播放功能"}, {"level": 3, "text": "⏱️ 时间与进度"}, {"level": 3, "text": "🎨 设计与体验"}, {"level": 2, "text": "安装与导入"}, {"level": 2, "text": "基础用法"}, {"level": 3, "text": "简单示例"}, {"level": 3, "text": "完整示例"}, {"level": 2, "text": "API 参考"}, {"level": 3, "text": "Props 接口"}, {"level": 3, "text": "参数说明"}, {"level": 2, "text": "样式定制"}, {"level": 3, "text": "CSS 类名"}, {"level": 3, "text": "状态类名"}, {"level": 3, "text": "自定义样式示例"}, {"level": 2, "text": "支持的音频格式"}, {"level": 2, "text": "浏览器兼容性"}, {"level": 2, "text": "最佳实践"}, {"level": 3, "text": "1. 音频源配置"}, {"level": 3, "text": "2. 错误处理"}, {"level": 3, "text": "3. 性能优化"}, {"level": 3, "text": "4. 外部音频源使用"}, {"level": 3, "text": "5. 跨域和安全考虑"}, {"level": 2, "text": "常见问题"}, {"level": 3, "text": "Q: 音频无法播放？"}, {"level": 3, "text": "Q: 如何实现自动播放？"}, {"level": 3, "text": "Q: 如何自定义进度条样式？"}, {"level": 3, "text": "Q: 使用外部 HTTPS URL 时遇到跨域问题怎么办？"}, {"level": 3, "text": "Q: 外部音频加载很慢怎么优化？"}, {"level": 2, "text": "更新日志"}, {"level": 3, "text": "v1.1.0 (2024-12-19)"}, {"level": 3, "text": "v1.0.1 (2024-12-19)"}, {"level": 3, "text": "v1.0.0 (2024-12-19)"}], "codeBlocks": [{"language": "typescript", "code": "import { AudioPlayer } from './components/AudioPlayer';\n"}, {"language": "tsx", "code": "<AudioPlayer\n  audioSrc=\"/audio/sample-audio.mp3\"\n  onPlay={() => console.log('开始播放')}\n  onPause={() => console.log('暂停播放')}\n  onEnded={() => console.log('播放结束')}\n/>\n"}, {"language": "tsx", "code": "import React from 'react';\nimport { AudioPlayer } from './components/AudioPlayer';\n\nfunction QuestionPage() {\n  const handleTimeUpdate = (currentTime: number, duration: number) => {\n    console.log(`播放进度: ${currentTime.toFixed(1)}s / ${duration.toFixed(1)}s`);\n  };\n\n  return (\n    <div>\n      <h2>题目配音</h2>\n      <AudioPlayer\n        audioSrc=\"/audio/question-1.mp3\"\n        onPlay={() => console.log('题目配音开始播放')}\n        onPause={() => console.log('题目配音暂停')}\n        onEnded={() => console.log('题目配音播放完毕')}\n        onTimeUpdate={handleTimeUpdate}\n        isDisabled={false}\n        isLoading={false}\n        className=\"custom-audio-player\"\n      />\n    </div>\n  );\n}\n"}, {"language": "typescript", "code": "interface AudioPlayerProps {\n  /** 音频源路径 - 支持本地文件路径或外部 HTTPS URL */\n  audioSrc: string;\n  /** 播放事件回调 */\n  onPlay?: () => void;\n  /** 暂停事件回调 */\n  onPause?: () => void;\n  /** 播放结束回调 */\n  onEnded?: () => void;\n  /** 时间更新回调 */\n  onTimeUpdate?: (currentTime: number, duration: number) => void;\n  /** 是否禁用 */\n  isDisabled?: boolean;\n  /** 是否加载中 */\n  isLoading?: boolean;\n  /** 自定义CSS类名 */\n  className?: string;\n  /** React Spectrum 样式 */\n  UNSAFE_style?: React.CSSProperties;\n}\n"}, {"language": "css", "code": ".audio-player                    /* 主容器 */\n.audio-player-controls          /* 控件容器 */\n.audio-player-play-pause        /* 播放/暂停按钮 */\n.audio-player-replay            /* 重播按钮 */\n.audio-player-time              /* 时间显示 */\n.audio-player-progress-container /* 进度条容器 */\n.audio-player-progress-bar      /* 进度条 */\n.audio-player-progress-fill     /* 进度条填充 */\n.audio-player-volume            /* 音量按钮 */\n"}, {"language": "css", "code": ".audio-player.disabled          /* 禁用状态 */\n.audio-player.loading           /* 加载状态 */\n.audio-player.error             /* 错误状态 */\n.audio-player.success           /* 成功状态 */\n"}, {"language": "css", "code": ".custom-audio-player {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.custom-audio-player .audio-player-play-pause {\n  background-color: #ff6b6b;\n}\n\n.custom-audio-player .audio-player-progress-fill {\n  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);\n}\n"}, {"language": "typescript", "code": "// 本地文件路径（推荐用于项目内音频）\n<AudioPlayer audioSrc=\"/audio/question-1.mp3\" />\n\n// 外部 HTTPS URL（用于远程音频资源）\n<AudioPlayer audioSrc=\"https://example.com/api/audio/question-1.mp3\" />\n\n// 相对路径\n<AudioPlayer audioSrc=\"./assets/audio/sample.wav\" />\n\n// Data URL（用于 base64 编码音频）\n<AudioPlayer audioSrc=\"data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA...\" />\n\n// 避免：使用过大的音频文件\n// 建议文件大小 < 5MB，时长 < 5分钟\n"}, {"language": "typescript", "code": "const [audioError, setAudioError] = useState(false);\n\n<AudioPlayer\n  audioSrc=\"/audio/question-1.mp3\"\n  onError={() => setAudioError(true)}\n  isDisabled={audioError}\n/>\n\n{audioError && (\n  <Text>音频加载失败，请检查文件路径</Text>\n)}\n"}, {"language": "typescript", "code": "// 使用 useMemo 避免不必要的重渲染\nconst audioSrc = useMemo(() => `/audio/question-${questionId}.mp3`, [questionId]);\n\n<AudioPlayer audioSrc={audioSrc} />\n"}, {"language": "typescript", "code": "// 使用外部音频 API\nconst [audioUrl, setAudioUrl] = useState('');\nconst [isLoading, setIsLoading] = useState(true);\n\nuseEffect(() => {\n  // 从 API 获取音频 URL\n  setIsLoading(true);\n  fetch('/api/audio/get-url?questionId=123')\n    .then(res => res.json())\n    .then(data => {\n      setAudioUrl(data.audioUrl);\n      setIsLoading(false);\n    })\n    .catch(err => {\n      console.error('获取音频URL失败:', err);\n      setIsLoading(false);\n    });\n}, []);\n\n<AudioPlayer\n  audioSrc={audioUrl}\n  isLoading={isLoading}\n  onPlay={() => console.log('外部音频开始播放')}\n  onError={() => console.error('外部音频加载失败')}\n/>\n\n// CDN 音频资源\n<AudioPlayer\n  audioSrc=\"https://cdn.example.com/audio/background-music.mp3\"\n  onPlay={() => console.log('CDN 音频播放')}\n/>\n\n// 动态音频 URL（带认证参数）\nconst authenticatedAudioUrl = useMemo(() =>\n  `https://secure-api.example.com/audio/private-audio.mp3?token=${authToken}&timestamp=${Date.now()}`,\n  [authToken]\n);\n\n<AudioPlayer\n  audioSrc={authenticatedAudioUrl}\n  onPlay={() => console.log('认证音频播放')}\n  onError={() => console.log('认证失败或音频不存在')}\n/>\n"}, {"language": "typescript", "code": "// 处理跨域音频资源\n<AudioPlayer\n  audioSrc=\"https://external-api.example.com/audio/file.mp3\"\n  onError={() => {\n    console.warn('跨域音频加载失败，请检查服务器CORS设置');\n    // 可以回退到本地音频\n    setFallbackAudio('/audio/default.mp3');\n  }}\n/>\n\n// 验证音频源安全性\nconst validateAudioSrc = (src: string): boolean => {\n  // 只允许特定域名的HTTPS URL\n  const allowedDomains = ['cdn.example.com', 'api.example.com'];\n\n  if (src.startsWith('https://')) {\n    try {\n      const url = new URL(src);\n      return allowedDomains.includes(url.hostname);\n    } catch {\n      return false;\n    }\n  }\n\n  // 允许本地路径\n  return src.startsWith('/') || src.startsWith('./');\n};\n\n// 使用验证后的音频源\nconst safeAudioSrc = validateAudioSrc(userProvidedUrl) ? userProvidedUrl : '/audio/default.mp3';\n<AudioPlayer audioSrc={safeAudioSrc} />\n"}, {"language": "typescript", "code": "const audioRef = useRef<HTMLAudioElement>(null);\n\nconst handleUserInteraction = () => {\n  audioRef.current?.play();\n};\n"}, {"language": "css", "code": ".audio-player-progress-fill {\n  background: your-custom-gradient;\n}\n"}, {"language": "unknown", "code": "   Access-Control-Allow-Origin: *\n   Access-Control-Allow-Methods: GET\n   "}], "links": [], "isOutdated": false, "contentHash": "6dc61f85"}, {"fileName": "API_DUPLICATE_REQUESTS_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/API_DUPLICATE_REQUESTS_ANALYSIS.md", "size": 6210, "lines": 340, "wordCount": 779, "lastModified": "2025-07-15T12:31:33.253Z", "categories": ["API", "FIXES", "ARCHITECTURE", "COMPONENTS"], "summary": {"title": "API 重复请求问题分析报告", "description": "进入争分夺秒和同分加赛环节时，发现以下重复的 API 请求：..."}, "headings": [{"level": 1, "text": "API 重复请求问题分析报告"}, {"level": 2, "text": "问题描述"}, {"level": 2, "text": "问题根源分析"}, {"level": 3, "text": "1. 表结构重复请求 (bases API)"}, {"level": 3, "text": "2. 学生表重复请求"}, {"level": 3, "text": "3. 答题记录表重复请求"}, {"level": 2, "text": "具体代码问题点"}, {"level": 3, "text": "1. 缓存管理问题"}, {"level": 3, "text": "2. 初始化逻辑重复"}, {"level": 3, "text": "3. 状态管理冲突"}, {"level": 2, "text": "解决方案"}, {"level": 3, "text": "1. 优化缓存策略"}, {"level": 3, "text": "2. 统一数据管理"}, {"level": 3, "text": "3. 请求合并和去重"}, {"level": 2, "text": "预期效果"}, {"level": 2, "text": "优先级"}, {"level": 2, "text": "监控指标"}, {"level": 2, "text": "✅ 修复完成状态"}, {"level": 3, "text": "已完成的修复"}, {"level": 4, "text": "1. 表结构缓存优化 ✅"}, {"level": 4, "text": "2. 争分夺秒 Hook 数据复用 ✅"}, {"level": 4, "text": "3. 排名 API 请求去重 ✅"}, {"level": 4, "text": "4. useRaceApi 缓存集成 ✅"}, {"level": 3, "text": "修复效果预期"}, {"level": 4, "text": "表结构请求优化"}, {"level": 4, "text": "选手数据请求优化"}, {"level": 4, "text": "答题记录请求优化"}, {"level": 3, "text": "技术改进点"}, {"level": 3, "text": "监控建议"}, {"level": 3, "text": "后续优化建议"}, {"level": 3, "text": "最终修复方案"}, {"level": 4, "text": "5. 全局表结构缓存 ✅"}, {"level": 4, "text": "6. NavigationAPI 缓存集成 ✅"}, {"level": 4, "text": "7. 缓存架构简化 ✅"}, {"level": 3, "text": "最终修复效果"}, {"level": 3, "text": "核心架构改进"}, {"level": 4, "text": "8. 全局请求去重器 ✅"}, {"level": 4, "text": "9. API 服务层去重集成 ✅"}, {"level": 4, "text": "10. Race API 单例架构重构 ✅"}, {"level": 3, "text": "🔧 最终架构优化"}, {"level": 3, "text": "🏗️ 架构演进图"}, {"level": 4, "text": "修复前的问题架构"}, {"level": 4, "text": "修复后的优化架构"}, {"level": 3, "text": "🐛 调试信息"}, {"level": 3, "text": "📊 最终性能提升"}], "codeBlocks": [{"language": "typescript", "code": "// nexus-panel/src/hooks/useRaceApi/cache.ts:120\n// 递归调用可能导致额外请求\nreturn getTableStructureWithCache(baseId);\n"}, {"language": "typescript", "code": "// nexus-panel/src/hooks/useTimeRaceRanking-fix.ts:175-195\n// 多个useEffect可能同时触发数据获取\nuseEffect(() => {\n    // 组件初始化逻辑\n    if (baseId && sectionName && !hasInitialized.current) {\n        // 可能与其他Hook的初始化冲突\n    }\n}, [baseId, sectionName, ...]);\n"}, {"language": "typescript", "code": "// useRaceApi 和 useTimeRaceRankingFixed 都在管理选手数据\n// 可能导致重复请求\n"}, {"language": "unknown", "code": "多个组件 → 各自的useRaceApi实例 → 重复的API请求\n    ↓           ↓                    ↓\nComponent A → useRaceApi() → API请求1\nComponent B → useRaceApi() → API请求2 (重复)\nComponent C → useRaceApi() → API请求3 (重复)\n"}, {"language": "unknown", "code": "RaceApiProvider (单例)\n    ↓\n共享的useRaceApi实例\n    ↓\n全局请求去重器\n    ↓\n全局缓存管理\n    ↓\n单一API请求 (无重复)\n"}], "links": [], "isOutdated": false, "contentHash": "-603f0a2a"}, {"fileName": "MQTT_DEVELOPER_GUIDE.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_DEVELOPER_GUIDE.md", "size": 5531, "lines": 316, "wordCount": 584, "lastModified": "2025-06-25T08:09:42.769Z", "categories": ["MQTT", "COMPONENTS", "FIXES", "API", "ARCHITECTURE"], "summary": {"title": "MQTT 组件开发者使用手册", "description": "本手册为 nexus-panel 项目的 MQTT 组件提供完整的使用指南，包括初始化配置、API 使用、最佳实践等。..."}, "headings": [{"level": 1, "text": "MQTT 组件开发者使用手册"}, {"level": 2, "text": "📖 概述"}, {"level": 2, "text": "🚀 快速开始"}, {"level": 3, "text": "1. 基础导入"}, {"level": 3, "text": "2. 基础使用"}, {"level": 2, "text": "⚙️ 配置参数"}, {"level": 3, "text": "MQTT 连接配置"}, {"level": 3, "text": "预设配置"}, {"level": 2, "text": "📡 消息发布"}, {"level": 3, "text": "Topic 结构"}, {"level": 3, "text": "发布示例"}, {"level": 3, "text": "消息模板"}, {"level": 2, "text": "📥 消息订阅"}, {"level": 3, "text": "基础订阅"}, {"level": 3, "text": "便捷订阅 Hook"}, {"level": 3, "text": "预定义订阅模式"}, {"level": 2, "text": "🔧 工具函数"}, {"level": 3, "text": "Topic 操作"}, {"level": 3, "text": "配置和状态"}, {"level": 2, "text": "🎯 最佳实践"}, {"level": 3, "text": "1. 连接管理"}, {"level": 3, "text": "2. 错误处理"}, {"level": 3, "text": "3. 性能优化"}, {"level": 2, "text": "🐛 常见问题排查"}, {"level": 3, "text": "连接问题"}, {"level": 3, "text": "消息问题"}, {"level": 3, "text": "订阅问题"}], "codeBlocks": [{"language": "typescript", "code": "import {\n  useMQTT,\n  MQTTUtils,\n  MQTTPresets,\n  MQTTDomain,\n  MQTTContext,\n  MQTTAction,\n  MQTTTarget,\n} from \"./src/services/mqtt\";\n"}, {"language": "typescript", "code": "function MyComponent() {\n  // 创建MQTT配置\n  const mqttConfig = MQTTUtils.createConfig(\n    \"wss://ws.ohvfx.com:8084/mqtt\",\n    \"Nexus-Panel\"\n  );\n  mqttConfig.username = \"1001\";\n  mqttConfig.password = \"1001\";\n\n  // 使用MQTT Hook\n  const mqtt = useMQTT({\n    config: mqttConfig,\n    autoConnect: true,\n    debug: true,\n  });\n\n  // 发布消息\n  const handleSendMessage = () => {\n    mqtt.publish(\n      MQTTUtils.createTopic(\n        MQTTDomain.QUIZ,\n        MQTTContext.SESSION,\n        MQTTTarget.ALL,\n        MQTTAction.START\n      ),\n      {\n        sessionType: \"qa\",\n        sessionId: 1,\n        sessionName: \"有问必答\",\n      }\n    );\n  };\n\n  return (\n    <div>\n      <p>连接状态: {mqtt.isConnected ? \"已连接\" : \"未连接\"}</p>\n      <button onClick={handleSendMessage}>发送消息</button>\n    </div>\n  );\n}\n"}, {"language": "typescript", "code": "interface MQTTConfig {\n  brokerUrl: string; // MQTT代理服务器URL\n  clientId: string; // 客户端唯一标识\n  username?: string; // 用户名\n  password?: string; // 密码\n  keepalive?: number; // 心跳间隔(秒)，默认60\n  reconnectPeriod?: number; // 重连间隔(毫秒)，默认1000\n  connectTimeout?: number; // 连接超时(毫秒)，默认30000\n}\n"}, {"language": "typescript", "code": "// 开发环境\nconst devConfig = MQTTPresets.development;\n\n// 生产环境\nconst prodConfig = MQTTPresets.production;\n\n// 测试环境\nconst testConfig = MQTTPresets.testing;\n"}, {"language": "unknown", "code": "{domain}/{context}/{target}/{action}\n"}, {"language": "typescript", "code": "// 环节开始消息\nmqtt.publish(\n  { domain: \"quiz\", context: \"session\", target: \"all\", action: \"start\" },\n  { sessionType: \"qa\", sessionId: 1, sessionName: \"有问必答\" }\n);\n\n// 排行榜显示\nmqtt.publish(\n  { domain: \"display\", context: \"rank\", target: \"screen\", action: \"show\" },\n  { rankType: \"general\" }\n);\n\n// 选手信息显示\nmqtt.publish(\n  { domain: \"display\", context: \"player\", target: \"screen\", action: \"show\" },\n  { playerId: 1, playerName: \"1号台选手\", position: 1 }\n);\n"}, {"language": "typescript", "code": "// 使用预定义模板\nconst sessionMessage = MessageTemplates.createSessionStart(\"qa\", \"有问必答\");\nmqtt.publish(sessionMessage.topic, sessionMessage.data);\n\nconst rankMessage = MessageTemplates.createRankDisplay(\"general\");\nmqtt.publish(rankMessage.topic, rankMessage.data);\n"}, {"language": "typescript", "code": "// 订阅特定Topic\nmqtt.subscribe(\"quiz/session/all/start\", (message, topic) => {\n  console.log(\"收到环节开始消息:\", message.data);\n});\n\n// 使用通配符订阅\nmqtt.subscribe(\"display/#\", (message, topic) => {\n  console.log(\"收到显示消息:\", topic, message.data);\n});\n"}, {"language": "typescript", "code": "import {\n  useMQTTSubscription,\n  MQTTSubscriptionPatterns,\n} from \"./src/hooks/useMQTT\";\n\nfunction MyComponent() {\n  const mqtt = useMQTT({ config });\n\n  // 自动管理订阅生命周期\n  useMQTTSubscription(\n    mqtt,\n    MQTTSubscriptionPatterns.QUIZ_ALL,\n    (message, topic) => {\n      console.log(\"环节消息:\", message);\n    }\n  );\n}\n"}, {"language": "typescript", "code": "MQTTSubscriptionPatterns.QUIZ_ALL; // 'quiz/#'\nMQTTSubscriptionPatterns.DISPLAY_ALL; // 'display/#'\nMQTTSubscriptionPatterns.SYSTEM_ALL; // 'system/#'\nMQTTSubscriptionPatterns.SCREEN; // 'display/+/screen/#'\nMQTTSubscriptionPatterns.PLAYER(1); // 'display/player/player-1/#'\n"}, {"language": "typescript", "code": "// 创建Topic结构\nconst topic = MQTTUtils.createTopic(\n  MQTTDomain.QUIZ,\n  MQTTContext.SESSION,\n  \"all\",\n  MQTTAction.START\n);\n\n// 解析Topic字符串\nconst parsed = MQTTUtils.parseTopic(\"quiz/session/all/start\");\n\n// 验证Topic结构\nconst isValid = MQTTUtils.validateTopic(topic);\n\n// Topic匹配检查\nconst matches = MQTTUtils.matchesTopic(\"quiz/session/all/start\", \"quiz/#\");\n"}, {"language": "typescript", "code": "// 生成客户端ID\nconst clientId = MQTTUtils.generateClientId(\"my-app\");\n\n// 格式化连接状态\nconst statusText = MQTTUtils.formatConnectionStatus(mqtt.connectionStatus);\n\n// 获取客户端信息\nconst clientInfo = mqtt.getClientInfo();\n"}, {"language": "typescript", "code": "// 推荐：使用自动连接\nconst mqtt = useMQTT({\n  config,\n  autoConnect: true,\n});\n\n// 手动连接控制\nuseEffect(() => {\n  if (shouldConnect) {\n    mqtt.connect().catch(console.error);\n  }\n  return () => mqtt.disconnect();\n}, [shouldConnect]);\n"}, {"language": "typescript", "code": "// 监听连接错误\nuseEffect(() => {\n  if (mqtt.error) {\n    console.error(\"MQTT错误:\", mqtt.error);\n    // 显示用户友好的错误信息\n  }\n}, [mqtt.error]);\n\n// 发布消息错误处理\nconst handlePublish = async () => {\n  try {\n    mqtt.publish(topic, data);\n  } catch (error) {\n    console.error(\"消息发布失败:\", error);\n  }\n};\n"}, {"language": "typescript", "code": "// 使用useCallback避免重复订阅\nconst messageHandler = useCallback((message, topic) => {\n  // 处理消息\n}, []);\n\n// 条件订阅\nuseEffect(() => {\n  if (mqtt.isConnected && shouldSubscribe) {\n    mqtt.subscribe(topic, messageHandler);\n    return () => mqtt.unsubscribe(topic, messageHandler);\n  }\n}, [mqtt.isConnected, shouldSubscribe, topic, messageHandler]);\n"}, {"language": "typescript", "code": "// 检查连接状态\nconsole.log(\"连接状态:\", mqtt.connectionStatus);\nconsole.log(\"错误信息:\", mqtt.error);\n\n// 检查配置\nconsole.log(\"客户端信息:\", mqtt.getClientInfo());\n"}, {"language": "typescript", "code": "// 启用调试模式\nconst mqtt = useMQTT({ config, debug: true });\n\n// 验证Topic格式\nconst isValidTopic = MQTTUtils.validateTopic(topicStructure);\n"}, {"language": "typescript", "code": "// 检查通配符匹配\nconst matches = MQTTUtils.matchesTopic(actualTopic, subscriptionPattern);\n\n// 确保在连接后订阅\nuseEffect(() => {\n  if (mqtt.isConnected) {\n    mqtt.subscribe(topic, handler);\n  }\n}, [mqtt.isConnected]);\n"}], "links": [], "isOutdated": false, "contentHash": "f057b5f"}, {"fileName": "AUDIO_PLAYER_RELEASE.md", "filePath": "/Users/<USER>/nexus-panel/docs/AUDIO_PLAYER_RELEASE.md", "size": 4786, "lines": 237, "wordCount": 612, "lastModified": "2025-06-19T07:06:25.105Z", "categories": ["COMPONENTS", "RELEASE", "TASKS", "FIXES", "API", "ARCHITECTURE"], "summary": {"title": "AudioPlayer 组件发布说明", "description": "AudioPlayer 是一个专为题目配音播放而设计的高质量音频播放组件，基于 HTML5 Audio API 和 Adobe React Spectrum 设计规范开发。该组件提供了完整的音频播放控制功能，具有现代化的用户界面和优秀的用户体验。..."}, "headings": [{"level": 1, "text": "AudioPlayer 组件发布说明"}, {"level": 2, "text": "🎵 产品概述"}, {"level": 2, "text": "✨ 主要功能特性"}, {"level": 3, "text": "🎮 核心播放控制"}, {"level": 3, "text": "⏱️ 时间与进度管理"}, {"level": 3, "text": "🎨 设计与用户体验"}, {"level": 3, "text": "🔧 技术特性"}, {"level": 2, "text": "🚀 使用指南"}, {"level": 3, "text": "快速开始"}, {"level": 3, "text": "完整配置示例"}, {"level": 2, "text": "📋 组件接口"}, {"level": 3, "text": "Props 参数"}, {"level": 2, "text": "🎯 使用场景"}, {"level": 3, "text": "题目配音播放"}, {"level": 3, "text": "答案解析音频"}, {"level": 3, "text": "批量音频管理"}, {"level": 2, "text": "🔧 自定义样式"}, {"level": 3, "text": "CSS 类名系统"}, {"level": 3, "text": "主题定制示例"}, {"level": 2, "text": "🌐 浏览器兼容性"}, {"level": 2, "text": "📁 文件结构"}, {"level": 2, "text": "🧪 测试覆盖"}, {"level": 2, "text": "🔮 未来规划"}, {"level": 3, "text": "短期优化（v1.1）"}, {"level": 3, "text": "中期功能（v1.2）"}, {"level": 3, "text": "长期愿景（v2.0）"}, {"level": 2, "text": "📞 技术支持"}, {"level": 2, "text": "📝 版本更新记录"}, {"level": 3, "text": "v1.0.1 (2024-12-19) - 图标优化版本"}, {"level": 3, "text": "v1.0.0 (2024-12-19) - 初始发布版本"}], "codeBlocks": [{"language": "tsx", "code": "import { AudioPlayer } from './components/AudioPlayer';\n\nfunction QuestionPage() {\n  return (\n    <AudioPlayer\n      audioSrc=\"/audio/question-1.mp3\"\n      onPlay={() => console.log('开始播放')}\n      onPause={() => console.log('暂停播放')}\n      onEnded={() => console.log('播放结束')}\n    />\n  );\n}\n"}, {"language": "tsx", "code": "<AudioPlayer\n  audioSrc=\"/audio/question-1.mp3\"\n  onPlay={() => console.log('音频开始播放')}\n  onPause={() => console.log('音频暂停')}\n  onEnded={() => console.log('音频播放结束')}\n  onTimeUpdate={(currentTime, duration) => {\n    console.log(`播放进度: ${currentTime.toFixed(1)}s / ${duration.toFixed(1)}s`);\n  }}\n  isDisabled={false}\n  isLoading={false}\n  className=\"custom-audio-player\"\n  UNSAFE_style={{ maxWidth: '800px' }}\n/>\n"}, {"language": "tsx", "code": "// 在题目页面中使用\n<View>\n  <Text>题目内容：请听音频并回答问题</Text>\n  <AudioPlayer\n    audioSrc={`/audio/question-${questionId}.mp3`}\n    onPlay={() => trackEvent('question_audio_play')}\n    onEnded={() => trackEvent('question_audio_complete')}\n  />\n</View>\n"}, {"language": "tsx", "code": "// 在答案解析中使用\n<View>\n  <Text>答案解析</Text>\n  <AudioPlayer\n    audioSrc={`/audio/explanation-${questionId}.mp3`}\n    className=\"explanation-audio\"\n  />\n</View>\n"}, {"language": "tsx", "code": "// 支持动态切换音频源\nconst [currentAudio, setCurrentAudio] = useState('/audio/question-1.mp3');\n\n<AudioPlayer\n  audioSrc={currentAudio}\n  onEnded={() => {\n    // 自动播放下一个音频\n    setCurrentAudio(getNextAudioSrc());\n  }}\n/>\n"}, {"language": "css", "code": ".audio-player                    /* 主容器 */\n.audio-player-controls          /* 控件容器 */\n.audio-player-play-pause        /* 播放/暂停按钮 */\n.audio-player-replay            /* 重播按钮 */\n.audio-player-time              /* 时间显示 */\n.audio-player-progress-container /* 进度条容器 */\n.audio-player-progress-bar      /* 进度条 */\n.audio-player-progress-fill     /* 进度条填充 */\n.audio-player-volume            /* 音量按钮 */\n"}, {"language": "css", "code": "/* 自定义主题色彩 */\n.custom-audio-player .audio-player-play-pause {\n  background-color: #ff6b6b;\n}\n\n.custom-audio-player .audio-player-progress-fill {\n  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);\n}\n\n/* 响应式设计 */\n@media (max-width: 480px) {\n  .audio-player {\n    padding: 8px 12px;\n  }\n}\n"}, {"language": "unknown", "code": "src/components/\n├── AudioPlayer.tsx          # 主组件文件\n├── AudioPlayer.css          # 样式文件\n├── AudioPlayer.md           # 使用文档\n└── AudioPlayer.test.tsx     # 测试文件\n\npublic/audio/\n├── README.md                # 音频文件说明\n├── generate-test-audio.html # 测试音频生成工具\n└── sample-audio.mp3         # 示例音频文件\n"}], "links": [], "isOutdated": false, "contentHash": "4419ade1"}, {"fileName": "INFINITE_LOOP_FIX_V2.md", "filePath": "/Users/<USER>/nexus-panel/docs/INFINITE_LOOP_FIX_V2.md", "size": 4663, "lines": 218, "wordCount": 507, "lastModified": "2025-06-24T10:13:38.979Z", "categories": ["PERFORMANCE", "MQTT", "FIXES", "RELEASE", "COMPONENTS"], "summary": {"title": "MQTT 内存管理器无限循环修复 V2", "description": "在修复日志数量实时更新功能时，再次出现了无限循环错误：..."}, "headings": [{"level": 1, "text": "MQTT 内存管理器无限循环修复 V2"}, {"level": 2, "text": "问题背景"}, {"level": 2, "text": "问题根因分析"}, {"level": 3, "text": "第二次无限循环的原因"}, {"level": 2, "text": "修复方案"}, {"level": 3, "text": "1. 使用 useRef 避免依赖循环"}, {"level": 3, "text": "2. 简化回调设置逻辑"}, {"level": 3, "text": "3. 避免清理函数中的循环调用"}, {"level": 2, "text": "修复效果"}, {"level": 3, "text": "解决的问题"}, {"level": 3, "text": "保持的功能"}, {"level": 2, "text": "架构改进"}, {"level": 3, "text": "状态管理策略"}, {"level": 3, "text": "依赖管理原则"}, {"level": 3, "text": "清理逻辑优化"}, {"level": 2, "text": "验证方法"}, {"level": 3, "text": "1. 构建验证"}, {"level": 1, "text": "应该无错误，构建成功"}, {"level": 3, "text": "2. 运行时验证"}, {"level": 3, "text": "3. 功能验证"}, {"level": 2, "text": "经验总结"}, {"level": 3, "text": "React Hook 最佳实践"}, {"level": 3, "text": "内存管理系统设计原则"}, {"level": 3, "text": "调试技巧"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "unknown", "code": "Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.\n"}, {"language": "typescript", "code": "   // 问题代码\n   const getLogCount = useCallback(() => {\n     return consoleLogs.length;\n   }, [consoleLogs.length]); // 依赖 consoleLogs.length\n   \n   useEffect(() => {\n     if (memoryManagerStartedRef.current) {\n       memoryManager.setLogCountCallback(getLogCount);\n       memoryManager.updateStats(); // 可能触发状态更新\n     }\n   }, [memoryManager, getLogCount, cleanupConsoleLogs]); // 依赖 getLogCount\n   "}, {"language": "typescript", "code": "   // 问题代码\n   const cleanupConsoleLogs = useCallback((count: number) => {\n     setConsoleLogs(prev => {\n       const newLogs = prev.slice(0, prev.length - count);\n       addConsoleLog(\"info\", `已清理 ${count} 条历史日志`); // 调用 addConsoleLog\n       return newLogs;\n     });\n   }, []);\n   "}, {"language": "typescript", "code": "const getLogCount = useCallback(() => {\n  return consoleLogs.length;\n}, [consoleLogs.length]); // 依赖会导致重新创建\n"}, {"language": "typescript", "code": "// 使用 ref 存储函数，避免依赖循环\nconst getLogCountRef = useRef(() => consoleLogs.length);\n\n// 更新 ref 中的函数，但不触发重新渲染\nuseEffect(() => {\n  getLogCountRef.current = () => consoleLogs.length;\n}, [consoleLogs.length]);\n\n// 设置回调时使用 ref\nmemoryManager.setLogCountCallback(() => getLogCountRef.current());\n"}, {"language": "typescript", "code": "// 每次依赖变化都重新设置回调\nuseEffect(() => {\n  if (memoryManagerStartedRef.current) {\n    memoryManager.setLogCountCallback(getLogCount);\n    memoryManager.setLogCleanupCallback(cleanupConsoleLogs);\n    memoryManager.updateStats(); // 可能导致循环\n  }\n}, [memoryManager, getLogCount, cleanupConsoleLogs]);\n"}, {"language": "typescript", "code": "// 只在内存管理器启动时设置一次回调\nuseEffect(() => {\n  if (mqtt.isConnected && mqtt.service && !memoryManagerStartedRef.current) {\n    memoryManager.start(mqtt.service);\n    memoryManagerStartedRef.current = true;\n    \n    // 设置回调函数 - 只设置一次\n    memoryManager.setLogCountCallback(() => getLogCountRef.current());\n    memoryManager.setLogCleanupCallback(cleanupConsoleLogs);\n    \n    // ... 其他初始化逻辑\n  }\n}, [mqtt.isConnected, mqtt.service, memoryManager, cleanupConsoleLogs]);\n"}, {"language": "typescript", "code": "const cleanupConsoleLogs = useCallback((count: number) => {\n  setConsoleLogs(prev => {\n    const newLogs = prev.slice(0, prev.length - count);\n    addConsoleLog(\"info\", `已清理 ${count} 条历史日志`); // 可能导致循环\n    return newLogs;\n  });\n}, []);\n"}, {"language": "typescript", "code": "const cleanupConsoleLogs = useCallback((count: number) => {\n  setConsoleLogs(prev => {\n    const newLogs = prev.slice(0, prev.length - count);\n    \n    // 避免在清理过程中调用 addConsoleLog，防止循环依赖\n    // 直接添加清理日志到结果中\n    const cleanupLog = {\n      id: `cleanup-${Date.now()}-${logIdCounter.current++}`,\n      timestamp: new Date(),\n      level: 'info' as const,\n      message: `已清理 ${count} 条历史日志`,\n      details: JSON.stringify({\n        清理前数量: prev.length,\n        清理后数量: newLogs.length\n      })\n    };\n    \n    return [cleanupLog, ...newLogs];\n  });\n}, []);\n"}, {"language": "bash", "code": "npm run build\n# 应该无错误，构建成功\n"}], "links": [], "isOutdated": false, "contentHash": "7ef36e27"}, {"fileName": "MQTT_CODE_QUALITY_REPORT.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_CODE_QUALITY_REPORT.md", "size": 4554, "lines": 287, "wordCount": 609, "lastModified": "2025-06-24T05:46:29.274Z", "categories": ["MQTT", "FIXES", "COMPONENTS", "ARCHITECTURE"], "summary": {"title": "MQTT组件代码质量评估报告", "description": "本报告对nexus-panel项目的MQTT组件进行全面的代码质量评估，包括架构设计、性能分析、安全性评估等。..."}, "headings": [{"level": 1, "text": "MQTT组件代码质量评估报告"}, {"level": 2, "text": "📊 评估概述"}, {"level": 2, "text": "🏆 整体评分"}, {"level": 2, "text": "✅ 优势分析"}, {"level": 3, "text": "1. 架构设计优秀"}, {"level": 3, "text": "2. 类型安全完备"}, {"level": 3, "text": "3. 错误处理机制"}, {"level": 3, "text": "4. React集成优雅"}, {"level": 2, "text": "⚠️ 改进建议"}, {"level": 3, "text": "1. 性能优化空间"}, {"level": 3, "text": "2. 重连策略优化"}, {"level": 3, "text": "3. 内存泄漏防护"}, {"level": 3, "text": "4. 安全性增强"}, {"level": 2, "text": "📈 性能分析"}, {"level": 3, "text": "连接性能"}, {"level": 3, "text": "消息处理性能"}, {"level": 3, "text": "优化建议"}, {"level": 2, "text": "🔒 安全性评估"}, {"level": 3, "text": "当前安全措施"}, {"level": 3, "text": "安全风险"}, {"level": 3, "text": "安全建议"}, {"level": 2, "text": "🧪 测试覆盖度"}, {"level": 3, "text": "当前状态"}, {"level": 3, "text": "测试建议"}, {"level": 2, "text": "📋 改进优先级"}, {"level": 3, "text": "高优先级 (立即实施)"}, {"level": 3, "text": "中优先级 (短期实施)"}, {"level": 3, "text": "低优先级 (长期规划)"}, {"level": 2, "text": "🎯 总结"}], "codeBlocks": [{"language": "typescript", "code": "// 清晰的接口定义\nexport interface MQTTConfig {\n  brokerUrl: string;\n  clientId: string;\n  // ...\n}\n\n// 服务层封装\nexport class MQTTService {\n  constructor(config: MQTTConfig) { /* ... */ }\n}\n\n// Hook层封装\nexport function useMQTT(options: UseMQTTOptions): UseMQTTReturn {\n  // ...\n}\n"}, {"language": "typescript", "code": "// 结构化的Topic类型\nexport interface MQTTTopicStructure {\n  domain: MQTTDomain;\n  context: MQTTContext;\n  target: string;\n  action: MQTTAction;\n}\n\n// 联合类型定义\nexport type MQTTMessageData = \n  | SessionData \n  | RankData \n  | PlayerData \n  | Record<string, unknown>;\n"}, {"language": "typescript", "code": "try {\n  this.client.publish(topic, JSON.stringify(message), publishOptions, (error) => {\n    if (error) {\n      console.error(`消息发布失败 [${topic}]:`, error);\n    }\n  });\n} catch (error) {\n  console.error(`消息解析错误 [${topic}]:`, error);\n}\n"}, {"language": "typescript", "code": "// 建议添加消息缓存\nclass MQTTService {\n  private messageCache = new Map<string, MQTTMessage[]>();\n  private maxCacheSize = 1000;\n  \n  private cacheMessage(topic: string, message: MQTTMessage) {\n    if (!this.messageCache.has(topic)) {\n      this.messageCache.set(topic, []);\n    }\n    const cache = this.messageCache.get(topic)!;\n    cache.push(message);\n    if (cache.length > this.maxCacheSize) {\n      cache.shift();\n    }\n  }\n}\n"}, {"language": "typescript", "code": "// 建议实现指数退避重连\nprivate calculateReconnectDelay(attempt: number): number {\n  const baseDelay = 1000;\n  const maxDelay = 30000;\n  const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);\n  return delay + Math.random() * 1000; // 添加随机抖动\n}\n"}, {"language": "typescript", "code": "// 建议添加定期清理机制\nclass MQTTService {\n  private cleanupInterval: NodeJS.Timeout;\n  \n  constructor(config: MQTTConfig) {\n    // 每小时清理一次过期数据\n    this.cleanupInterval = setInterval(() => {\n      this.cleanup();\n    }, 3600000);\n  }\n  \n  private cleanup() {\n    // 清理过期的消息处理器\n    // 清理无效的订阅\n  }\n}\n"}, {"language": "typescript", "code": "// 建议添加消息签名验证\ninterface MQTTMessage {\n  timestamp: number;\n  sender: string;\n  signature?: string; // 消息签名\n  data: MQTTMessageData;\n}\n\n// 消息验证函数\nprivate validateMessage(message: MQTTMessage): boolean {\n  // 验证时间戳是否在合理范围内\n  const now = Date.now();\n  if (Math.abs(now - message.timestamp) > 300000) { // 5分钟\n    return false;\n  }\n  \n  // 验证消息签名\n  if (message.signature) {\n    return this.verifySignature(message);\n  }\n  \n  return true;\n}\n"}, {"language": "typescript", "code": "// 建议的测试结构\ndescribe('MQTTService', () => {\n  describe('连接管理', () => {\n    it('应该成功连接到MQTT代理', async () => {\n      // 测试连接功能\n    });\n    \n    it('应该在连接失败时自动重试', async () => {\n      // 测试重连机制\n    });\n  });\n  \n  describe('消息处理', () => {\n    it('应该正确发布消息', () => {\n      // 测试消息发布\n    });\n    \n    it('应该正确处理接收的消息', () => {\n      // 测试消息接收\n    });\n  });\n});\n"}], "links": [], "isOutdated": false, "contentHash": "-25177d91"}, {"fileName": "RELEASE_NOTES.md", "filePath": "/Users/<USER>/nexus-panel/docs/RELEASE_NOTES.md", "size": 4449, "lines": 261, "wordCount": 463, "lastModified": "2025-06-19T10:03:21.244Z", "categories": ["RELEASE", "COMPONENTS"], "summary": {"title": "🎯 Nexus Panel v0.2.0 重磅更新", "description": "**Nexus Panel** 是一款专为赛事管理打造的现代化控制面板，提供直观易用的操作界面和专业的管理功能。本次 v0.2.0 版本带来了激动人心的音频播放功能和全面的按钮样式优化，让您的赛事管理体验更加专业和高效。..."}, "headings": [{"level": 1, "text": "🎯 Nexus Panel v0.2.0 重磅更新"}, {"level": 2, "text": "🌟 产品概述"}, {"level": 3, "text": "✨ 为什么选择 Nexus Panel？"}, {"level": 2, "text": "🆕 v0.2.0 版本亮点"}, {"level": 3, "text": "🎵 全新音频播放器"}, {"level": 3, "text": "🎨 按钮样式全面升级"}, {"level": 2, "text": "🚀 核心功能亮点"}, {"level": 3, "text": "🎵 专业音频播放器"}, {"level": 3, "text": "📋 智能导航系统"}, {"level": 3, "text": "📊 设备状态监控"}, {"level": 3, "text": "🎨 智能按钮样式系统"}, {"level": 3, "text": "🎛️ 操作模式切换"}, {"level": 3, "text": "💻 控制台管理"}, {"level": 3, "text": "🎨 现代化界面"}, {"level": 2, "text": "📱 跨设备兼容"}, {"level": 3, "text": "🖥️ 电脑端 (推荐)"}, {"level": 3, "text": "📱 平板端"}, {"level": 3, "text": "📱 手机端"}, {"level": 2, "text": "🎯 主要使用场景"}, {"level": 3, "text": "🏆 赛事现场管理"}, {"level": 3, "text": "📝 赛前准备工作"}, {"level": 3, "text": "🔧 技术支持服务"}, {"level": 2, "text": "🎮 快速上手指南"}, {"level": 3, "text": "第一步：了解界面布局"}, {"level": 3, "text": "第二步：基本操作流程"}, {"level": 3, "text": "第三步：高级功能使用"}, {"level": 2, "text": "🔗 网络连接状态"}, {"level": 2, "text": "📋 版本信息"}, {"level": 3, "text": "🆕 v0.2.0 版本新增功能"}, {"level": 3, "text": "📈 v0.1.0 基础功能"}, {"level": 2, "text": "💡 使用小贴士"}, {"level": 3, "text": "🎯 提高效率的技巧"}, {"level": 3, "text": "🔧 常见问题解决"}, {"level": 2, "text": "📞 技术支持"}, {"level": 2, "text": "🎉 感谢使用"}, {"level": 2, "text": "🎉 升级亮点总结"}, {"level": 3, "text": "🎵 音频播放器 - 让赛事更生动"}, {"level": 3, "text": "🎨 按钮样式系统 - 让操作更直观"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "-215af3d"}, {"fileName": "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "size": 4407, "lines": 222, "wordCount": 479, "lastModified": "2025-07-15T13:07:31.011Z", "categories": ["API", "FIXES"], "summary": {"title": "环节数据重复请求问题分析与修复", "description": "首次进入争分夺秒环节或同分加赛环节时，会出现 **4 次环节列表 API 请求**，具体表现为：..."}, "headings": [{"level": 1, "text": "环节数据重复请求问题分析与修复"}, {"level": 2, "text": "🔍 问题描述"}, {"level": 2, "text": "🕵️ 问题根源分析"}, {"level": 3, "text": "1. 多个 useRaceApi 实例问题"}, {"level": 3, "text": "2. 重复的数据获取逻辑"}, {"level": 3, "text": "3. 组件初始化时序问题"}, {"level": 2, "text": "✅ 修复方案"}, {"level": 3, "text": "1. 添加环节数据 API 请求去重 ✅"}, {"level": 3, "text": "2. 优化 fetchAllProjectData 函数 ✅"}, {"level": 3, "text": "3. 全局请求去重机制 ✅"}, {"level": 2, "text": "📊 修复效果"}, {"level": 3, "text": "预期结果"}, {"level": 3, "text": "调试信息"}, {"level": 2, "text": "🧪 测试验证"}, {"level": 3, "text": "测试步骤"}, {"level": 3, "text": "成功标准"}, {"level": 3, "text": "调试命令"}, {"level": 2, "text": "🔧 架构改进"}, {"level": 3, "text": "修复前的问题架构"}, {"level": 3, "text": "修复后的优化架构"}, {"level": 2, "text": "🚨 潜在问题和后续优化"}, {"level": 3, "text": "1. useRaceApi 多实例问题"}, {"level": 3, "text": "2. 组件初始化时序"}, {"level": 3, "text": "3. 缓存一致性"}, {"level": 2, "text": "📈 性能提升"}], "codeBlocks": [{"language": "typescript", "code": "// 1. fetchAllProjectData 直接调用 getSectionData\nconst [sectionDataResponse, configurationResult] = await Promise.all([\n  import(\"../../services/api\").then(({ getSectionData }) =>\n    getSectionData(sectionTable.id)\n  ),\n  // ...\n]);\n\n// 2. fetchNavigationData 调用 getNavigationData，内部也调用 getSectionData\nconst fetchNavigationData = useCallback(\n  (baseId: string) =>\n    factories.createDataFetcher(\n      \"导航\",\n      setNavigationData,\n      setNavigationLoading,\n      setNavigationError,\n      getNavigationData // 这个函数内部也会调用 getSectionData\n    )(baseId),\n  [factories]\n);\n"}, {"language": "typescript", "code": "static async getSectionData(tableId: string): Promise<ApiResponse<SectionDataResponse>> {\n    // 使用全局请求去重器防止重复请求\n    const { GlobalRequestDeduplicator } = await import('./requestDeduplicator');\n\n    return GlobalRequestDeduplicator.execute(\n        `section_data_${tableId}`,\n        async () => {\n            // 原有的API请求逻辑\n        }\n    );\n}\n"}, {"language": "typescript", "code": "// 修复前：直接调用 getSectionData\nconst [sectionDataResponse, configurationResult] = await Promise.all([\n  import(\"../../services/api\").then(({ getSectionData }) =>\n    getSectionData(sectionTable.id)\n  ),\n  // ...\n]);\n\n// 修复后：复用 getNavigationData\nconst [navigationResult, configurationResult] = await Promise.all([\n  import(\"../../services/api\").then(({ getNavigationData }) =>\n    getNavigationData(baseId)\n  ),\n  // ...\n]);\n"}, {"language": "unknown", "code": "[GlobalRequestDeduplicator] 🔍 请求去重检查\n[NavigationApiService] 🚀 发起环节数据API请求\n[NavigationApiService] ✅ 环节数据API请求成功\n[GlobalRequestDeduplicator] ⏳ 检测到重复请求，等待现有请求完成 (后续重复请求)\n"}, {"language": "javascript", "code": "// 加载调试脚本\nconst script = document.createElement(\"script\");\nscript.src = \"/debug-api-requests.js\";\ndocument.head.appendChild(script);\n\n// 开始监控\ndebugApiRequests.startMonitoring(30000);\n// 然后进行测试操作\n"}, {"language": "unknown", "code": "主应用 → useRaceApi() → fetchAllProjectData → getSectionData\n                                            ↓\n争分夺秒组件 → useRaceApi() → fetchNavigationData → getNavigationData → getSectionData\n                                                                      ↓\nTimeRaceRanking → useRaceApi() → (可能触发额外请求)\n"}, {"language": "unknown", "code": "主应用 → useRaceApi() → fetchAllProjectData → getNavigationData (复用)\n                                            ↓\n争分夺秒组件 → useRaceApi() → (复用已有数据)\n                           ↓\nTimeRaceRanking → useRaceApi() → (复用已有数据)\n                               ↓\n                    GlobalRequestDeduplicator (防止重复请求)\n"}], "links": [], "isOutdated": false, "contentHash": "54133cc3"}, {"fileName": "SidebarButtonStyles.md", "filePath": "/Users/<USER>/nexus-panel/docs/SidebarButtonStyles.md", "size": 4401, "lines": 260, "wordCount": 439, "lastModified": "2025-06-18T09:46:24.065Z", "categories": ["COMPONENTS"], "summary": {"title": "侧边栏按钮样式系统使用指南", "description": "侧边栏按钮样式系统为 `SidebarButtonGroup` 组件提供了一套统一、可复用的按钮样式解决方案。通过预定义的样式类型，您可以快速为不同功能的按钮应用合适的视觉样式，确保界面的一致性和用户体验。..."}, "headings": [{"level": 1, "text": "侧边栏按钮样式系统使用指南"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "核心特性"}, {"level": 2, "text": "样式类型详解"}, {"level": 3, "text": "1. PRIMARY_ACTION (主要操作)"}, {"level": 3, "text": "2. NAVIGATION (导航操作)"}, {"level": 3, "text": "3. CONTENT_DISPLAY (内容展示)"}, {"level": 3, "text": "4. DATA_OPERATION (数据操作)"}, {"level": 3, "text": "5. SPECIAL_FUNCTION (特殊功能)"}, {"level": 3, "text": "6. DANGER_ACTION (危险操作)"}, {"level": 3, "text": "7. INFO_DISPLAY (信息展示)"}, {"level": 2, "text": "使用方式"}, {"level": 3, "text": "基础使用"}, {"level": 3, "text": "自动样式推荐"}, {"level": 3, "text": "混合使用"}, {"level": 2, "text": "样式自定义"}, {"level": 3, "text": "CSS类名结构"}, {"level": 3, "text": "自定义样式"}, {"level": 2, "text": "最佳实践"}, {"level": 3, "text": "1. 选择合适的样式类型"}, {"level": 3, "text": "2. 利用自动推荐功能"}, {"level": 3, "text": "3. 合理使用危险操作样式"}, {"level": 3, "text": "4. 响应式考虑"}, {"level": 2, "text": "迁移指南"}, {"level": 3, "text": "从现有代码迁移"}, {"level": 3, "text": "渐进式迁移"}, {"level": 2, "text": "故障排除"}, {"level": 3, "text": "样式不生效"}, {"level": 3, "text": "TypeScript错误"}, {"level": 3, "text": "性能考虑"}], "codeBlocks": [{"language": "typescript", "code": "{\n  text: '跳转',\n  styleType: ButtonStyleType.PRIMARY_ACTION,\n  onPress: () => handleJump()\n}\n"}, {"language": "typescript", "code": "{\n  text: '上一题',\n  styleType: ButtonStyleType.NAVIGATION,\n  onPress: () => goToPrevious()\n}\n"}, {"language": "typescript", "code": "{\n  text: '正确答案',\n  styleType: ButtonStyleType.CONTENT_DISPLAY,\n  onPress: () => showCorrectAnswer()\n}\n"}, {"language": "typescript", "code": "{\n  text: '赋分',\n  styleType: ButtonStyleType.DATA_OPERATION,\n  onPress: () => assignScore()\n}\n"}, {"language": "typescript", "code": "{\n  text: '填空放大',\n  styleType: ButtonStyleType.SPECIAL_FUNCTION,\n  onPress: () => enlargeBlank()\n}\n"}, {"language": "typescript", "code": "{\n  text: '重置',\n  styleType: ButtonStyleType.DANGER_ACTION,\n  onPress: () => resetData()\n}\n"}, {"language": "typescript", "code": "{\n  text: '当前状态: 已连接',\n  styleType: ButtonStyleType.INFO_DISPLAY\n  // 通常不需要 onPress 事件\n}\n"}, {"language": "typescript", "code": "import { SidebarButtonGroup, ButtonConfig } from './components/SidebarButtonGroup';\nimport { ButtonStyleType } from './components/SidebarButtonStyles';\n\nconst buttons: ButtonConfig[] = [\n  {\n    text: '跳转',\n    styleType: ButtonStyleType.PRIMARY_ACTION,\n    onPress: () => console.log('跳转')\n  },\n  {\n    text: '上一题',\n    styleType: ButtonStyleType.NAVIGATION,\n    onPress: () => console.log('上一题')\n  }\n];\n\n<SidebarButtonGroup\n  title=\"题目切换\"\n  buttons={buttons}\n/>\n"}, {"language": "typescript", "code": "import { suggestButtonStyleType } from './components/SidebarButtonStyles';\n\nconst buttonTexts = ['上一题', '跳转', '正确答案', '赋分'];\nconst autoButtons: ButtonConfig[] = buttonTexts.map(text => ({\n  text,\n  styleType: suggestButtonStyleType(text),\n  onPress: () => console.log(text)\n}));\n"}, {"language": "typescript", "code": "const mixedButtons: ButtonConfig[] = [\n  {\n    text: '自定义样式',\n    styleType: ButtonStyleType.PRIMARY_ACTION,\n    variant: 'secondary', // 手动指定variant会覆盖styleType的设置\n    className: 'my-custom-class' // 自定义CSS类名会与styleType的类名合并\n  },\n  {\n    text: '传统方式',\n    variant: 'accent' // 不使用styleType，直接指定variant\n  }\n];\n"}, {"language": "css", "code": "/* 自定义主要操作按钮的样式 */\n.sidebar-button-primary-action {\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n  border-radius: 8px;\n}\n\n.sidebar-button-primary-action:hover {\n  transform: scale(1.05);\n}\n"}, {"language": "typescript", "code": "// 旧方式\n{\n  text: '跳转',\n  variant: 'accent'\n}\n\n// 新方式\n{\n  text: '跳转',\n  styleType: ButtonStyleType.PRIMARY_ACTION\n}\n"}], "links": [], "isOutdated": false, "contentHash": "-3b370ca1"}, {"fileName": "REALTIME_LOG_COUNT_FIX.md", "filePath": "/Users/<USER>/nexus-panel/docs/REALTIME_LOG_COUNT_FIX.md", "size": 4399, "lines": 259, "wordCount": 502, "lastModified": "2025-06-24T10:21:42.751Z", "categories": ["PERFORMANCE", "MQTT", "RELEASE", "FIXES", "COMPONENTS"], "summary": {"title": "日志数量实时更新修复报告", "description": "用户发现了一个数据一致性问题：..."}, "headings": [{"level": 1, "text": "日志数量实时更新修复报告"}, {"level": 2, "text": "问题描述"}, {"level": 2, "text": "根本原因分析"}, {"level": 3, "text": "数据流对比"}, {"level": 3, "text": "更新机制差异"}, {"level": 3, "text": "问题根因"}, {"level": 2, "text": "解决方案"}, {"level": 3, "text": "实现主动触发更新机制"}, {"level": 4, "text": "1. 添加日志时触发更新"}, {"level": 4, "text": "2. 清空日志时触发更新"}, {"level": 4, "text": "3. 刷新状态时触发更新"}, {"level": 3, "text": "技术实现细节"}, {"level": 4, "text": "1. 使用 setTimeout(fn, 0) 的原因"}, {"level": 4, "text": "2. 使用 useCallback 包装函数"}, {"level": 4, "text": "3. 条件检查机制"}, {"level": 2, "text": "修复效果"}, {"level": 3, "text": "更新时机对比"}, {"level": 3, "text": "触发场景"}, {"level": 3, "text": "数据一致性"}, {"level": 2, "text": "验证方法"}, {"level": 3, "text": "1. 功能验证"}, {"level": 3, "text": "2. 性能验证"}, {"level": 3, "text": "3. 一致性验证"}, {"level": 2, "text": "架构改进"}, {"level": 3, "text": "数据流优化"}, {"level": 3, "text": "设计原则"}, {"level": 2, "text": "经验总结"}, {"level": 3, "text": "React 状态管理最佳实践"}, {"level": 3, "text": "内存管理系统设计"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "typescript", "code": "// 直接使用 props 中的 logs 数组\n<Text UNSAFE_className=\"console-panel-badge-text\">\n  {logs.length}  // 直接绑定到 React 状态，自动更新\n</Text>\n"}, {"language": "typescript", "code": "// 通过内存管理器的定时器获取\nmemoryStats.logCount  // 每5秒通过回调函数获取一次\n"}, {"language": "typescript", "code": "const addConsoleLog = (level, message, details) => {\n  setConsoleLogs(prev => {\n    // ... 添加日志逻辑\n    return newLogs;\n  });\n  // 没有触发内存统计更新\n};\n"}, {"language": "typescript", "code": "const addConsoleLog = useCallback((level, message, details) => {\n  setConsoleLogs(prev => {\n    // ... 添加日志逻辑\n    const newLogs = [newLog, ...prev.slice(0, 99)];\n    \n    // 主动触发内存统计更新，确保日志数量实时反映\n    if (memoryManagerStartedRef.current) {\n      setTimeout(() => {\n        memoryManager.updateStats();\n      }, 0);\n    }\n    \n    return newLogs;\n  });\n}, [memoryManager]);\n"}, {"language": "typescript", "code": "const handleClearConsoleLogs = () => {\n  setConsoleLogs([]);\n  // 没有触发内存统计更新\n};\n"}, {"language": "typescript", "code": "const handleClearConsoleLogs = useCallback(() => {\n  setConsoleLogs([]);\n  \n  // 主动触发内存统计更新，确保日志数量立即归零\n  if (memoryManagerStartedRef.current) {\n    setTimeout(() => {\n      memoryManager.updateStats();\n    }, 0);\n  }\n}, [memoryManager]);\n"}, {"language": "typescript", "code": "const handleRefreshConsoleStatus = () => {\n  // ... 添加刷新日志\n  setConsoleLogs(prev => [newLog, ...prev]);\n  // 没有触发内存统计更新\n};\n"}, {"language": "typescript", "code": "const handleRefreshConsoleStatus = useCallback(() => {\n  // ... 添加刷新日志\n  setConsoleLogs(prev => [newLog, ...prev]);\n  \n  // 主动触发内存统计更新\n  if (memoryManagerStartedRef.current) {\n    setTimeout(() => {\n      memoryManager.updateStats();\n    }, 0);\n  }\n}, [memoryManager]);\n"}, {"language": "typescript", "code": "setTimeout(() => {\n  memoryManager.updateStats();\n}, 0);\n"}, {"language": "typescript", "code": "const addConsoleLog = useCallback((level, message, details) => {\n  // ... 实现\n}, [memoryManager]);\n"}, {"language": "typescript", "code": "if (memoryManagerStartedRef.current) {\n  // 只有在内存管理器启动时才触发更新\n}\n"}, {"language": "unknown", "code": "日志操作 → 状态更新 → 主动触发统计更新 → UI 同步显示\n     ↓\n  React 自动重新渲染 → 标题栏徽章更新\n     ↓\n  手动触发更新 → 内存统计更新\n"}], "links": [], "isOutdated": false, "contentHash": "-5adbb415"}, {"fileName": "MQTT_MEMORY_MANAGEMENT.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_MEMORY_MANAGEMENT.md", "size": 4389, "lines": 256, "wordCount": 563, "lastModified": "2025-06-24T09:59:12.113Z", "categories": ["PERFORMANCE", "MQTT", "FIXES", "RELEASE"], "summary": {"title": "MQTT 内存管理系统", "description": "MQTT 内存管理系统是为 nexus-panel 项目设计的智能内存泄漏防护机制，旨在防止 MQTT 客户端在长时间运行过程中出现内存泄漏问题。..."}, "headings": [{"level": 1, "text": "MQTT 内存管理系统"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "核心功能"}, {"level": 3, "text": "1. 主动内存监控"}, {"level": 3, "text": "2. 内存统计报告"}, {"level": 3, "text": "3. 用户界面集成"}, {"level": 2, "text": "架构设计"}, {"level": 3, "text": "核心组件"}, {"level": 4, "text": "1. MQTT<PERSON>emoryManager"}, {"level": 4, "text": "2. useMQTTMemoryManager Hook"}, {"level": 4, "text": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 集成"}, {"level": 2, "text": "配置选项"}, {"level": 3, "text": "内存管理器配置"}, {"level": 3, "text": "健康状态评估"}, {"level": 2, "text": "使用方法"}, {"level": 3, "text": "1. 基本集成"}, {"level": 3, "text": "2. 手动清理"}, {"level": 3, "text": "3. 监控内存状态"}, {"level": 2, "text": "清理策略"}, {"level": 3, "text": "1. 时间维度清理"}, {"level": 3, "text": "2. 频率维度清理"}, {"level": 3, "text": "3. 容量维度清理"}, {"level": 3, "text": "4. 渐进式清理"}, {"level": 2, "text": "性能影响"}, {"level": 3, "text": "1. 清理操作性能"}, {"level": 3, "text": "2. 内存占用"}, {"level": 3, "text": "3. CPU 使用"}, {"level": 2, "text": "故障排除"}, {"level": 3, "text": "1. 常见问题"}, {"level": 3, "text": "2. 调试模式"}, {"level": 2, "text": "最佳实践"}, {"level": 3, "text": "1. 配置建议"}, {"level": 3, "text": "2. 监控建议"}, {"level": 3, "text": "3. 性能优化"}, {"level": 2, "text": "版本历史"}], "codeBlocks": [{"language": "typescript", "code": "class MQTTMemoryManager {\n  // 配置参数\n  private config: {\n    cleanupInterval: number;     // 清理间隔（默认30分钟）\n    handlerThreshold: number;    // 处理器数量阈值（默认50）\n    logThreshold: number;        // 日志数量阈值（默认1000）\n    handlerExpireTime: number;   // 处理器过期时间（默认1小时）\n    debug: boolean;              // 调试模式\n  }\n}\n"}, {"language": "typescript", "code": "const memoryManager = useMQTTMemoryManager({\n  config: {\n    cleanupInterval: 30 * 60 * 1000,  // 30分钟\n    handlerThreshold: 50,\n    logThreshold: 1000,\n    handlerExpireTime: 60 * 60 * 1000, // 1小时\n    debug: false\n  },\n  autoStart: false,\n  statsUpdateInterval: 5000,  // 5秒更新统计\n  debug: false\n});\n"}, {"language": "typescript", "code": "<ConsolePanel\n  memoryStats={memoryManager.memoryStats}\n  onMemoryCleanup={memoryManager.manualCleanup}\n  isCleaningMemory={memoryManager.isCleaningUp}\n  // ... 其他 props\n/>\n"}, {"language": "typescript", "code": "import { useMQTTMemoryManager } from './hooks/useMQTTMemoryManager';\n\nexport function App() {\n  // MQTT Hook\n  const mqtt = useMQTT({ /* 配置 */ });\n  \n  // 内存管理器 Hook\n  const memoryManager = useMQTTMemoryManager({\n    config: {\n      cleanupInterval: 30 * 60 * 1000,\n      handlerThreshold: 50,\n      logThreshold: 1000,\n      debug: process.env.NODE_ENV === 'development'\n    }\n  });\n\n  // 初始化内存管理器\n  useEffect(() => {\n    if (mqtt.isConnected && mqtt.service) {\n      memoryManager.start(mqtt.service);\n      memoryManager.setLogCountCallback(() => consoleLogs.length);\n      memoryManager.setLogCleanupCallback(cleanupConsoleLogs);\n    }\n    \n    return () => {\n      if (memoryManager.memoryManager) {\n        memoryManager.stop();\n      }\n    };\n  }, [mqtt.isConnected, mqtt.service]);\n\n  // ... 其他代码\n}\n"}, {"language": "typescript", "code": "// 手动触发清理\nconst handleManualCleanup = async () => {\n  try {\n    const result = await memoryManager.manualCleanup();\n    console.log('清理完成:', result);\n  } catch (error) {\n    console.error('清理失败:', error);\n  }\n};\n"}, {"language": "typescript", "code": "// 获取当前内存统计\nconst stats = memoryManager.memoryStats;\nif (stats) {\n  console.log('处理器数量:', stats.handlerCount);\n  console.log('订阅数量:', stats.subscriptionCount);\n  console.log('健康状态:', stats.healthStatus);\n}\n\n// 获取清理历史\nconst history = memoryManager.cleanupHistory;\nconsole.log('最近清理记录:', history);\n"}, {"language": "typescript", "code": "const memoryManager = useMQTTMemoryManager({\n  config: { debug: true },\n  debug: true\n});\n"}], "links": [], "isOutdated": false, "contentHash": "28d591a4"}, {"fileName": "TimeRaceRanking_Usage.md", "filePath": "/Users/<USER>/nexus-panel/docs/TimeRaceRanking_Usage.md", "size": 4125, "lines": 202, "wordCount": 474, "lastModified": "2025-07-14T09:56:38.280Z", "categories": ["API", "FIXES", "MQTT", "COMPONENTS"], "summary": {"title": "争分夺秒和同分加赛动态排名功能使用指南", "description": "本文档介绍如何使用新实现的争分夺秒和同分加赛环节的动态排名功能。该功能支持实时数据更新、分页显示和与现有系统的无缝集成。..."}, "headings": [{"level": 1, "text": "争分夺秒和同分加赛动态排名功能使用指南"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "功能特性"}, {"level": 3, "text": "1. 实时排名显示"}, {"level": 3, "text": "2. 分页功能"}, {"level": 3, "text": "3. 侧边栏控制"}, {"level": 3, "text": "4. 错误处理和性能优化"}, {"level": 2, "text": "使用方法"}, {"level": 3, "text": "1. 基本使用"}, {"level": 3, "text": "2. 集成到题目骨架屏"}, {"level": 3, "text": "3. 侧边栏按钮组配置"}, {"level": 2, "text": "API 接口"}, {"level": 3, "text": "useRaceApi Hook 新增方法"}, {"level": 3, "text": "getSectionRankingData API"}, {"level": 2, "text": "配置选项"}, {"level": 3, "text": "TimeRaceRankingContent Props"}, {"level": 3, "text": "QuestionSkeleton 新增 Props"}, {"level": 2, "text": "注意事项"}, {"level": 2, "text": "故障排除"}, {"level": 3, "text": "常见问题"}, {"level": 3, "text": "调试技巧"}], "codeBlocks": [{"language": "tsx", "code": "import { TimeRaceRankingContent } from '../components/TimeRaceRankingContent';\nimport { useRaceApi } from '../hooks/useRaceApi';\n\nfunction TimeRaceSection() {\n  const {\n    sectionRankingData,\n    sectionRankingLoading,\n    sectionRankingError,\n    fetchSectionRankingData,\n    startSectionRankingPolling,\n    stopSectionRankingPolling\n  } = useRaceApi();\n\n  return (\n    <TimeRaceRankingContent\n      sectionName=\"争分夺秒\"\n      baseId=\"your-base-id\"\n      rankingData={sectionRankingData}\n      loading={sectionRankingLoading}\n      error={sectionRankingError}\n      onStartPolling={() => startSectionRankingPolling('your-base-id', '争分夺秒')}\n      onStopPolling={stopSectionRankingPolling}\n      onRefresh={() => fetchSectionRankingData('your-base-id', '争分夺秒')}\n      onLog={(level, message, details) => console.log(level, message, details)}\n    />\n  );\n}\n"}, {"language": "tsx", "code": "// ContentArea.tsx 中的自动检测逻辑\nconst isTimeRaceSection = Boolean(contentType === '快答' || \n  (nodeName && (nodeName.includes('争分夺秒') || nodeName.includes('同分加赛'))));\n\nreturn (\n  <QuestionSkeleton \n    animated={true} \n    height=\"auto\" \n    showRankingArea={isTimeRaceSection}\n    rankingRowCount={8}\n  />\n);\n"}, {"language": "tsx", "code": "// 按钮组配置示例\nconst buttonGroups = createTimeRaceAndTieBreakButtonGroups(\n  contentType,\n  nodeName,\n  onLog,\n  // ... 其他参数\n  onScoreAssignment,  // 成绩赋分回调\n  onPageFirst,        // 第一页回调\n  onPageSecond        // 第二页回调\n);\n"}, {"language": "typescript", "code": "interface UseRaceApiReturn {\n  // 环节排名数据相关功能\n  sectionRankingData: RankingData | null;\n  sectionRankingLoading: boolean;\n  sectionRankingError: ApiError | null;\n  fetchSectionRankingData: (baseId: string, sectionName: string) => Promise<void>;\n  startSectionRankingPolling: (baseId: string, sectionName: string, interval?: number) => void;\n  stopSectionRankingPolling: () => void;\n  resetSectionRankingData: () => void;\n}\n"}, {"language": "typescript", "code": "/**\n * 获取指定环节的排名数据\n * @param baseId 项目基础ID\n * @param sectionName 环节名称（如\"争分夺秒\"、\"同分加赛\"）\n * @returns 环节排名数据\n */\nexport async function getSectionRankingData(\n  baseId: string,\n  sectionName: string\n): Promise<RankingData>\n"}, {"language": "tsx", "code": "<TimeRaceRankingContent\n  onLog={(level, message, details) => {\n    console.log(`[${level}] ${message}`, details);\n  }}\n  // ... 其他props\n/>\n"}], "links": [], "isOutdated": false, "contentHash": "41d5324e"}, {"fileName": "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "size": 3656, "lines": 194, "wordCount": 314, "lastModified": "2025-07-15T13:30:55.307Z", "categories": ["API", "COMPONENTS", "FIXES"], "summary": {"title": "TimeRaceRankingContent 数据获取问题分析", "description": "通过代码分析发现，`TimeRaceRankingContent` 组件没有获取到选手列表数据和环节排名数据的主要原因是：**组件架构设计完整，但缺少实际的集成和调用**。..."}, "headings": [{"level": 1, "text": "TimeRaceRankingContent 数据获取问题分析"}, {"level": 2, "text": "问题概述"}, {"level": 2, "text": "根本原因分析"}, {"level": 3, "text": "1. 组件未被实际使用"}, {"level": 3, "text": "2. 导航配置缺失"}, {"level": 3, "text": "3. 组件渲染路径缺失"}, {"level": 3, "text": "4. 数据流断裂"}, {"level": 2, "text": "具体问题点"}, {"level": 3, "text": "1. App.tsx 中的导航处理不完整"}, {"level": 3, "text": "2. DynamicComponent<PERSON><PERSON><PERSON> 缺少排名组件渲染"}, {"level": 3, "text": "3. 组件集成缺失"}, {"level": 2, "text": "解决方案"}, {"level": 3, "text": "方案1：修改现有架构（推荐）"}, {"level": 3, "text": "方案2：创建专门的排名内容类型"}, {"level": 3, "text": "方案3：动态切换显示模式"}, {"level": 2, "text": "推荐实施步骤"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "typescript", "code": "// App.tsx 中的导航处理逻辑\nconst isTimeRaceSection = currentNode.name.includes('争分夺秒') ||\n    currentNode.name.includes('同分加赛') ||\n    currentNode.contentType?.includes('快答');\n\nif (isTimeRaceSection) {\n    // 只获取了选手列表数据，但没有触发排名数据获取\n    if (!raceApi.playerListLoading && !raceApi.playerListData) {\n        raceApi.fetchPlayerListData(selectedProject);\n    }\n}\n"}, {"language": "typescript", "code": "// componentConfigurations.ts\n\"快答\": {\n    skeleton: \"QuestionSkeleton\",           // 使用题目骨架屏\n    buttonGroups: \"timeRaceAndTieBreakButtonGroups\", // 使用争分夺秒按钮组\n    showBreadcrumbs: true,\n    showAudioPlayer: false\n}\n"}, {"language": "unknown", "code": "✅ useRaceApi Hook (数据获取逻辑完整)\n    ↓\n✅ fetchSectionRankingData (API调用正常)\n    ↓\n✅ getSectionRankingData (服务层实现完整)\n    ↓\n❌ 数据传递到组件 (缺失环节)\n    ↓\n❌ TimeRaceRankingContent 渲染 (未被调用)\n"}, {"language": "typescript", "code": "// 当前代码只处理题目获取\nif (isTimeRaceSection) {\n    // ✅ 获取选手列表\n    raceApi.fetchPlayerListData(selectedProject);\n    \n    // ❌ 缺失：没有获取排名数据\n    // ❌ 缺失：没有启动轮询\n    // ❌ 缺失：没有渲染排名组件\n}\n"}, {"language": "typescript", "code": "if (isTimeRaceSection) {\n    // 获取选手列表\n    raceApi.fetchPlayerListData(selectedProject);\n    \n    // 获取环节排名数据\n    raceApi.fetchSectionRankingData(selectedProject, sectionName);\n    \n    // 启动轮询（如果需要实时更新）\n    raceApi.startSectionRankingPolling(selectedProject, sectionName);\n}\n"}, {"language": "typescript", "code": "// 当前 componentConfigurations.ts 配置\n\"快答\": {\n    skeleton: \"QuestionSkeleton\",  // ❌ 应该是 RankingSkeleton\n    // ...\n}\n"}, {"language": "typescript", "code": "\"快答\": {\n    skeleton: \"RankingSkeleton\",  // ✅ 使用排名骨架屏\n    buttonGroups: \"timeRaceAndTieBreakButtonGroups\",\n    showBreadcrumbs: true,\n    showAudioPlayer: false\n}\n"}, {"language": "typescript", "code": "if (isTimeRaceSection) {\n    // 获取选手列表\n    raceApi.fetchPlayerListData(selectedProject);\n    \n    // 获取环节排名数据\n    const sectionName = currentNode.name.includes('争分夺秒') ? '争分夺秒' : '同分加赛';\n    raceApi.fetchSectionRankingData(selectedProject, sectionName);\n}\n"}, {"language": "typescript", "code": "\"快答\": {\n    skeleton: \"RankingSkeleton\",\n    buttonGroups: \"timeRaceAndTieBreakButtonGroups\",\n    showBreadcrumbs: true,\n    showAudioPlayer: false\n}\n"}, {"language": "typescript", "code": "\"争分夺秒排名\": {\n    skeleton: \"RankingSkeleton\",\n    buttonGroups: \"timeRaceAndTieBreakButtonGroups\",\n    showBreadcrumbs: true,\n    showAudioPlayer: false\n}\n"}], "links": [], "isOutdated": false, "contentHash": "5ddc3090"}, {"fileName": "MQTT_COMPONENT_ANALYSIS.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_COMPONENT_ANALYSIS.md", "size": 3476, "lines": 165, "wordCount": 404, "lastModified": "2025-06-24T05:44:49.622Z", "categories": ["MQTT", "FIXES", "COMPONENTS", "ARCHITECTURE", "RELEASE"], "summary": {"title": "MQTT组件全面分析报告", "description": "本文档对nexus-panel项目中的MQTT组件进行全面分析，包括文件关联关系、功能用途、架构设计等关键信息。..."}, "headings": [{"level": 1, "text": "MQTT组件全面分析报告"}, {"level": 2, "text": "📋 概述"}, {"level": 2, "text": "🗂️ 文件结构分析"}, {"level": 3, "text": "核心文件组织"}, {"level": 3, "text": "依赖关系图"}, {"level": 2, "text": "🔧 核心组件功能分析"}, {"level": 3, "text": "1. types.ts - 类型定义层"}, {"level": 3, "text": "2. MQTTService.ts - 核心服务层"}, {"level": 3, "text": "3. useMQTT.ts - React Hook层"}, {"level": 3, "text": "4. index.ts - 工具函数层"}, {"level": 2, "text": "🔗 组件间调用链分析"}, {"level": 3, "text": "数据流向图"}, {"level": 2, "text": "📊 架构特点分析"}, {"level": 3, "text": "优势"}, {"level": 3, "text": "设计模式"}, {"level": 2, "text": "🎯 集成状态分析"}, {"level": 3, "text": "当前状态"}, {"level": 3, "text": "集成就绪度"}], "codeBlocks": [{"language": "unknown", "code": "nexus-panel/src/\n├── services/mqtt/\n│   ├── types.ts              # 类型定义和枚举 (268行)\n│   ├── MQTTService.ts        # 核心服务类 (324行)\n│   └── index.ts              # 统一导出文件 (271行)\n├── hooks/\n│   └── useMQTT.ts           # React Hook封装 (302行)\n└── App.tsx                   # 主应用组件 (未集成MQTT)\n"}, {"language": "mermaid", "code": "graph TD\n    A[App.tsx] -.-> B[useMQTT.ts]\n    B --> C[MQTTService.ts]\n    C --> D[types.ts]\n    E[index.ts] --> C\n    E --> D\n    B --> D\n    \n    style A fill:#ffcccc\n    style B fill:#ccffcc\n    style C fill:#ccccff\n    style D fill:#ffffcc\n    style E fill:#ffccff\n"}, {"language": "typescript", "code": "// Topic结构：{domain}/{context}/{target}/{action}\nexport interface MQTTTopicStructure {\n  domain: MQTTDomain;    // 业务域: quiz|display|system\n  context: MQTTContext;  // 具体场景: session|rank|player等\n  target: string;        // 作用目标: all|player-1|screen\n  action: MQTTAction;    // 行为动作: start|show|update等\n}\n"}, {"language": "typescript", "code": "interface UseMQTTReturn {\n  connectionStatus: MQTTConnectionStatus;\n  isConnected: boolean;\n  error: string | null;\n  connect: () => Promise<void>;\n  disconnect: () => void;\n  publish: (topicStructure, data) => void;\n  subscribe: (topic, handler) => void;\n  unsubscribe: (topic, handler?) => void;\n  getClientInfo: () => ClientInfo | null;\n}\n"}, {"language": "mermaid", "code": "sequenceDiagram\n    participant App as App.tsx\n    participant Hook as useMQTT Hook\n    participant Service as MQTTService\n    participant Broker as MQTT Broker\n    \n    App->>Hook: useMQTT(config)\n    Hook->>Service: new MQTTService(config)\n    Hook->>Service: connect()\n    Service->>Broker: mqtt.connect()\n    Broker-->>Service: connect event\n    Service-->>Hook: statusChange event\n    Hook-->>App: isConnected: true\n    \n    App->>Hook: publish(topic, data)\n    Hook->>Service: publish(topic, data)\n    Service->>Broker: client.publish()\n    \n    Broker->>Service: message event\n    Service->>Hook: message handler\n    Hook->>App: callback execution\n"}], "links": [], "isOutdated": false, "contentHash": "4df9cc11"}, {"fileName": "MEMORY_STATS_EXPLANATION.md", "filePath": "/Users/<USER>/nexus-panel/docs/MEMORY_STATS_EXPLANATION.md", "size": 3364, "lines": 198, "wordCount": 428, "lastModified": "2025-06-24T10:10:30.557Z", "categories": ["MQTT", "PERFORMANCE", "RELEASE"], "summary": {"title": "MQTT 内存统计详解", "description": "**含义**：当前活跃的 MQTT 消息处理器数量..."}, "headings": [{"level": 1, "text": "MQTT 内存统计详解"}, {"level": 2, "text": "内存统计指标说明"}, {"level": 3, "text": "1. 处理器数量 (Handler Count)"}, {"level": 3, "text": "2. 订阅数量 (Subscription Count)"}, {"level": 3, "text": "3. 日志数量 (Log Count) ⭐"}, {"level": 3, "text": "4. 清理次数 (Total Cleanups)"}, {"level": 3, "text": "5. 最后清理 (Last Cleanup Time)"}, {"level": 2, "text": "日志数量实时更新机制"}, {"level": 3, "text": "问题背景"}, {"level": 3, "text": "原因分析"}, {"level": 3, "text": "解决方案"}, {"level": 4, "text": "1. 动态回调更新"}, {"level": 4, "text": "2. 立即更新机制"}, {"level": 3, "text": "更新时机"}, {"level": 2, "text": "健康状态评估"}, {"level": 3, "text": "状态计算逻辑"}, {"level": 3, "text": "默认阈值"}, {"level": 3, "text": "状态指示"}, {"level": 2, "text": "清理策略"}, {"level": 3, "text": "日志清理"}, {"level": 3, "text": "处理器清理"}, {"level": 2, "text": "监控建议"}, {"level": 3, "text": "正常范围"}, {"level": 3, "text": "需要关注"}, {"level": 3, "text": "需要处理"}, {"level": 2, "text": "故障排除"}, {"level": 3, "text": "日志数量不更新"}, {"level": 3, "text": "统计数据异常"}, {"level": 3, "text": "性能问题"}], "codeBlocks": [{"language": "typescript", "code": "// 每次订阅都会增加处理器\nmqtt.subscribe(\"topic/1\", handler1); // +1\nmqtt.subscribe(\"topic/2\", handler2); // +1\nmqtt.subscribe(\"topic/1\", handler3); // +1 (同一主题可以有多个处理器)\n// 总计：3个处理器\n"}, {"language": "typescript", "code": "mqtt.subscribe(\"topic/1\", handler1); // +1 主题\nmqtt.subscribe(\"topic/2\", handler2); // +1 主题\nmqtt.subscribe(\"topic/1\", handler3); // +0 主题 (已存在)\n// 总计：2个主题\n"}, {"language": "typescript", "code": "// 创建稳定的日志计数回调\nconst getLogCount = useCallback(() => {\n  return consoleLogs.length;\n}, [consoleLogs.length]);\n\n// 当日志数量变化时重新设置回调\nuseEffect(() => {\n  if (memoryManagerStartedRef.current) {\n    memoryManager.setLogCountCallback(getLogCount);\n    memoryManager.setLogCleanupCallback(cleanupConsoleLogs);\n    \n    // 立即更新统计，确保日志数量实时反映\n    memoryManager.updateStats();\n  }\n}, [memoryManager, getLogCount, cleanupConsoleLogs]);\n"}, {"language": "typescript", "code": "// 添加手动更新统计的方法\nupdateStats: () => void;\n\n// 在日志变化时立即触发更新\nmemoryManager.updateStats();\n"}, {"language": "typescript", "code": "private calculateHealthStatus(handlerCount: number, logCount: number): 'healthy' | 'warning' | 'critical' {\n  if (handlerCount > this.config.handlerThreshold || logCount > this.config.logThreshold) {\n    return 'critical';  // 红色：超过阈值\n  }\n  \n  if (handlerCount > this.config.handlerThreshold * 0.7 || logCount > this.config.logThreshold * 0.7) {\n    return 'warning';   // 黄色：接近阈值 (70%)\n  }\n\n  return 'healthy';     // 绿色：正常范围\n}\n"}], "links": [], "isOutdated": false, "contentHash": "31b9c481"}, {"fileName": "PLAYER_DATA_AUTO_FETCH.md", "filePath": "/Users/<USER>/nexus-panel/docs/PLAYER_DATA_AUTO_FETCH.md", "size": 3144, "lines": 213, "wordCount": 343, "lastModified": "2025-07-15T13:06:45.944Z", "categories": ["API", "COMPONENTS"], "summary": {"title": "选手数据自动获取功能", "description": "**实现时间：** 2025-01-15  ..."}, "headings": [{"level": 1, "text": "选手数据自动获取功能"}, {"level": 2, "text": "📋 功能概述"}, {"level": 2, "text": "🎯 功能描述"}, {"level": 2, "text": "🔧 实现逻辑"}, {"level": 3, "text": "触发条件"}, {"level": 3, "text": "代码实现"}, {"level": 2, "text": "📊 工作流程"}, {"level": 2, "text": "🔍 调试信息"}, {"level": 3, "text": "成功获取时的日志"}, {"level": 3, "text": "跳过获取时的情况"}, {"level": 2, "text": "🎯 支持的环节类型"}, {"level": 3, "text": "1. 争分夺秒环节"}, {"level": 3, "text": "2. 同分加赛环节"}, {"level": 3, "text": "3. 快答环节"}, {"level": 2, "text": "🔄 与现有功能的集成"}, {"level": 3, "text": "Race API 集成"}, {"level": 3, "text": "导航系统集成"}, {"level": 3, "text": "日志系统集成"}, {"level": 2, "text": "📈 性能优化"}, {"level": 3, "text": "智能检查机制"}, {"level": 3, "text": "内存管理"}, {"level": 2, "text": "🧪 测试验证"}, {"level": 3, "text": "测试场景"}, {"level": 3, "text": "验证方法"}, {"level": 2, "text": "🚨 注意事项"}, {"level": 3, "text": "依赖条件"}, {"level": 3, "text": "错误处理"}, {"level": 3, "text": "兼容性"}, {"level": 2, "text": "🔮 未来优化"}, {"level": 3, "text": "预加载策略"}, {"level": 3, "text": "缓存优化"}, {"level": 3, "text": "性能监控"}], "codeBlocks": [{"language": "typescript", "code": "// 检查是否为争分夺秒或同分加赛环节，需要获取选手列表数据\nconst isTimeRaceSection = currentNode.name.includes('争分夺秒') || \n    currentNode.name.includes('同分加赛') ||\n    currentNode.contentType?.includes('快答');\n\nif (isTimeRaceSection) {\n    // 获取选手列表数据（如果尚未加载）\n    if (!raceApi.playerListLoading && !raceApi.playerListData) {\n        addConsoleLog('info', `[${currentNode.name}] 选手数据未加载，开始获取`, {\n            sectionName: currentNode.name,\n            contentType: currentNode.contentType,\n            selectedProject,\n            timestamp: Date.now(),\n            action: 'fetch_player_list_for_time_race'\n        });\n        raceApi.fetchPlayerListData(selectedProject);\n    }\n}\n"}, {"language": "mermaid", "code": "graph TD\n    A[用户选择导航节点] --> B{检查节点类型}\n    B -->|争分夺秒/同分加赛/快答| C{检查选手数据状态}\n    B -->|其他类型| D[继续正常流程]\n    C -->|数据未加载且未在加载中| E[自动获取选手数据]\n    C -->|数据已存在或正在加载| F[跳过获取]\n    E --> G[记录日志]\n    F --> H[继续题目数据获取]\n    G --> H\n    D --> H\n"}, {"language": "unknown", "code": "[${currentNode.name}] 选手数据未加载，开始获取\n{\n  sectionName: \"争分夺秒\",\n  contentType: \"快答\",\n  selectedProject: \"project_id\",\n  timestamp: 1705123456789,\n  action: \"fetch_player_list_for_time_race\"\n}\n"}], "links": [], "isOutdated": false, "contentHash": "-7030c6a2"}, {"fileName": "API_REQUEST_TESTING_GUIDE.md", "filePath": "/Users/<USER>/nexus-panel/docs/API_REQUEST_TESTING_GUIDE.md", "size": 3056, "lines": 198, "wordCount": 366, "lastModified": "2025-07-15T13:05:16.645Z", "categories": ["API", "ARCHITECTURE", "PERFORMANCE", "COMPONENTS"], "summary": {"title": "API 重复请求修复测试指南", "description": "1. 打开浏览器开发者工具..."}, "headings": [{"level": 1, "text": "API 重复请求修复测试指南"}, {"level": 2, "text": "🧪 测试步骤"}, {"level": 3, "text": "1. 准备测试环境"}, {"level": 3, "text": "2. 执行测试场景"}, {"level": 4, "text": "场景 1：进入争分夺秒环节"}, {"level": 4, "text": "场景 2：进入同分加赛环节"}, {"level": 4, "text": "场景 3：切换项目"}, {"level": 3, "text": "3. 查看调试日志"}, {"level": 4, "text": "✅ 正常情况下的日志"}, {"level": 4, "text": "✅ 缓存命中时的日志"}, {"level": 4, "text": "✅ 请求去重时的日志"}, {"level": 3, "text": "4. 问题诊断"}, {"level": 4, "text": "检查点 1：请求来源"}, {"level": 4, "text": "检查点 2：请求时间"}, {"level": 4, "text": "检查点 3：baseId 差异"}, {"level": 4, "text": "检查点 4：控制台日志"}, {"level": 2, "text": "🔍 高级调试"}, {"level": 3, "text": "获取缓存统计信息"}, {"level": 3, "text": "手动清理缓存"}, {"level": 2, "text": "📊 性能验证"}, {"level": 3, "text": "修复前 vs 修复后对比"}, {"level": 3, "text": "成功标准"}, {"level": 2, "text": "🚨 常见问题排查"}, {"level": 3, "text": "问题 1：仍然看到 2 次请求"}, {"level": 3, "text": "问题 2：缓存没有命中"}, {"level": 3, "text": "问题 3：预加载没有生效"}, {"level": 2, "text": "📞 获取帮助"}], "codeBlocks": [{"language": "unknown", "code": "[GlobalRequestDeduplicator] 🔍 请求去重检查\n[GlobalTableStructureCache] 🔍 请求表结构数据 (请求#1)\n[NavigationApiService] 🚀 发起表结构API请求\n[GlobalTableStructureCache] ✅ 数据已存储到全局缓存 (请求#1)\n"}, {"language": "unknown", "code": "[GlobalTableStructureCache] 🔍 请求表结构数据 (请求#2)\n[GlobalTableStructureCache] ✅ 全局缓存命中 (请求#2)\n"}, {"language": "unknown", "code": "[GlobalRequestDeduplicator] ⏳ 检测到重复请求，等待现有请求完成\n[GlobalTableStructureCache] ⏳ 检测到正在进行的表结构请求，等待结果\n"}, {"language": "javascript", "code": "// 获取全局缓存统计\nimport(\"./src/services/api/tableStructureCache.js\").then((module) => {\n  console.log(\"缓存统计:\", module.GlobalTableStructureCache.getCacheStats());\n});\n\n// 获取请求去重统计\nimport(\"./src/services/api/requestDeduplicator.js\").then((module) => {\n  console.log(\"去重统计:\", module.GlobalRequestDeduplicator.getStats());\n});\n\n// 获取预加载统计\nimport(\"./src/services/api/preloader.js\").then((module) => {\n  console.log(\"预加载统计:\", module.ApiPreloader.getStats());\n});\n"}, {"language": "javascript", "code": "// 清理所有缓存\nimport(\"./src/services/api/tableStructureCache.js\").then((module) => {\n  module.GlobalTableStructureCache.clearCache();\n});\n\n// 清理请求去重器\nimport(\"./src/services/api/requestDeduplicator.js\").then((module) => {\n  module.GlobalRequestDeduplicator.clearAll();\n});\n"}], "links": [], "isOutdated": false, "contentHash": "5fdec176"}, {"fileName": "Integration_Verification.md", "filePath": "/Users/<USER>/nexus-panel/docs/Integration_Verification.md", "size": 3027, "lines": 226, "wordCount": 539, "lastModified": "2025-07-14T09:57:35.745Z", "categories": ["API", "COMPONENTS", "MQTT", "FIXES", "TASKS"], "summary": {"title": "争分夺秒动态排名功能集成验证清单", "description": "本文档提供了验证争分夺秒和同分加赛动态排名功能与现有系统集成效果的详细清单。..."}, "headings": [{"level": 1, "text": "争分夺秒动态排名功能集成验证清单"}, {"level": 2, "text": "验证概述"}, {"level": 2, "text": "1. 组件集成验证"}, {"level": 3, "text": "✅ TimeRaceRankingContent 组件"}, {"level": 3, "text": "✅ QuestionSkeleton 扩展"}, {"level": 3, "text": "✅ ContentArea 集成"}, {"level": 2, "text": "2. API 集成验证"}, {"level": 3, "text": "✅ useRaceApi Hook 扩展"}, {"level": 3, "text": "✅ rankingApi 服务扩展"}, {"level": 3, "text": "✅ API 导出和类型定义"}, {"level": 2, "text": "3. 配置系统集成验证"}, {"level": 3, "text": "✅ 按钮组配置扩展"}, {"level": 3, "text": "✅ 组件配置集成"}, {"level": 2, "text": "4. 功能验证清单"}, {"level": 3, "text": "数据获取和显示"}, {"level": 3, "text": "轮询机制"}, {"level": 3, "text": "错误处理"}, {"level": 3, "text": "UI/UX 验证"}, {"level": 3, "text": "侧边栏按钮组"}, {"level": 2, "text": "5. 性能验证"}, {"level": 3, "text": "内存和资源管理"}, {"level": 3, "text": "网络优化"}, {"level": 2, "text": "6. 兼容性验证"}, {"level": 3, "text": "现有功能兼容性"}, {"level": 3, "text": "数据格式兼容性"}, {"level": 2, "text": "7. 边界情况验证"}, {"level": 3, "text": "数据边界"}, {"level": 3, "text": "网络边界"}, {"level": 3, "text": "用户操作边界"}, {"level": 2, "text": "8. 集成测试建议"}, {"level": 3, "text": "单元测试"}, {"level": 1, "text": "运行TimeRaceRankingContent组件测试"}, {"level": 1, "text": "运行useRaceApi Hook测试"}, {"level": 1, "text": "运行API服务测试"}, {"level": 3, "text": "集成测试"}, {"level": 1, "text": "运行完整的排名功能集成测试"}, {"level": 1, "text": "运行按钮组配置测试"}, {"level": 3, "text": "端到端测试"}, {"level": 1, "text": "使用Cypress或Playwright进行端到端测试"}, {"level": 2, "text": "9. 部署前检查"}, {"level": 3, "text": "代码质量"}, {"level": 3, "text": "构建验证"}, {"level": 3, "text": "功能回归测试"}, {"level": 2, "text": "10. 上线后监控"}, {"level": 3, "text": "关键指标"}, {"level": 3, "text": "日志监控"}, {"level": 2, "text": "验证结果"}], "codeBlocks": [{"language": "bash", "code": "# 运行TimeRaceRankingContent组件测试\nnpm test TimeRaceRankingContent.test.tsx\n\n# 运行useRaceApi Hook测试\nnpm test useRaceApi.test.ts\n\n# 运行API服务测试\nnpm test rankingApi.test.ts\n"}, {"language": "bash", "code": "# 运行完整的排名功能集成测试\nnpm test -- --testPathPattern=\"ranking.*integration\"\n\n# 运行按钮组配置测试\nnpm test buttonGroupConfigurations.test.ts\n"}, {"language": "bash", "code": "# 使用Cypress或Playwright进行端到端测试\nnpm run e2e:test -- --spec=\"**/time-race-ranking.cy.ts\"\n"}], "links": [], "isOutdated": false, "contentHash": "-1c861395"}, {"fileName": "TimeRaceRanking_Refactor_Comparison.md", "filePath": "/Users/<USER>/nexus-panel/docs/TimeRaceRanking_Refactor_Comparison.md", "size": 3020, "lines": 183, "wordCount": 319, "lastModified": "2025-07-15T07:34:45.835Z", "categories": ["API", "COMPONENTS", "FIXES", "RELEASE"], "summary": {"title": "TimeRaceRankingContent 重构对比文档", "description": "本次重构将原本745行的复杂组件拆分为简洁的分层架构，遵循关注点分离原则，参考了 `RankingContent` 的简洁设计。..."}, "headings": [{"level": 1, "text": "TimeRaceRankingContent 重构对比文档"}, {"level": 2, "text": "重构概述"}, {"level": 2, "text": "架构对比"}, {"level": 3, "text": "重构前（原始架构）"}, {"level": 3, "text": "重构后（新架构）"}, {"level": 2, "text": "代码行数对比"}, {"level": 2, "text": "功能对比"}, {"level": 3, "text": "保持不变的功能"}, {"level": 3, "text": "改进的功能"}, {"level": 2, "text": "使用方式对比"}, {"level": 3, "text": "重构前使用方式"}, {"level": 3, "text": "重构后使用方式"}, {"level": 2, "text": "性能优化对比"}, {"level": 3, "text": "重构前的性能问题"}, {"level": 3, "text": "重构后的性能优化"}, {"level": 2, "text": "测试友好性对比"}, {"level": 3, "text": "重构前测试困难"}, {"level": 3, "text": "重构后测试友好"}, {"level": 2, "text": "迁移指南"}, {"level": 3, "text": "无缝迁移"}, {"level": 3, "text": "渐进式迁移"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "unknown", "code": "TimeRaceRankingContent.tsx (745行)\n├── 数据获取逻辑 (useRaceApi)\n├── 轮询管理逻辑 (15个useEffect)\n├── 计时器同步逻辑 (globalTimerManager)\n├── 错误处理逻辑 (分散在多处)\n├── 分页控制逻辑\n├── 防抖处理逻辑\n└── UI渲染逻辑\n"}, {"language": "unknown", "code": "数据管理层:\n├── useTimeRaceRanking.ts (350行)\n│   ├── 数据获取和状态管理\n│   ├── 轮询生命周期管理\n│   ├── 计时器同步逻辑\n│   ├── 错误处理和重试机制\n│   └── 分页控制逻辑\n\nUI展示层:\n├── TimeRaceRankingContent.new.tsx (458行)\n│   ├── 纯UI渲染逻辑\n│   ├── 状态展示组件\n│   └── 用户交互处理\n\n容器层:\n└── TimeRaceRankingContainer.tsx (85行)\n    └── 数据与UI的桥接\n"}, {"language": "tsx", "code": "// 直接使用复杂组件\n<TimeRaceRankingContent\n  sectionName=\"争分夺秒\"\n  baseId={baseId}\n  pollingInterval={5000}\n  pageSize={8}\n  onLog={handleLog}\n/>\n"}, {"language": "tsx", "code": "// 使用容器组件，与原来的使用方式完全一致\n<TimeRaceRankingContainer\n  sectionName=\"争分夺秒\"\n  baseId={baseId}\n  pollingInterval={5000}\n  pageSize={8}\n  onLog={handleLog}\n/>\n"}, {"language": "tsx", "code": "// 自定义数据管理逻辑\nfunction MyCustomRankingComponent() {\n  const {\n    rankingData,\n    loading,\n    error,\n    isPolling,\n    currentPage,\n    lastUpdateTime,\n    refresh,\n    changePage\n  } = useTimeRaceRanking({\n    sectionName: \"争分夺秒\",\n    baseId,\n    pollingInterval: 3000, // 自定义轮询间隔\n    onLog: handleLog\n  });\n\n  return (\n    <TimeRaceRankingContent\n      sectionName=\"争分夺秒\"\n      rankingData={rankingData}\n      loading={loading}\n      error={error}\n      isPolling={isPolling}\n      currentPage={currentPage}\n      lastUpdateTime={lastUpdateTime}\n      onRefresh={refresh}\n      onPageChange={changePage}\n      onLog={handleLog}\n    />\n  );\n}\n"}], "links": [], "isOutdated": false, "contentHash": "78ee3ad5"}, {"fileName": "USERACEAPI_HOOK_FIX.md", "filePath": "/Users/<USER>/nexus-panel/docs/USERACEAPI_HOOK_FIX.md", "size": 3006, "lines": 184, "wordCount": 310, "lastModified": "2025-07-15T10:27:08.536Z", "categories": ["API", "FIXES", "ARCHITECTURE", "COMPONENTS"], "summary": {"title": "useRaceApi Hook 修复报告", "description": "修复了 `nexus-panel/src/hooks/useRaceApi/useRaceApi.ts` 文件中的多个 TypeScript 编译错误，确保代码类型安全和依赖关系正确。..."}, "headings": [{"level": 1, "text": "useRaceApi Hook 修复报告"}, {"level": 2, "text": "修复概述"}, {"level": 2, "text": "修复的问题"}, {"level": 3, "text": "1. 导入语句清理"}, {"level": 3, "text": "2. useCallback 依赖项修复"}, {"level": 4, "text": "重置函数修复"}, {"level": 4, "text": "数据获取函数修复"}, {"level": 3, "text": "3. 缓存管理器依赖简化"}, {"level": 3, "text": "4. 复合依赖项清理"}, {"level": 2, "text": "修复后的功能验证"}, {"level": 3, "text": "TypeScript 编译"}, {"level": 3, "text": "Hook 功能完整性"}, {"level": 3, "text": "性能优化"}, {"level": 2, "text": "技术细节"}, {"level": 3, "text": "修复的 TypeScript 错误类型"}, {"level": 3, "text": "代码质量改进"}, {"level": 2, "text": "影响评估"}, {"level": 3, "text": "正面影响"}, {"level": 3, "text": "无负面影响"}, {"level": 2, "text": "建议"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "typescript", "code": "// 修复前\nimport {\n  getVisibleRaces,\n  checkRaceServiceHealth,\n  getNavigationData,\n  getGroupedConfigurationData,\n  getProcessedRulesIntroductionData,\n  getQuestionData,\n  getRankingData,\n} from \"../../services/api\";\n\n// 修复后\nimport {\n  getVisibleRaces,\n  checkRaceServiceHealth,\n  getNavigationData,\n  getGroupedConfigurationData,\n  getProcessedRulesIntroductionData,\n} from \"../../services/api\";\n"}, {"language": "typescript", "code": "// 修复前\nconst resetNavigationData = useCallback(\n  factories.createResetFunction(\n    \"导航\",\n    setNavigationData,\n    setNavigationLoading,\n    setNavigationError\n  ),\n  [factories.createResetFunction] // 错误的依赖\n);\n\n// 修复后\nconst resetNavigationData = useCallback(() => {\n  factories.createResetFunction(\n    \"导航\",\n    setNavigationData,\n    setNavigationLoading,\n    setNavigationError\n  )();\n}, [factories]); // 正确的依赖\n"}, {"language": "typescript", "code": "// 修复前\nconst fetchNavigationData = useCallback(\n    factories.createDataFetcher(...),\n    [factories.createDataFetcher] // 错误的依赖\n);\n\n// 修复后\nconst fetchNavigationData = useCallback(\n    (baseId: string) => factories.createDataFetcher(...)(baseId),\n    [factories] // 正确的依赖\n);\n"}, {"language": "typescript", "code": "// 修复前\n[\n  factories.createTableBasedDataFetcher,\n  cacheManager.getTableStructureWithCache,\n][\n  // 修复后\n  (factories, cacheManager)\n];\n"}, {"language": "typescript", "code": "// 修复前\n[\n  resetNavigationData,\n  resetConfigurationData,\n  resetRulesIntroductionData,\n  resetQuestionData,\n  resetPlayerListData,\n  cacheManager.clearTableStructureCache,\n  cacheManager.setCurrentTableStructure,\n][\n  // 修复后\n  (resetNavigationData,\n  resetConfigurationData,\n  resetRulesIntroductionData,\n  resetQuestionData,\n  resetPlayerListData,\n  cacheManager)\n];\n"}], "links": [], "isOutdated": false, "contentHash": "277f68af"}, {"fileName": "MEMORY_LEAK_FIX.md", "filePath": "/Users/<USER>/nexus-panel/docs/MEMORY_LEAK_FIX.md", "size": 2955, "lines": 162, "wordCount": 384, "lastModified": "2025-06-24T10:05:48.031Z", "categories": ["PERFORMANCE", "MQTT", "COMPONENTS", "RELEASE", "FIXES"], "summary": {"title": "MQTT 内存管理器无限循环修复报告", "description": "在实现 MQTT 内存管理器后，页面出现了严重的无限循环问题：..."}, "headings": [{"level": 1, "text": "MQTT 内存管理器无限循环修复报告"}, {"level": 2, "text": "问题描述"}, {"level": 2, "text": "问题根因分析"}, {"level": 3, "text": "1. 依赖循环问题"}, {"level": 3, "text": "2. useEffect 依赖链问题"}, {"level": 3, "text": "3. 组件卸载清理问题"}, {"level": 2, "text": "修复方案"}, {"level": 3, "text": "1. 使用 useRef 替代状态依赖"}, {"level": 3, "text": "2. 简化依赖链"}, {"level": 3, "text": "3. 优化组件卸载清理"}, {"level": 3, "text": "4. 移除自动启动逻辑"}, {"level": 2, "text": "修复后的架构"}, {"level": 3, "text": "状态管理策略"}, {"level": 3, "text": "依赖管理策略"}, {"level": 3, "text": "生命周期管理"}, {"level": 2, "text": "验证方法"}, {"level": 3, "text": "1. 构建验证"}, {"level": 1, "text": "应该无 TypeScript 错误"}, {"level": 3, "text": "2. 运行时验证"}, {"level": 3, "text": "3. 控制台日志验证"}, {"level": 2, "text": "性能影响"}, {"level": 3, "text": "修复前"}, {"level": 3, "text": "修复后"}, {"level": 2, "text": "经验教训"}, {"level": 3, "text": "1. React Hook 依赖管理"}, {"level": 3, "text": "2. 复杂状态管理"}, {"level": 3, "text": "3. 组件生命周期"}, {"level": 2, "text": "总结"}], "codeBlocks": [{"language": "unknown", "code": "Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.\n"}, {"language": "typescript", "code": "// 添加 ref 引用，避免状态依赖\nconst memoryManagerRef = useRef<MQTTMemoryManager | null>(null);\n\n// 在函数中使用 ref 而不是状态\nconst updateMemoryStats = useCallback(() => {\n  const currentManager = memoryManagerRef.current; // 使用 ref\n  if (currentManager) {\n    // ... 更新逻辑\n  }\n}, [log]); // 移除 memoryManager 依赖\n"}, {"language": "typescript", "code": "// 移除复杂的依赖关系\nconst start = useCallback((mqttService: MQTTService) => {\n  // 直接操作 ref，避免状态依赖\n  memoryManagerRef.current = newManager;\n  setMemoryManager(newManager); // 仅用于 UI 显示\n}, [config, debug, startStatsTimer, updateMemoryStats, log]);\n"}, {"language": "typescript", "code": "// 直接清理，避免依赖循环\nuseEffect(() => {\n  return () => {\n    const currentManager = memoryManagerRef.current;\n    if (currentManager) {\n      currentManager.stop();\n      memoryManagerRef.current = null;\n    }\n    \n    if (statsTimerRef.current) {\n      clearInterval(statsTimerRef.current);\n      statsTimerRef.current = null;\n    }\n  };\n}, []); // 空依赖数组\n"}, {"language": "typescript", "code": "// 移除复杂的自动启动 useEffect\n// 改为在 App.tsx 中手动控制启动时机\n"}, {"language": "bash", "code": "npm run build\n# 应该无 TypeScript 错误\n"}, {"language": "unknown", "code": "[MQTTMemoryManager] 内存管理器已初始化\n[MQTTMemoryManager] 内存管理器已启动\n[useMQTTMemoryManager] 统计更新定时器已启动\n[useMQTTMemoryManager] 内存管理器已启动\n[useMQTTMemoryManager] 日志计数回调已设置\n[useMQTTMemoryManager] 日志清理回调已设置\n"}], "links": [], "isOutdated": false, "contentHash": "44adc8cd"}, {"fileName": "TreeView-CSS-Analysis.md", "filePath": "/Users/<USER>/nexus-panel/docs/TreeView-CSS-Analysis.md", "size": 2955, "lines": 157, "wordCount": 401, "lastModified": "2025-06-12T10:32:41.457Z", "categories": ["COMPONENTS"], "summary": {"title": "React Spectrum TreeView CSS 类名分析报告", "description": "本报告详细分析了 React Spectrum TreeView 组件的 CSS 类名结构和各种交互状态的样式定制方法。..."}, "headings": [{"level": 1, "text": "React Spectrum TreeView CSS 类名分析报告"}, {"level": 2, "text": "概述"}, {"level": 2, "text": "1. React Spectrum TreeView 架构分析"}, {"level": 3, "text": "组件结构"}, {"level": 3, "text": "CSS 类名生成方式"}, {"level": 2, "text": "2. 交互状态的 CSS 选择器"}, {"level": 3, "text": "2.1 基于 ARIA 属性的选择器（推荐）"}, {"level": 4, "text": "Hover 状态 - 鼠标悬停"}, {"level": 4, "text": "Selected 状态 - 选中项"}, {"level": 4, "text": "Expanded 状态 - 展开节点"}, {"level": 4, "text": "Focus 状态 - 焦点状态"}, {"level": 3, "text": "2.2 基于 CSS Modules 类名的选择器（备用方案）"}, {"level": 2, "text": "3. 发现的实际 CSS 类名"}, {"level": 3, "text": "现有类名分析"}, {"level": 3, "text": "类名特征"}, {"level": 2, "text": "4. 完整的交互状态样式方案"}, {"level": 3, "text": "4.1 基础交互状态"}, {"level": 3, "text": "4.2 组合状态"}, {"level": 3, "text": "4.3 特殊节点样式"}, {"level": 2, "text": "5. 可访问性和响应式增强"}, {"level": 3, "text": "5.1 高对比度模式支持"}, {"level": 3, "text": "5.2 减少动画偏好支持"}, {"level": 2, "text": "6. 实施建议"}, {"level": 3, "text": "6.1 优先级策略"}, {"level": 3, "text": "6.2 维护性考虑"}, {"level": 2, "text": "7. 测试验证"}, {"level": 3, "text": "7.1 交互测试"}, {"level": 3, "text": "7.2 兼容性测试"}, {"level": 2, "text": "8. 结论"}], "codeBlocks": [{"language": "css", "code": ".navigation-tree-view [role=\"treeitem\"]:hover {\n  background-color: rgba(0, 110, 239, 0.1);\n  cursor: pointer;\n}\n"}, {"language": "css", "code": ".navigation-tree-view [role=\"treeitem\"][aria-selected=\"true\"] {\n  background-color: #006EEF;\n  color: #ffffff;\n}\n"}, {"language": "css", "code": ".navigation-tree-view [role=\"treeitem\"][aria-expanded=\"true\"] {\n  border-left: 2px solid rgba(0, 110, 239, 0.3);\n}\n"}, {"language": "css", "code": ".navigation-tree-view [role=\"treeitem\"]:focus-visible {\n  outline: 2px solid #0078d4;\n  outline-offset: -2px;\n}\n"}, {"language": "css", "code": "/* 悬停状态 */\n.navigation-tree-view [class*=\"s1-\"]:hover {\n  background-color: rgba(0, 110, 239, 0.1) !important;\n}\n\n/* 选中状态 */\n.navigation-tree-view [class*=\"s1-\"][aria-selected=\"true\"] {\n  background-color: #006EEF !important;\n  color: #ffffff !important;\n}\n\n/* 焦点状态 */\n.navigation-tree-view [class*=\"s1-\"]:focus-visible {\n  outline: 2px solid #0078d4 !important;\n  outline-offset: -2px !important;\n}\n"}, {"language": "css", "code": "@media (prefers-contrast: high) {\n  .navigation-tree-view [role=\"treeitem\"][aria-selected=\"true\"] {\n    background-color: #000000;\n    color: #ffffff;\n    border: 2px solid #ffffff;\n  }\n}\n"}, {"language": "css", "code": "@media (prefers-reduced-motion: reduce) {\n  .navigation-tree-view [role=\"treeitem\"] {\n    transition: none;\n  }\n}\n"}], "links": [], "isOutdated": false, "contentHash": "-56f03b9"}, {"fileName": "MQTT_DOCUMENTATION_INDEX.md", "filePath": "/Users/<USER>/nexus-panel/docs/MQTT_DOCUMENTATION_INDEX.md", "size": 2872, "lines": 148, "wordCount": 330, "lastModified": "2025-06-24T05:48:42.294Z", "categories": ["MQTT", "COMPONENTS", "FIXES"], "summary": {"title": "MQTT组件文档索引", "description": "本索引提供nexus-panel项目MQTT组件的完整文档导航，包括分析报告、使用手册、集成指南等。..."}, "headings": [{"level": 1, "text": "MQTT组件文档索引"}, {"level": 2, "text": "📚 文档概述"}, {"level": 2, "text": "📋 文档清单"}, {"level": 3, "text": "1. 核心分析文档"}, {"level": 4, "text": "📊 [MQTT_COMPONENT_ANALYSIS.md](./MQTT_COMPONENT_ANALYSIS.md)"}, {"level": 4, "text": "🏆 [MQTT_CODE_QUALITY_REPORT.md](./MQTT_CODE_QUALITY_REPORT.md)"}, {"level": 3, "text": "2. 使用指南文档"}, {"level": 4, "text": "📖 [MQTT_DEVELOPER_GUIDE.md](./MQTT_DEVELOPER_GUIDE.md)"}, {"level": 4, "text": "🔧 [MQTT_APP_INTEGRATION_EXAMPLE.md](./MQTT_APP_INTEGRATION_EXAMPLE.md)"}, {"level": 3, "text": "3. 可视化图表"}, {"level": 4, "text": "🏗️ MQTT组件架构图"}, {"level": 4, "text": "🔄 MQTT消息数据流图"}, {"level": 2, "text": "🎯 文档使用建议"}, {"level": 3, "text": "新手入门路径"}, {"level": 3, "text": "技术评估路径"}, {"level": 3, "text": "集成实施路径"}, {"level": 2, "text": "📈 项目状态总结"}, {"level": 3, "text": "当前状态"}, {"level": 3, "text": "技术评估"}, {"level": 3, "text": "核心优势"}, {"level": 3, "text": "改进建议"}, {"level": 2, "text": "🔗 相关资源"}, {"level": 3, "text": "技术文档"}, {"level": 3, "text": "项目文件"}, {"level": 3, "text": "配置信息"}, {"level": 2, "text": "📞 支持信息"}], "codeBlocks": [], "links": [{"text": "MQTT_COMPONENT_ANALYSIS.md", "url": "./MQTT_COMPONENT_ANALYSIS.md"}, {"text": "MQTT_CODE_QUALITY_REPORT.md", "url": "./MQTT_CODE_QUALITY_REPORT.md"}, {"text": "MQTT_DEVELOPER_GUIDE.md", "url": "./MQTT_DEVELOPER_GUIDE.md"}, {"text": "MQTT_APP_INTEGRATION_EXAMPLE.md", "url": "./MQTT_APP_INTEGRATION_EXAMPLE.md"}, {"text": "MQTT 3.1.1 协议规范", "url": "https://docs.oasis-open.org/mqtt/mqtt/v3.1.1/mqtt-v3.1.1.html"}, {"text": "mqtt.js 官方文档", "url": "https://github.com/mqttjs/MQTT.js"}, {"text": "React Hooks 最佳实践", "url": "https://reactjs.org/docs/hooks-intro.html"}], "isOutdated": false, "contentHash": "153b605d"}, {"fileName": "SidebarButtonStyles-QuickReference.md", "filePath": "/Users/<USER>/nexus-panel/docs/SidebarButtonStyles-QuickReference.md", "size": 2656, "lines": 127, "wordCount": 402, "lastModified": "2025-06-18T09:47:57.503Z", "categories": ["COMPONENTS"], "summary": {"title": "侧边栏按钮样式系统 - 快速参考", "description": "| 样式类型 | 用途 | 视觉特征 | 推荐场景 |..."}, "headings": [{"level": 1, "text": "侧边栏按钮样式系统 - 快速参考"}, {"level": 2, "text": "🎨 样式类型速查表"}, {"level": 2, "text": "🚀 快速使用"}, {"level": 3, "text": "基础用法"}, {"level": 3, "text": "自动推荐"}, {"level": 3, "text": "混合使用"}, {"level": 2, "text": "📝 常用按钮文本映射"}, {"level": 2, "text": "🎯 最佳实践"}, {"level": 3, "text": "✅ 推荐做法"}, {"level": 3, "text": "❌ 避免做法"}, {"level": 2, "text": "🔧 自定义样式"}, {"level": 3, "text": "CSS类名"}, {"level": 3, "text": "自定义示例"}, {"level": 2, "text": "🔄 迁移指南"}, {"level": 3, "text": "从旧版本迁移"}, {"level": 3, "text": "渐进式迁移"}, {"level": 2, "text": "🐛 故障排除"}, {"level": 3, "text": "样式不生效"}, {"level": 3, "text": "TypeScript 错误"}, {"level": 2, "text": "📞 技术支持"}], "codeBlocks": [{"language": "typescript", "code": "import { ButtonStyleType } from './components/SidebarButtonStyles';\n\nconst buttons: ButtonConfig[] = [\n  { text: '跳转', styleType: ButtonStyleType.PRIMARY_ACTION },\n  { text: '上一题', styleType: ButtonStyleType.NAVIGATION },\n  { text: '正确答案', styleType: ButtonStyleType.CONTENT_DISPLAY }\n];\n"}, {"language": "typescript", "code": "import { suggestButtonStyleType } from './components/SidebarButtonStyles';\n\nconst button = {\n  text: '上一题',\n  styleType: suggestButtonStyleType('上一题') // 自动返回 NAVIGATION\n};\n"}, {"language": "typescript", "code": "const buttons: ButtonConfig[] = [\n  { text: '自定义', styleType: ButtonStyleType.PRIMARY_ACTION, variant: 'secondary' },\n  { text: '传统', variant: 'accent' } // 不使用styleType\n];\n"}, {"language": "css", "code": ".sidebar-button-primary-action { /* 主要操作 */ }\n.sidebar-button-navigation { /* 导航操作 */ }\n.sidebar-button-content-display { /* 内容展示 */ }\n.sidebar-button-data-operation { /* 数据操作 */ }\n.sidebar-button-special-function { /* 特殊功能 */ }\n.sidebar-button-danger-action { /* 危险操作 */ }\n.sidebar-button-info-display { /* 信息展示 */ }\n"}, {"language": "css", "code": ".sidebar-button-primary-action {\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n  border-radius: 8px;\n}\n"}, {"language": "typescript", "code": "// 旧方式 ❌\n{ text: '跳转', variant: 'accent' }\n\n// 新方式 ✅\n{ text: '跳转', styleType: ButtonStyleType.PRIMARY_ACTION }\n"}], "links": [], "isOutdated": false, "contentHash": "-3da2719d"}, {"fileName": "ICON_UPDATE_SUMMARY.md", "filePath": "/Users/<USER>/nexus-panel/docs/ICON_UPDATE_SUMMARY.md", "size": 2375, "lines": 128, "wordCount": 264, "lastModified": "2025-06-19T07:07:27.685Z", "categories": ["COMPONENTS", "ARCHITECTURE", "RELEASE"], "summary": {"title": "AudioPlayer 图标更新总结", "description": "根据用户要求，对AudioPlayer组件中的图标使用逻辑进行了以下修改：..."}, "headings": [{"level": 1, "text": "AudioPlayer 图标更新总结"}, {"level": 2, "text": "🎯 修改目标"}, {"level": 2, "text": "✅ 已完成的修改"}, {"level": 3, "text": "1. 图标导入更新"}, {"level": 3, "text": "2. 播放/暂停按钮图标切换逻辑"}, {"level": 3, "text": "3. 音量按钮图标替换"}, {"level": 2, "text": "🔍 图标可用性验证"}, {"level": 2, "text": "📋 修改的具体位置"}, {"level": 3, "text": "文件：`nexus-panel/src/components/AudioPlayer.tsx`"}, {"level": 2, "text": "✅ 验证结果"}, {"level": 3, "text": "编译测试"}, {"level": 3, "text": "功能测试"}, {"level": 3, "text": "代码质量"}, {"level": 2, "text": "🎨 视觉效果"}, {"level": 3, "text": "播放控制"}, {"level": 3, "text": "音量控制"}, {"level": 2, "text": "📚 相关文档更新"}, {"level": 2, "text": "🚀 部署状态"}], "codeBlocks": [{"language": "typescript", "code": "import Bell from '@spectrum-icons/workflow/Bell';\nimport Info from '@spectrum-icons/workflow/Info';\n"}, {"language": "typescript", "code": "import VolumeThree from '@spectrum-icons/workflow/VolumeThree';\nimport VolumeMute from '@spectrum-icons/workflow/VolumeMute';\n"}, {"language": "typescript", "code": "{isPlaying ? <Pause /> : <Play />}\n"}, {"language": "typescript", "code": "{isMuted ? <Info /> : <Bell />}\n"}, {"language": "typescript", "code": "{isMuted ? <VolumeMute /> : <VolumeThree />}\n"}, {"language": "bash", "code": "$ find node_modules/@spectrum-icons/workflow -name \"*.js\" | grep -i volume\nnode_modules/@spectrum-icons/workflow/VolumeMute.js      ✅ 已使用\nnode_modules/@spectrum-icons/workflow/VolumeTwo.js       ⚪ 可选替代\nnode_modules/@spectrum-icons/workflow/VolumeThree.js     ✅ 已使用\nnode_modules/@spectrum-icons/workflow/VolumeOne.js       ⚪ 可选替代\n"}, {"language": "typescript", "code": "   import VolumeThree from '@spectrum-icons/workflow/VolumeThree';\n   import VolumeMute from '@spectrum-icons/workflow/VolumeMute';\n   "}, {"language": "typescript", "code": "   {isPlaying ? <Pause /> : <Play />}\n   "}, {"language": "typescript", "code": "   {isMuted ? <VolumeMute /> : <VolumeThree />}\n   "}], "links": [], "isOutdated": false, "contentHash": "-402c4c2e"}, {"fileName": "ranking_pagination_task_progress.md", "filePath": "/Users/<USER>/nexus-panel/docs/ranking_pagination_task_progress.md", "size": 2155, "lines": 110, "wordCount": 282, "lastModified": "2025-07-11T06:59:42.095Z", "categories": ["API", "COMPONENTS", "TASKS"], "summary": {"title": "RankingContent 分页功能实施进度", "description": "2025-01-11..."}, "headings": [{"level": 1, "text": "RankingContent 分页功能实施进度"}, {"level": 2, "text": "任务完成时间"}, {"level": 2, "text": "已完成的检查清单项目"}, {"level": 3, "text": "✅ 1. 扩展类型定义"}, {"level": 3, "text": "✅ 2. 修改ContentArea组件"}, {"level": 3, "text": "✅ 3. 计算分页数据"}, {"level": 3, "text": "✅ 4. 添加页面切换处理器"}, {"level": 3, "text": "✅ 5. 修改RankingContent组件"}, {"level": 3, "text": "✅ 6. 添加页码显示区域"}, {"level": 3, "text": "✅ 7. 创建动态按钮组配置函数"}, {"level": 3, "text": "✅ 8. 添加分页按钮事件处理器"}, {"level": 3, "text": "✅ 9. 实现按钮禁用逻辑"}, {"level": 3, "text": "✅ 10. 集成分页按钮组到App"}, {"level": 3, "text": "✅ 11. 添加数据更新时的分页重置"}, {"level": 3, "text": "✅ 12. 测试边界情况处理"}, {"level": 2, "text": "核心功能实现"}, {"level": 3, "text": "分页显示逻辑"}, {"level": 3, "text": "分页控制集成"}, {"level": 3, "text": "技术实现"}, {"level": 3, "text": "UI交互"}, {"level": 2, "text": "技术架构说明"}, {"level": 3, "text": "状态管理层级"}, {"level": 3, "text": "数据流"}, {"level": 3, "text": "按钮组集成"}, {"level": 2, "text": "完成状态"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "-642acd8c"}, {"fileName": "HOOK_FIXES_SUMMARY.md", "filePath": "/Users/<USER>/nexus-panel/docs/HOOK_FIXES_SUMMARY.md", "size": 2073, "lines": 124, "wordCount": 300, "lastModified": "2025-07-15T12:30:10.863Z", "categories": ["API", "FIXES", "ARCHITECTURE"], "summary": {"title": "Hook 修复总结", "description": "**修复时间：** 2025-01-15  ..."}, "headings": [{"level": 1, "text": "Hook 修复总结"}, {"level": 2, "text": "修复概述"}, {"level": 2, "text": "修复的文件"}, {"level": 3, "text": "1. 缓存管理器优化"}, {"level": 3, "text": "2. 争分夺秒 Hook 优化"}, {"level": 3, "text": "3. 排名 API 优化"}, {"level": 3, "text": "4. 主 Hook 集成优化"}, {"level": 2, "text": "修复效果"}, {"level": 2, "text": "核心改进"}, {"level": 2, "text": "验证方法"}, {"level": 3, "text": "5. 全局请求去重器"}, {"level": 3, "text": "6. API 服务层集成"}, {"level": 2, "text": "🔧 最终架构"}, {"level": 2, "text": "🐛 调试信息"}, {"level": 3, "text": "9. Race API 单例架构重构 ✅"}, {"level": 2, "text": "🏗️ 最终架构优化"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "6d4bbea3"}, {"fileName": "UPDATE_LOG_v0.2.md", "filePath": "/Users/<USER>/nexus-panel/docs/UPDATE_LOG_v0.2.md", "size": 1653, "lines": 108, "wordCount": 213, "lastModified": "2025-06-19T10:04:02.806Z", "categories": ["COMPONENTS", "RELEASE", "API"], "summary": {"title": "🎯 Nexus Panel v0.2.0 更新日志", "description": "**专为题目配音和规则解说打造的专业播放器**..."}, "headings": [{"level": 1, "text": "🎯 Nexus Panel v0.2.0 更新日志"}, {"level": 2, "text": "✨ 重要更新亮点"}, {"level": 3, "text": "🎵 全新音频播放器组件"}, {"level": 3, "text": "🎨 按钮样式系统全面升级"}, {"level": 2, "text": "🔧 技术改进"}, {"level": 3, "text": "音频播放器技术特性"}, {"level": 3, "text": "按钮样式系统技术特性"}, {"level": 2, "text": "🎯 用户体验提升"}, {"level": 3, "text": "界面布局优化"}, {"level": 3, "text": "操作体验改进"}, {"level": 2, "text": "🌐 兼容性支持"}, {"level": 3, "text": "浏览器兼容性"}, {"level": 3, "text": "设备适配"}, {"level": 2, "text": "📋 版本信息"}, {"level": 2, "text": "🚀 快速体验"}, {"level": 3, "text": "音频播放器使用"}, {"level": 3, "text": "按钮样式识别"}, {"level": 2, "text": "💡 使用建议"}, {"level": 3, "text": "最佳实践"}, {"level": 3, "text": "注意事项"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "6d278e87"}, {"fileName": "TimeRaceRanking_Performance_Test.md", "filePath": "/Users/<USER>/nexus-panel/docs/TimeRaceRanking_Performance_Test.md", "size": 1542, "lines": 105, "wordCount": 156, "lastModified": "2025-07-15T05:57:03.246Z", "categories": ["API", "COMPONENTS", "PERFORMANCE", "FIXES"], "summary": {"title": "TimeRaceRankingContent 性能优化验证", "description": "本次优化实现了分层缓存架构，将数据分为静态层（选手列表）和动态层（答题记录），采用不同的缓存和更新策略。..."}, "headings": [{"level": 1, "text": "TimeRaceRankingContent 性能优化验证"}, {"level": 2, "text": "优化概述"}, {"level": 2, "text": "优化前后对比"}, {"level": 3, "text": "优化前"}, {"level": 3, "text": "优化后"}, {"level": 2, "text": "性能测试场景"}, {"level": 3, "text": "场景1：组件初始化"}, {"level": 3, "text": "场景2：轮询更新"}, {"level": 3, "text": "场景3：错误恢复"}, {"level": 2, "text": "性能指标"}, {"level": 3, "text": "API调用优化"}, {"level": 3, "text": "内存使用优化"}, {"level": 3, "text": "用户体验优化"}, {"level": 2, "text": "验证方法"}, {"level": 3, "text": "开发者工具验证"}, {"level": 3, "text": "日志验证"}, {"level": 3, "text": "代码审查验证"}, {"level": 2, "text": "预期收益"}, {"level": 2, "text": "注意事项"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "68c44e25"}, {"fileName": "SIMPLE_UPDATE_LOG.md", "filePath": "/Users/<USER>/nexus-panel/docs/SIMPLE_UPDATE_LOG.md", "size": 1434, "lines": 83, "wordCount": 168, "lastModified": "2025-06-19T10:04:41.851Z", "categories": ["COMPONENTS", "RELEASE", "API"], "summary": {"title": "🎯 Nexus Panel v0.2.0 更新", "description": "**专为题目配音和规则解说设计的专业播放器**..."}, "headings": [{"level": 1, "text": "🎯 Nexus Panel v0.2.0 更新"}, {"level": 2, "text": "🆕 新增功能"}, {"level": 3, "text": "🎵 音频播放器组件"}, {"level": 3, "text": "🎨 按钮样式系统升级"}, {"level": 2, "text": "🔧 技术改进"}, {"level": 3, "text": "音频播放器"}, {"level": 3, "text": "按钮样式系统"}, {"level": 2, "text": "🎯 用户体验提升"}, {"level": 3, "text": "界面布局优化"}, {"level": 3, "text": "操作体验改进"}, {"level": 2, "text": "📱 设备兼容"}, {"level": 3, "text": "浏览器支持"}, {"level": 3, "text": "设备适配"}, {"level": 2, "text": "🚀 快速上手"}, {"level": 3, "text": "音频播放器使用"}, {"level": 3, "text": "按钮样式识别"}, {"level": 2, "text": "📋 版本信息"}], "codeBlocks": [], "links": [], "isOutdated": false, "contentHash": "-78d609ce"}, {"fileName": "Tasks_2025-07-14T08-27-38.md", "filePath": "/Users/<USER>/nexus-panel/docs/Tasks_2025-07-14T08-27-38.md", "size": 592, "lines": 19, "wordCount": 37, "lastModified": "2025-07-14T08:27:38.710Z", "categories": ["API", "TASKS"], "summary": {"title": "No title found", "description": "[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__..."}, "headings": [], "codeBlocks": [], "links": [], "isOutdated": true, "contentHash": "55a1aae0"}], "categories": {"API": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "Integration_Verification.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_DEVELOPER_GUIDE.md", "PLAYER_DATA_AUTO_FETCH.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "SIMPLE_UPDATE_LOG.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "Tasks_2025-07-14T08-27-38.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "UPDATE_LOG_v0.2.md", "USERACEAPI_HOOK_FIX.md", "ranking_pagination_task_progress.md"], "FIXES": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "MQTT_MEMORY_MANAGEMENT.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "USERACEAPI_HOOK_FIX.md"], "ARCHITECTURE": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "ICON_UPDATE_SUMMARY.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "USERACEAPI_HOOK_FIX.md"], "COMPONENTS": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "ICON_UPDATE_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "PLAYER_DATA_AUTO_FETCH.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "SidebarButtonStyles-QuickReference.md", "SidebarButtonStyles.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "TreeView-CSS-Analysis.md", "UPDATE_LOG_v0.2.md", "USERACEAPI_HOOK_FIX.md", "ranking_pagination_task_progress.md"], "MQTT": ["API_ENDPOINTS_ANALYSIS.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "MQTT_MEMORY_MANAGEMENT.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "TimeRaceRanking_Usage.md"], "PERFORMANCE": ["API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "INFINITE_LOOP_FIX_V2.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_MEMORY_MANAGEMENT.md", "REALTIME_LOG_COUNT_FIX.md", "TimeRaceRanking_Performance_Test.md"], "RELEASE": ["API_ENDPOINTS_ANALYSIS.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "ICON_UPDATE_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_MEMORY_MANAGEMENT.md", "REALTIME_LOG_COUNT_FIX.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TimeRaceRanking_Refactor_Comparison.md", "UPDATE_LOG_v0.2.md"], "TASKS": ["AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "Integration_Verification.md", "Tasks_2025-07-14T08-27-38.md", "ranking_pagination_task_progress.md"]}, "duplicates": [{"type": "similar", "files": ["API_ENDPOINTS_ANALYSIS.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "UPDATE_LOG_v0.2.md"], "reason": "Similar topic: \"nexus\""}, {"type": "similar", "files": ["API_ENDPOINTS_ANALYSIS.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "UPDATE_LOG_v0.2.md"], "reason": "Similar topic: \"panel\""}, {"type": "similar", "files": ["AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "ICON_UPDATE_SUMMARY.md"], "reason": "Similar topic: \"audioplayer\""}, {"type": "similar", "files": ["HOOK_FIXES_SUMMARY.md", "USERACEAPI_HOOK_FIX.md"], "reason": "Similar topic: \"hook\""}, {"type": "similar", "files": ["INFINITE_LOOP_FIX_V2.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_MEMORY_MANAGEMENT.md"], "reason": "Similar topic: \"mqtt\""}, {"type": "similar", "files": ["RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "UPDATE_LOG_v0.2.md"], "reason": "Similar topic: \"v0.2.0\""}, {"type": "similar", "files": ["TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md"], "reason": "Similar topic: \"timeracerankingcontent\""}], "outdated": [{"fileName": "Tasks_2025-07-14T08-27-38.md", "reasons": ["Old task file"], "lastModified": "2025-07-14T08:27:38.710Z"}], "migrationPlan": [{"target": "API_GUIDE.md", "sources": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "Integration_Verification.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_DEVELOPER_GUIDE.md", "PLAYER_DATA_AUTO_FETCH.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "SIMPLE_UPDATE_LOG.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "Tasks_2025-07-14T08-27-38.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "UPDATE_LOG_v0.2.md", "USERACEAPI_HOOK_FIX.md", "ranking_pagination_task_progress.md"], "description": "Consolidated API documentation including endpoints, testing, and architecture", "action": "merge"}, {"target": "MQTT_GUIDE.md", "sources": ["API_ENDPOINTS_ANALYSIS.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "MQTT_MEMORY_MANAGEMENT.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "TimeRaceRanking_Usage.md"], "description": "Complete MQTT integration and development guide", "action": "merge"}, {"target": "COMPONENTS_GUIDE.md", "sources": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "ICON_UPDATE_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "PLAYER_DATA_AUTO_FETCH.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "SidebarButtonStyles-QuickReference.md", "SidebarButtonStyles.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "TreeView-CSS-Analysis.md", "UPDATE_LOG_v0.2.md", "USERACEAPI_HOOK_FIX.md", "ranking_pagination_task_progress.md"], "description": "UI components development and styling guide", "action": "merge"}, {"target": "TROUBLESHOOTING.md", "sources": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "Integration_Verification.md", "MEMORY_LEAK_FIX.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "MQTT_DOCUMENTATION_INDEX.md", "MQTT_MEMORY_MANAGEMENT.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "REALTIME_LOG_COUNT_FIX.md", "SECTION_DATA_DUPLICATE_REQUESTS_ANALYSIS.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TIMERACE_RANKING_DATA_ISSUE_ANALYSIS.md", "TimeRaceRanking_Performance_Test.md", "TimeRaceRanking_Refactor_Comparison.md", "TimeRaceRanking_Usage.md", "USERACEAPI_HOOK_FIX.md"], "description": "Consolidated troubleshooting and bug fix documentation", "action": "merge"}, {"target": "PERFORMANCE_OPTIMIZATION.md", "sources": ["API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "INFINITE_LOOP_FIX_V2.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_MEMORY_MANAGEMENT.md", "REALTIME_LOG_COUNT_FIX.md", "TimeRaceRanking_Performance_Test.md"], "description": "Performance analysis and optimization guide", "action": "merge"}, {"target": "ARCHITECTURE.md", "sources": ["API_DUPLICATE_REQUESTS_ANALYSIS.md", "API_ENDPOINTS_ANALYSIS.md", "API_REQUEST_TESTING_GUIDE.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "HOOK_FIXES_SUMMARY.md", "ICON_UPDATE_SUMMARY.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_CODE_QUALITY_REPORT.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_DEVELOPER_GUIDE.md", "RACE_API_SINGLETON_ARCHITECTURE.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "USERACEAPI_HOOK_FIX.md"], "description": "System architecture and design documentation", "action": "merge"}, {"target": "RELEASE_NOTES.md", "sources": ["API_ENDPOINTS_ANALYSIS.md", "AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "ICON_UPDATE_SUMMARY.md", "INFINITE_LOOP_FIX_V2.md", "MEMORY_LEAK_FIX.md", "MEMORY_STATS_EXPLANATION.md", "MQTT_APP_INTEGRATION_EXAMPLE.md", "MQTT_COMPONENT_ANALYSIS.md", "MQTT_MEMORY_MANAGEMENT.md", "REALTIME_LOG_COUNT_FIX.md", "RELEASE_NOTES.md", "SIMPLE_UPDATE_LOG.md", "TIMERACE_RANKING_API_FLOW_ANALYSIS.md", "TimeRaceRanking_Refactor_Comparison.md", "UPDATE_LOG_v0.2.md"], "description": "Consolidated release notes and version history", "action": "merge"}, {"target": "archive/", "sources": ["AUDIO_PLAYER_RELEASE.md", "AudioPlayer.md", "Integration_Verification.md", "Tasks_2025-07-14T08-27-38.md", "ranking_pagination_task_progress.md"], "description": "Archived task files and outdated documentation", "action": "archive"}]}