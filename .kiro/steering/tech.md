# 技术栈

## 构建系统与开发环境

- **构建工具**: Vite 6.3.5 配合 React SWC 插件，实现快速开发和构建
- **包管理器**: npm（存在 package-lock.json）
- **TypeScript**: 5.8.3 配合严格配置
- **Node 目标**: ES2020 支持现代浏览器

## 核心技术

### 前端框架

- **React**: 19.1.0 配合 React DOM 19.1.0
- **TypeScript**: 完整类型安全，严格编译器选项
- **JSX**: react-jsx 转换（无需导入 React）

### UI 框架

- **Adobe React Spectrum**: 3.42.1 - 主要设计系统
- **Spectrum Icons**: 工作流和插图包
- **Motion**: 12.23.0 用于动画和过渡效果

### 实时通信

- **MQTT**: 5.13.1 用于实时消息传递
- **WebSocket**: 内置于 MQTT 客户端，用于实时更新

### 开发工具

- **ESLint**: 9.25.0 配合 TypeScript ESLint 集成
- **Prettier**: 3.6.0 用于代码格式化
- **Vite 开发服务器**: 热模块替换和快速刷新

## 常用命令

```bash
# 开发
npm run dev          # 启动开发服务器，支持 HMR

# 构建
npm run build        # TypeScript 编译 + Vite 构建
npm run preview      # 本地预览生产构建

# 代码质量
npm run lint         # 对所有文件运行 ESLint
```

## 架构模式

### 数据-UI 分离

- 使用自定义 hooks 进行数据管理和业务逻辑
- 纯展示组件接收 props
- 需要时使用容器组件进行数据-UI 桥接

### 服务层

- API 服务位于 `src/services/api/`
- MQTT 服务位于 `src/services/mqtt/`
- 集中式错误处理和请求管理

### 配置驱动

- 基于配置的动态组件渲染
- 集中配置文件位于 `src/config/`
- 来自 API 端点的运行时配置
