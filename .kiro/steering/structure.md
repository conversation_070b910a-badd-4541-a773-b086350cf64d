# 项目结构

## 根级目录组织

```
nexus-panel/                    # 主应用程序目录
├── src/                        # 源代码
├── public/                     # 静态资源
├── dist/                       # 构建输出
├── node_modules/               # 依赖项
└── docs/                       # 文档（项目根目录）
```

## 源代码结构 (`src/`)

### 核心应用文件

- `main.tsx` - 应用程序入口点，包含提供者
- `App.tsx` - 主应用组件（1500+ 行，中央状态管理）
- `index.css` - 全局样式
- `vite-env.d.ts` - Vite 类型定义

### 组件组织

```
components/
├── common/                     # 可复用工具组件
│   ├── DynamicComponentRenderer.tsx
│   ├── ImageWithRetry.tsx
│   └── index.ts
├── layout/                     # 布局组件
├── question/                   # 题目相关组件
├── ContentToggle/              # 功能特定组件组
└── [ComponentName].tsx         # 单独组件
└── [ComponentName].css         # 组件特定样式
```

### 业务逻辑层

```
hooks/                          # 自定义 React hooks
├── useApi.ts                   # 通用 API hook
├── useRaceApi.ts              # 竞赛特定 API 操作
├── useMQTT*.ts                # MQTT 集成 hooks
├── use*Navigation.ts          # 导航相关 hooks
└── use*Manager.ts             # 状态管理 hooks
```

### 服务层

```
services/
├── api/                       # HTTP API 服务
│   ├── client.ts              # HTTP 客户端配置
│   ├── errors.ts              # 错误处理
│   ├── types.ts               # API 类型定义
│   └── [feature]Api.ts        # 功能特定 API 模块
└── mqtt/                      # MQTT 服务
    ├── MQTTService.ts         # 核心 MQTT 实现
    ├── MQTTMemoryManager.ts   # 内存管理
    └── types.ts               # MQTT 类型定义
```

### 配置与类型

```
config/                        # 配置文件
├── buttonGroupConfigurations.ts
├── componentConfigurations.ts
├── navigationConfigurations.ts
└── motionConfig.ts

types/                         # 类型定义
├── motion.ts
└── ultimatePK.ts

contexts/                      # React 上下文
└── UserInteractionContext.tsx
```

### 工具函数

```
utils/                         # 工具函数
├── globalTimerManager.ts      # 计时器管理
├── requestManager.ts          # 请求队列管理
├── rankingUtils.ts           # 排名计算
└── timeRaceUtils.ts          # 争分夺秒工具
```

## 关键架构模式

### 文件命名约定

- **组件**: PascalCase（例如：`TimeRaceTimer.tsx`）
- **Hooks**: camelCase 带 `use` 前缀（例如：`useRaceApi.ts`）
- **服务**: 类使用 PascalCase（例如：`MQTTService.ts`）
- **类型**: 文件使用 camelCase，接口使用 PascalCase
- **CSS**: 匹配组件名称（例如：`TimeRaceTimer.css`）

### 导入组织

1. React 核心依赖优先
2. 第三方库（Adobe Spectrum、Motion 等）
3. 内部服务和 hooks
4. 类型和配置
5. 样式最后

### 组件共置

- 需要时组件有对应的 CSS 文件
- 相关组件按功能分组到文件夹
- 共享组件放在 `common/` 目录

### 状态管理模式

- 中央状态在 `App.tsx`（1500+ 行）
- 功能特定状态使用自定义 hooks
- 跨切面关注点使用上下文提供者
- 实时更新使用 MQTT 集成

## 开发规范

### 代码风格

- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- 组件使用函数式组件和 hooks
- 优先使用 Adobe React Spectrum 组件

### 性能优化

- 使用 React.memo 优化重渲染
- 合理使用 useCallback 和 useMemo
- 避免在渲染函数中创建对象和函数
- MQTT 连接复用和内存管理

### 错误处理

- 统一的 API 错误处理机制
- 组件级错误边界
- 用户友好的错误提示
- 详细的日志记录系统
