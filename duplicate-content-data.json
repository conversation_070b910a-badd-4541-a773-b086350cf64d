{"exactDuplicates": [], "similarContent": [], "duplicateHeadings": [{"heading": "概述", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "概述"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "概述"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "概述"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "概述"}, {"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "概述"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "概述"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "概述"}, {"file": "README.md", "level": 2, "text": "概述"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "概述"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "概述"}], "count": 10}, {"heading": "目录", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "目录"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "目录"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "目录"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "目录"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "目录"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "目录"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "目录"}], "count": 7}, {"heading": "架构设计", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "架构设计"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "架构设计"}], "count": 2}, {"heading": "系统架构", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "系统架构"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "系统架构"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "系统架构"}], "count": 3}, {"heading": "基础配置", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "基础配置"}, {"file": "API_GUIDE.md", "level": 3, "text": "基础配置"}], "count": 2}, {"heading": "性能优化", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "性能优化"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "性能优化"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "性能优化"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "性能优化"}], "count": 6}, {"heading": "性能监控", "occurrences": [{"file": "API_GUIDE.md", "level": 3, "text": "性能监控"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "性能监控"}], "count": 2}, {"heading": "错误处理策略", "occurrences": [{"file": "API_GUIDE.md", "level": 4, "text": "错误处理策略"}, {"file": "API_GUIDE.md", "level": 4, "text": "错误处理策略"}], "count": 2}, {"heading": "故障排除", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "故障排除"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "故障排除"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "故障排除"}], "count": 3}, {"heading": "最佳实践", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "最佳实践"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "最佳实践"}], "count": 2}, {"heading": "测试指南", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "测试指南"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "测试指南"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "测试指南"}], "count": 3}, {"heading": "相关文档", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "相关文档"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "相关文档"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "相关文档"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "相关文档"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "相关文档"}], "count": 8}, {"heading": "更新历史", "occurrences": [{"file": "API_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "ARCHITECTURE.md", "level": 2, "text": "更新历史"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "更新历史"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "更新历史"}, {"file": "README.md", "level": 2, "text": "更新历史"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "更新历史"}, {"file": "TROUBLESHOOTING.md", "level": 2, "text": "更新历史"}], "count": 9}, {"heading": "技术栈", "occurrences": [{"file": "ARCHITECTURE.md", "level": 2, "text": "技术栈"}, {"file": "README.md", "level": 3, "text": "技术栈"}], "count": 2}, {"heading": "组件架构", "occurrences": [{"file": "ARCHITECTURE.md", "level": 2, "text": "组件架构"}, {"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "组件架构"}], "count": 2}, {"heading": "渲染优化", "occurrences": [{"file": "ARCHITECTURE.md", "level": 3, "text": "渲染优化"}, {"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "渲染优化"}], "count": 2}, {"heading": "开发环境", "occurrences": [{"file": "ARCHITECTURE.md", "level": 1, "text": "开发环境"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "开发环境"}], "count": 2}, {"heading": "目录结构", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "目录结构"}, {"file": "STRUCTURE_OVERVIEW.md", "level": 2, "text": "目录结构"}], "count": 2}, {"heading": "核心组件", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 2, "text": "核心组件"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "核心组件"}], "count": 2}, {"heading": "功能特性", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "功能特性"}], "count": 5}, {"heading": "props 接口", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "Props 接口"}], "count": 5}, {"heading": "使用示例", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}, {"file": "COMPONENTS_GUIDE.md", "level": 4, "text": "使用示例"}], "count": 7}, {"heading": "typescript 规范", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "TypeScript 规范"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 3, "text": "TypeScript 规范"}], "count": 2}, {"heading": "单元测试", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "单元测试"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "单元测试"}], "count": 2}, {"heading": "集成测试", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "集成测试"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "集成测试"}], "count": 2}, {"heading": "常见问题", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "常见问题"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "常见问题"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "常见问题"}, {"file": "README.md", "level": 3, "text": "常见问题"}], "count": 4}, {"heading": "调试技巧", "occurrences": [{"file": "COMPONENTS_GUIDE.md", "level": 3, "text": "调试技巧"}, {"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "调试技巧"}], "count": 2}, {"heading": "快速开始", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "快速开始"}, {"file": "MQTT_GUIDE.md", "level": 2, "text": "快速开始"}, {"file": "README.md", "level": 2, "text": "快速开始"}], "count": 3}, {"heading": "克隆项目", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "克隆项目"}, {"file": "README.md", "level": 1, "text": "克隆项目"}], "count": 2}, {"heading": "安装依赖", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "安装依赖"}, {"file": "README.md", "level": 1, "text": "安装依赖"}], "count": 2}, {"heading": "启动开发服务器", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "启动开发服务器"}, {"file": "README.md", "level": 1, "text": "启动开发服务器"}], "count": 2}, {"heading": "构建生产版本", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 1, "text": "构建生产版本"}, {"file": "README.md", "level": 1, "text": "构建生产版本"}], "count": 2}, {"heading": "项目结构", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "项目结构"}, {"file": "README.md", "level": 2, "text": "项目结构"}], "count": 2}, {"heading": "开发流程", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 2, "text": "开发流程"}, {"file": "README.md", "level": 3, "text": "开发流程"}], "count": 2}, {"heading": "api 请求优化", "occurrences": [{"file": "DEVELOPMENT_GUIDE.md", "level": 4, "text": "API 请求优化"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 2, "text": "API 请求优化"}], "count": 2}, {"heading": "改进建议", "occurrences": [{"file": "DOCUMENTATION_MAINTENANCE_GUIDE.md", "level": 2, "text": "改进建议"}, {"file": "MQTT_GUIDE.md", "level": 3, "text": "改进建议"}], "count": 2}, {"heading": "内存管理", "occurrences": [{"file": "MQTT_GUIDE.md", "level": 2, "text": "内存管理"}, {"file": "PERFORMANCE_OPTIMIZATION.md", "level": 3, "text": "内存管理"}], "count": 2}], "duplicateCodeBlocks": [{"hash": "174626b3", "occurrences": [{"file": "API_GUIDE.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}, {"file": "MQTT_GUIDE.md", "language": "unknown", "code": "{domain}/{context}/{target}/{action}\n..."}], "count": 2}]}