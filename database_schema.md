# NocoDB 数据表设计

除特殊说明外，各表均包含一个由NocoDB自动管理的、名为`Id`的自增数字主键

环节表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| session_id | Single select | 环节的唯一数字ID，用于程序内部关联  | qa_mandatory_01 |
| session_name | Single line text | 环节的显示名称，例如“有问必答” | 有问必答 |
| display_order | Number | 环节在大屏和中控台的显示顺序 | 1 |
| nav_type | Single select | 导航类型分组，例如“规则介绍”、“必答环节”、“抢答环节”等 | 规则介绍 |
| session_icon | Single select | 用于前端展示的环节图标 | 图像 |
| content_type | Single select | 环节内容的主要类型，如“规则”、“题目”等 | 规则 |
| initial_stage | Single select | 进入该环节时的初始阶段或题型 | 通用题 |
| session_config | JSON | 用于存储环节的特定配置，如PK各阶段时长等 | `{
"phases": [
{ "id": "opening_statement", "name": "观点陈述", "duration": 90, "reminder_at": 15 },
{ "id": "cross_examination", "name": "双方质询", "duration": 90, "reminder_at": 15 },
{ "id": "free_debate", "name": "自由辩论", "duration": 45, "reminder_at": 10 },
{ "id": "closing_statement", "name": "总结陈词", "duration": 120, "reminder_at": 30 }
]
}` |

赛事素材表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| info_id | Number | 信息的唯一数字ID | 1 |
| title | Single line text | 信息的标题 | 有问必答 |
| content | Long text | 详细的文本内容，如环节规则、介绍文字等 | 本环节共**8道**题目，每题分值**10分**，满分**80分**。题型包括**判断**、**单选**、**多选**等，具体以实际为准。 |
| attachment_url | Attachment | 相关的附件链接。API返回一个对象数组，每个对象包含文件的`title`, `mimetype`, `size`等元数据以及用于直接访问的`signedUrl`。 | `json [ { "title": "选手头像.jpg", "mimetype": "image/jpeg", "size": 125000, "signedUrl": "(...一个用于访问jpg文件的长效签名URL...)" } ]` |
| info_type | Single select | 该条信息的分类，如“规则介绍”、“奖项显示”等 | 规则介绍 |

题目表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| question_id | Number | 题目的唯一数字ID | 126 |
| question_number | Number | 题目在当前环节或题包内的顺序号 | 1 |
| question_type | Single select | 题目的类型，例如“单选题”、“多选题”、“判断题”  | 多选题 |
| prompt | Long text | 题目的题干内容 | 每年（ ）为全民国家安全教育日。 |
| options | JSON | **题目的所有选项，通常包含A, B, C, D等** | [{"key": "A", "text": "12月4日"}, {"key": "B", "text": "4月15日"}] |
| correct_answer | Single line text | 题目的正确答案 | B |
| points | Number | 本题目的分值 | 10 |
| explanation | Long text | 对题目答案的详细解析，没有则为空 | 因为国家规定 |
| attachment_url | Attachment | 相关的附件链接。API返回一个对象数组，每个对象包含文件的`title`, `mimetype`, `size`等元数据以及用于直接访问的`signedUrl`。 | `json [ { "title": "题目背景音乐.mp3", "mimetype": "audio/mpeg", "size": 850000, "signedUrl": "(...一个用于访问mp3文件的长效签名URL...)" } ]` |
| session_id | Single select | 该题目所属的环节ID，关联到环节表 | qa_mandatory_01 |
| question_pack_id | Single select | 题包编号，用于区分正式题包和备用题包 | 1 |
| stage | Single select | 有问必答环节所属的答题阶段，如“必答题”、“挑战题” | 必答题 |

选手表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| user_id | Number | 选手的唯一数字ID | 1001 |
| user_name | Single line text | 选手或队伍的名称 | 复旦大学 |
| avatar_url | Attachment | Attachment相关的附件链接。API返回一个对象数组，每个对象包含文件的`title`, `mimetype`, `size`等元数据以及用于直接访问的`signedUrl`。 | `json [ { "title": "赛事Logo.png", "mimetype": "image/png", "size": 78000, "signedUrl": "(...一个用于访问png文件的长效签名URL...)" } ]` |
| revival_chances | Number | 选手拥有的复活机会次数 | 1 |

答题记录表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID 题目的唯一数字ID，nocodb 初始自动创建字段 | 1 |
| submitted_answer | Long text | 选手提交的答案内容 | ABC |
| is_correct | Number | 选手提交的答案是否正确 | 1 |
| score | DECIMAL | 记录本次作答的得分
注意：此 `DECIMAL` 类型在通过API交互时可能以`string`形式返回以保证精度，具体请参考API集成指南 | 10 |
| user_id | Number | 关联到选手表的记录，指向提交答案的选手 | 1001 |
| question_id | Number | 回答对应题目的唯一数字ID | 189 |
| question_number | Number | 回答题目在当前环节或题包内的顺序号 | 1 |
| grading_type | Single select | 判分类型，如“自动判分”、“人工判分” | 自动判分 |
| session_id | Single select | 提交答案时所属的环节ID | qa_mandatory_01 |
| status | Single select | 记录该条作答的有效状态。**新增中间状态 `作废中` 和 `题包作废中`，用于处理分布式数据一致性问题。** 可选值：`有效`、`作废`、`题包作废`、**`作废中`**、**`题包作废中`** | `有效` / `作废中` |
| submission_type | Single select | 标记记录性质，如“常规提交”、“主观放弃”、“记分补录” | 记分补录 |
| audit_details | JSON | 存储仲裁原因、操作人等上下文信息 | `{
"reason": "XXX号选手设备于20:15:32发生死机，当场向裁判确认答案为B。",
"arbiter_id": "chief_judge_01",
"arbitration_timestamp": "2025-07-25T20:18:00Z"
}` |

评委表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| judge_id | Number | 评委的唯一数字ID | 2001 |
| judge_name | Single line text | 评委的名称 | 王评委 |

评委评分表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 评分的唯一ID 题目的唯一数字ID，nocodb 初始自动创建字段 | 1 |
| judge_score | DECIMAL | 评委给出的具体分数，可包含小数 
注意：此 `DECIMAL` 类型在通过API交互时可能以`string`形式返回以保证精度，具体请参考API集成指南 | 88.25 |
| user_id | Number | 被评分选手的ID | 1001 |
| judge_id | Number | 打分评委的ID | 2001 |
| session_id | Single select | 评分时所属的环节ID | qa_mandatory_01 |

投票记录表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 评分的唯一ID 题目的唯一数字ID，nocodb 初始自动创建字段 | 1 |
| voter_id | Single line text | 投票人ID (可以是评委ID或设备ID) | `judge_2001` / `device_vwxyz` |
| voter_type | Single select | 投票人类型 | 专家/大众 |
| user_id | Number | 支持的选手的ID | 1001 |
| points | DECIMAL | 本次投票对应的分数 | `5.0` (专家), `1.0` (大众) |

日志记录表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID 题目的唯一数字ID，nocodb 初始自动创建字段 | 12138 |
| topic | Long text | 接收到的MQTT消息的完整Topic |  |
| payload | JSON | 接收到的消息的原始Payload，以JSON格式存储 |  |
| status | Single select | 消息处理状态，如"success", "error", "warning" |  |

最终得分表

| 字段名(Column Name) | 数据类型(Data Type) | 说明(Desctiption) | 示例(Example) |
| --- | --- | --- | --- |
| Id | Number | 记录的唯一ID，由NocoDB自动创建和管理 |  |
| user_id | Number | 选手的唯一ID | 1001 |
| user_name | Single line text | 手的名称，此处为冗余字段以方便查询 | 复旦大学 |
| final_rank | Number | 选手的最终排名 | 1 |
| final_score | Number | 选手的最终总分 | 600 |
| score_details | JSON | 包含各环节得分、扣分等详细信息的JSON对象 | `{"sessions":[{"session_id":"qa_mandatory_01","session_name":"有问必答","score":100,"correct_answers":10,"total_answers":10},{"session_id":"sprint_180","session_name":"争分夺秒","score":158,"time_used":"172s","correct_answers":28,"total_answers":30},{"session_id":"judge_final_pk","session_name":"终极PK评分","score":88.5,"judge_score":[90,88,87.5]}],"adjustments":[{"amount":-10,"reason":"技术犯规，扣除10分","operator":"admin_user"}]}` |