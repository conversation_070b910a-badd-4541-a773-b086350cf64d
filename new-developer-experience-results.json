{"timestamp": "2025-07-16T08:51:31.914Z", "summary": {"totalTests": 28, "passedTests": 27, "failedTests": 1, "score": 54, "maxScore": 55, "successRate": 98}, "testResults": [{"name": "Documentation README exists", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has project description", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has documentation structure", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has quick navigation", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Project README exists", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has installation instructions", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has run instructions", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Development guide exists", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.907Z"}, {"name": "Has environment requirements", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.908Z"}, {"name": "Has development tools configuration", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.908Z"}, {"name": "Has troubleshooting section", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.908Z"}, {"name": "Architecture guide exists", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.908Z"}, {"name": "Has system overview", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Has technology stack", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Has directory structure", "passed": false, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Structure overview exists", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "API guide exists", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Has API usage examples", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Has error handling guidance", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Component guide exists", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.909Z"}, {"name": "Has component examples", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Has styling guidance", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Troubleshooting guide exists", "passed": true, "weight": 3, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Has common issues section", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Has debugging guidance", "passed": true, "weight": 2, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Has performance troubleshooting", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.910Z"}, {"name": "Good internal linking ratio", "passed": true, "weight": 2, "details": "Internal links: 66/146 (45%)", "timestamp": "2025-07-16T08:51:31.913Z"}, {"name": "Has documentation templates", "passed": true, "weight": 1, "details": "", "timestamp": "2025-07-16T08:51:31.913Z"}], "recommendations": [], "experienceLevel": "优秀 (Excellent)"}